import { Skeleton } from '@/components/ui/skeleton'

export default function Loading() {
  return (
    <div className="p-8 space-y-6">
      <div className="flex items-center space-x-2">
        <Skeleton className="h-4 w-12" />
        <Skeleton className="h-4 w-8" />
        <Skeleton className="h-4 w-20" />
      </div>

      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-[500px]" />
        </div>
        <Skeleton className="h-10 w-40" />
      </div>

      <div className="flex space-x-4">
        {['Step 1', 'Step 2', 'Step 3', 'Step 3', 'Step 4'].map(
          (step, index) => (
            <div key={index} className="space-y-2 text-center">
              <Skeleton className="h-6 w-32" />
            </div>
          )
        )}
      </div>

      <div className="space-y-4">
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-4 w-96" />
        <div className="space-y-2">
          {Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} className="h-12 w-full" />
          ))}
        </div>
      </div>
    </div>
  )
}
