import { z } from 'zod'
import { UnauthorizedError, DatabaseError } from './exceptions'

export const errorHandler = (error: any) => {
  if (error instanceof z.ZodError) {
    return new Response(JSON.stringify(error.issues), { status: 422 })
  }

  if (error instanceof UnauthorizedError) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401
    })
  }

  if (error instanceof DatabaseError) {
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500
    })
  }
  return new Response(null, { status: 500 })
}
