'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'
import { useRouter } from 'next/navigation'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { editPrompt } from '@/lib/actions/prompt'
import { Prompt } from '@prisma/client'
import { Label } from '@/components/ui/label'

export function EditPromptDialog({
  id,
  prompt,
  open,
  setOpen
}: {
  id: string
  prompt: Prompt
  open: boolean
  setOpen: (open: boolean) => void
}) {
  const [editedPrompt, setEditedPrompt] = useState<Partial<Prompt>>(prompt)
  const [isLoading, setIsLoading] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)

  const handleChange = (field: keyof Prompt, value: string) => {
    setEditedPrompt((prev) => ({ ...prev, [field]: value }))
  }

  const router = useRouter()

  const variables =
    editedPrompt.variables &&
    Array.isArray(editedPrompt.variables) &&
    editedPrompt.variables.filter(
      (variable) =>
        variable !== null && variable !== undefined && variable !== ''
    ).length > 0 // Filter out null, undefined, and empty string values
      ? editedPrompt.variables.filter((variable) => variable !== '')
      : null

  // Add a validation function to check if all variables appear in the prompt with proper handlebars
  const validateVariablesInPrompt = (): boolean => {
    if (!variables || variables.length === 0) {
      return true // No variables to validate
    }

    const promptText = editedPrompt.prompt || ''

    // Check if each variable appears in the prompt with proper handlebars syntax {{variable}}
    const missingVariables = variables.filter((variable) => {
      const pattern = new RegExp(`{{\\s*${variable}\\s*}}`, 'g')
      return !pattern.test(promptText)
    })

    if (missingVariables.length > 0) {
      setValidationError(
        `The following variables are missing or improperly formatted in the prompt: ${missingVariables
          .map((v) => `{{${v}}}`)
          .join(', ')}`
      )
      return false
    }

    setValidationError(null)
    return true
  }

  const handleEditPrompt = async ({
    id,
    editedPrompt
  }: {
    id: string
    editedPrompt: Partial<Prompt>
  }) => {
    // Validate variables in prompt before updating
    if (!validateVariablesInPrompt()) {
      toast({
        title: 'Validation Error',
        description: validationError,
        variant: 'destructive'
      })
      return
    }

    setIsLoading(true)
    const updateData = await editPrompt({ id, editedPrompt })
    if (updateData) {
      toast({
        title: 'Prompt updated'
      })
    } else {
      toast({
        title: 'Failed to update prompt',
        variant: 'destructive'
      })
    }

    router.refresh()
    setIsLoading(false)
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-h-[80vh] max-w-[90%] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Prompt</DialogTitle>
          <DialogDescription>
            {variables && variables.length > 0
              ? `Required variables: ${variables
                  .map((variable) => `{{${variable}}}`)
                  .join(', ')}`
              : 'No required variables.'}
          </DialogDescription>
        </DialogHeader>
        {validationError && (
          <div className="bg-destructive/15 text-destructive p-3 rounded-md text-sm">
            {validationError}
          </div>
        )}
        <div className="flex flex-col gap-3">
          <div>
            <Label>Source</Label>
            <Input
              type="text"
              className="p-2"
              value={editedPrompt.role || ''}
              readOnly
              onChange={(e) => handleChange('source', e.target.value)}
            />
          </div>
          <div>
            <Label>Prompt</Label>
            <Textarea
              className="p-2 resize-none"
              style={{ height: '300px', overflowY: 'scroll' }}
              value={editedPrompt.prompt || ''}
              onChange={(e) => handleChange('prompt', e.target.value)}
            />
          </div>
          <div>
            <Label>Expected Output</Label>
            <Textarea
              className="p-2 resize-none"
              style={{ height: '300px', overflowY: 'scroll' }}
              value={editedPrompt.expectedOutput || ''}
              onChange={(e) => handleChange('expectedOutput', e.target.value)}
            />
          </div>
        </div>
        <DialogFooter className="sm:justify-start">
          <Button
            onClick={() => handleEditPrompt({ id, editedPrompt })}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save'}
          </Button>
          <Button onClick={() => setOpen(false)} variant="secondary">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
