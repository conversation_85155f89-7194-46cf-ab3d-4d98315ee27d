@media print {
    .noprint {
        visibility: hidden;
    }
}

#bg-text {
    color: lightgrey;
    position: fixed;
    transform: rotate(300deg);
    -webkit-transform: rotate(300deg);
    top: 30%;

    background-position: center;
    width: 80%;

}

body {
    padding: 10px;
    width: 80%;
    margin-left: 10%;
}

.link {
    font-size: 10px;
    font-style: normal;
    line-height: 25px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #666666;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: right center;
    letter-spacing: 1px;
    text-align: center;
}

.midbold {

    font-size: 10px;
    font-style: normal;
    line-height: 16px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #CC0000;
    text-align: justify;
    padding-right: 4px;
    padding-left: 4px;
}

.midboldover {
    font-size: 10px;
    font-style: normal;
    line-height: 16px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #E60000;
    text-align: justify;
    padding-right: 4px;
    padding-left: 4px;
    text-decoration: underline;
    cursor: hand;
}

.text1Copy {
    font-size: 13px;
    font-style: normal;
    line-height: 16px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #006699;
    text-align: justify;
    padding-right: 2px;
    padding-left: 2px;
    word-spacing: normal;
    letter-spacing: normal;
}

.link1 {

    font-size: 8px;
    font-style: normal;
    line-height: 25px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #666666;
    letter-spacing: 1px;
    text-align: center;
}

.whiteline {
    background-attachment: scroll;
    background-repeat: repeat-y;
    background-position: right center;
}

.em {
    font-size: 14px;
    font-style: normal;
    line-height: 25px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #FFFFFF;
    letter-spacing: 1px;
    text-align: center;
}

.border {
    border: 1px solid #83AA01;
}

.bold {
    font-size: 16px;
    font-style: normal;
    line-height: 25px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    letter-spacing: 1px;
    padding-left: 6px;
}

.link_b {

    font-size: 10px;
    font-style: normal;
    line-height: 20px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #FFFFFF;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: right center;
    letter-spacing: 1px;
    text-align: center;
}

.link_b1 {


    font-size: 10px;
    font-style: normal;
    line-height: 20px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #FFFFFF;
    letter-spacing: 1px;
    text-align: center;
}

.linkover {
    font-size: 16px;
    font-style: normal;
    line-height: 20px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: right center;
    letter-spacing: 1px;
    text-align: center;
    cursor: hand;
}

.link1over {
    font-size: 10px;
    font-style: normal;
    line-height: 25px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #000066;
    letter-spacing: 1px;
    text-align: center;
    cursor: hand;
}

.link_bover {
    font-size: 10px;
    font-style: normal;
    line-height: 20px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #FFFFFF;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: right center;
    letter-spacing: 1px;
    text-align: center;
    text-decoration: underline;
    cursor: hand;
}

.link_b1over {
    font-size: 10px;
    font-style: normal;
    line-height: 20px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #FFFFFF;
    letter-spacing: 1px;
    text-align: center;
    text-decoration: underline;
    cursor: hand;
}

.border1 {
    border-right: 1px solid #C5C5C5;
}

.mid {
    font-size: 11px;
    font-style: normal;
    line-height: 15px;
    font-weight: normal;
    font-variant: normal;
    color: #000066;
    text-align: justify;
    padding-right: 5px;
    padding-left: 5px;
}

.border2 {
    border-right-widh: 1px;
    border-left-widh: 1px;
    border-right-style: solid;
    border-left-style: solid;
    border-right-color: #969696;
    border-left-color: #969696;
}

.emover {
    font-size: 14px;
    font-style: normal;
    line-height: 25px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #FFFF99;
    letter-spacing: 1px;
    text-align: center;
    cursor: hand;
}

.vert_line {
    line-height: 30px;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: center bottom;
}

.link_l {
    font-size: 11px;
    font-style: normal;
    line-height: 25px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #FFFFFF;
    letter-spacing: 1px;
    padding-left: 10px;

}

.link_lover {
    font-size: 11px;
    font-style: normal;
    line-height: 25px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    color: #CCCCCC;
    letter-spacing: 1px;
    background-attachment: fixed;
    cursor: hand;
    padding-left: 10px;

}

.box {
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: center top;
}

.bold1 {
    font-style: normal;
    line-height: 25px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    letter-spacing: 1px;
    padding-left: 8px;
}

.mid1 {
    font-style: normal;
    line-height: 18px;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
}


.borderCopy {
    border-right: 1px solid #808080;
    border-left: 1px solid #808080;
}

.tt {

    font-size: 10px;
    font-style: normal;
    line-height: 18px;
    font-weight: normal;
    font-variant: normal;
    color: #000066;
    text-align: justify;
    padding-right: 5px;
    padding-left: 5px;
}

.linkoverCopy {

    font-style: normal;
    line-height: 18px;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: right center;
    letter-spacing: 1px;
    text-align: center;
    cursor: hand;
}

.linkoverCopy1 {

    font-style: normal;
    line-height: 18px;
    font-weight: bold;
    font-variant: normal;
    text-transform: none;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: right center;
    letter-spacing: 1px;
    text-align: center;
    cursor: hand;
}

.mid1Copy {

    font-style: normal;
    line-height: 18px;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
}

.disablelink {
    text-decoration: none;
    cursor: default;
    pointer-events: none;
    font-style: normal
}

p {
    margin: 0;
    padding: 10px 0;
}