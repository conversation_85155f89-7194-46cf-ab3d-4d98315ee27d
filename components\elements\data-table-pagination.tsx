'use client'

import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { formSearchParams } from '@/lib/utils'
import { Icons } from './icons'

interface DataTablePaginationProps<TData> {
  currentPage: number
  totalPage: number
  searchParams: { [key: string]: string | string[] }
}

export function DataTablePagination<TData>({
  currentPage,
  totalPage,
  searchParams
}: DataTablePaginationProps<TData>) {
  const router = useRouter()
  return (
    <div className="flex items-center justify-evenly px-2 py-4">
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
          Page {currentPage} of {totalPage}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => {
              router.push(formSearchParams(searchParams, [{ page: `${1}` }]))
            }}
            disabled={currentPage === 1}
          >
            <span className="sr-only">Go to first page</span>
            <Icons.chevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => {
              router.push(
                formSearchParams(searchParams, [{ page: `${currentPage - 1}` }])
              )
            }}
            disabled={currentPage === 1}
          >
            <span className="sr-only">Go to previous page</span>
            <Icons.chevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => {
              router.push(
                formSearchParams(searchParams, [{ page: `${currentPage + 1}` }])
              )
            }}
            disabled={currentPage === totalPage}
          >
            <span className="sr-only">Go to next page</span>
            <Icons.chevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => {
              router.push(
                formSearchParams(searchParams, [{ page: `${totalPage}` }])
              )
            }}
            disabled={currentPage === totalPage}
          >
            <span className="sr-only">Go to last page</span>
            <Icons.chevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
