import Link from 'next/link'
import { ShowCauseNotice } from '@prisma/client'
import { formatDate } from '@/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'

interface ShowCauseItemProps {
  showCauseOrder: Pick<ShowCauseNotice, 'id' | 'title' | 'createdAt'>
}

export function ShowCauseItem({ showCauseOrder }: ShowCauseItemProps) {
  return (
    <div className="flex items-center justify-between p-4">
      <div className="grid gap-1">
        <Link
          href={`/dashboard/show-cause-notice/${showCauseOrder.id}`}
          className="font-semibold hover:underline"
        >
          {showCauseOrder.title}
        </Link>
        <div>
          <p className="text-sm text-muted-foreground">
            {formatDate(showCauseOrder.createdAt?.toDateString())}
          </p>
        </div>
      </div>
    </div>
  )
}

ShowCauseItem.Skeleton = function ShowCauseItemSkeleton() {
  return (
    <div className="p-4">
      <div className="space-y-3">
        <Skeleton className="h-5 w-2/5" />
        <Skeleton className="h-4 w-4/5" />
      </div>
    </div>
  )
}
