import { User, Team } from '@prisma/client'
import { cn, formatDate } from '@/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'
import Link from 'next/link'
import { buttonVariants } from '../../ui/button'
import { DeleteUserButton } from './delete-user-button'

interface UserExtended extends User {
  index: number
  team: Team | null
}

interface UserItemProps {
  user: UserExtended
}

export function UserItem({ user }: UserItemProps) {
  return (
    <div className="flex items-center justify-between p-4">
      <div className="flex gap-3 items-center">
        <span className="text-sm text-muted-foreground">{user.index}. </span>
        <div>
          <p className="font-semibold">{user.name || '_unknown'}</p>
          <p className="text-sm text-muted-foreground">
            {user.email || '_unknown'}
          </p>
          <p className="text-sm text-muted-foreground">
            <Link
              className={'underline cursor-pointer'}
              href={`/admin/teams?teamId=${user.teamId}`}
            >
              Team: {user.team?.name || '_unknown'}
            </Link>
          </p>
          <p className="text-sm text-muted-foreground">
            Started on {formatDate(user.createdAt?.toDateString())}
          </p>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <Link
          className={cn(
            buttonVariants({
              variant: 'outline',
              size: 'sm'
            })
          )}
          href={`/admin/users/${user.id}/masquerade/research`}
          target="_blank"
        >
          Test Research
        </Link>
        <Link
          className={cn(
            buttonVariants({
              variant: 'outline',
              size: 'sm'
            })
          )}
          href={`/admin/users/${user.id}/masquerade/research-case`}
          target="_blank"
        >
          Test Research Case
        </Link>
        <Link
          className={cn(
            buttonVariants({
              variant: 'outline',
              size: 'sm'
            })
          )}
          href={`/admin/users/${user.id}/masquerade/research-private`}
          target="_blank"
        >
          Test Research Private
        </Link>
        <Link
          className={cn(
            buttonVariants({
              variant: 'outline',
              size: 'sm'
            })
          )}
          href={`/admin/users/${user.id}/masquerade/case`}
          target="_blank"
        >
          Test Case Features
        </Link>
        <DeleteUserButton
          userId={user.id}
          userName={user.name || '_unknown'}
          userEmail={user.email || '_unknown'}
        />
      </div>
    </div>
  )
}

UserItem.Skeleton = function UserItemSkeleton() {
  return (
    <div className="p-4">
      <div className="space-y-3">
        <Skeleton className="h-5 w-2/5" />
        <Skeleton className="h-4 w-4/5" />
      </div>
    </div>
  )
}
