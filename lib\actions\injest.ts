'use server'

import { <PERSON><PERSON><PERSON> } from 'buffer'
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter'
import { AuthUser } from 'next-auth'
import { db } from '../db'
import { storePrivateTextEmbedding } from '../pg-db'
import {
  extractFileContentAndHtmlWithMammoth,
  fileToBuffer
} from '../file-handler'
import { Region } from '@prisma/client'
import { AWS_BUCKET, s3UploadFile } from '../services/s3-service'

export async function injestDocument({
  file,
  user
}: {
  file: File
  user: AuthUser
}) {
  try {
    const buffer = await fileToBuffer(file)

    const create = await injest({
      ...buffer,
      teamId: user.teamId!,
      region: user.region
    })

    return create
  } catch (error) {
    console.log(error)
    console.log('Error in injestDocument')
  }
}

export async function injestDocumentBuffer({
  file,
  user
}: {
  file: {
    buffer: A<PERSON><PERSON><PERSON>uffer
    type: string
    name: string
  }
  user: AuthUser
}) {
  try {
    const buffer = Buffer.from(file.buffer)
    const create = await injest({
      buffer: buffer,
      type: file.type,
      name: file.name,
      teamId: user.teamId!,
      region: user.region
    })

    return create
  } catch (error) {
    console.log(error)
    console.log('Error in injestDocumentBuffer')
  }
}

export async function injest({
  buffer,
  type,
  name,
  teamId,
  region
}: {
  buffer: Buffer
  type: string
  name: string
  teamId: string
  region: Region
}) {
  try {
    const ref =
      name
        .toLowerCase()
        .replace(/[\s\.!@#$%^&*()+\-=\[\]{};':"\\|,<>\/?]+/g, '_') +
      '_' +
      Date.now()

    // Upload file to S3
    const s3Key = `${teamId}/${ref}`
    const [s3Url, { htmlContent, textContent }] = await Promise.all([
      s3UploadFile({
        buffer,
        key: s3Key,
        fileType: type,
        bucket: AWS_BUCKET.secure
      }),

      extractFileContentAndHtmlWithMammoth({
        file: {
          buffer,
          type,
          name
        }
      })
    ])

    console.time('Creating document record in the database')
    const dataSource = teamId
    const create = await db.documentRecords.create({
      data: {
        source: dataSource,
        ref: ref,
        title: name,
        html: htmlContent,
        content: textContent,
        region: region,
        meta: '',
        url: s3Url // Store the S3 URL in the url column
      }
    })

    await db.documentResource.create({
      data: {
        documentRecordId: create.id,
        s3Key,
        fileUrl: s3Url
      }
    })

    console.timeEnd('Creating document record in the database')

    console.time('Splitting document into chunks')
    const splitter = RecursiveCharacterTextSplitter.fromLanguage('markdown', {
      chunkSize: 1000,
      chunkOverlap: 200
    })
    const splitDocuments = await splitter.createDocuments([textContent])
    console.timeEnd('Splitting document into chunks')

    console.time('Storing document embeddings')
    const batchSize = 20
    for (let i = 0; i < splitDocuments.length; i += batchSize) {
      const batch = splitDocuments.slice(i, i + batchSize)
      const promises = batch.map(async (doc) => {
        doc.metadata = {
          ...doc.metadata,
          documentRecordsId: create.id,
          documentRecordsSource: create.source,
          refId: create.ref
        }
        return storePrivateTextEmbedding({
          inputText: doc.pageContent,
          namespace: create.source,
          docId: create.id,
          metadata: doc.metadata
        })
      })
      await Promise.all(promises)
    }
    console.timeEnd('Storing document embeddings')

    return create
  } catch (error) {
    console.log(error)
    console.log('Error in injest')
  }
}
