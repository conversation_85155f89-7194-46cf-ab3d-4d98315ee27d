import { notFound, redirect } from 'next/navigation'

import { db } from '@/lib/db'
import { authOptions } from '@/lib/auth'
import { getCurrentUser, getFeatureUsageStats } from '@/lib/session'
import { features } from '@/config/dashboard'
import { Card } from '@/components/ui/card'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { CopyHrefButton } from '@/components/elements/buttons/button-copy-href'
import { ChatWindow } from '@/components/elements/chat/chat-window'
import { CreditType, ResearchType } from '@prisma/client'

import type { ResearchStoreContent } from '@/types'
import Link from 'next/link'
import { buttonVariants } from '@/components/ui/button'
import { cn } from '@/lib/utils'

export const metadata = features['researchCase']

interface ResearchPageProps {
  params: { researchId: string }
}

export default async function ResearchPage({ params }: ResearchPageProps) {
  const user = await getCurrentUser()

  if (!user) {
    redirect(authOptions?.pages?.signIn || '/login')
  }

  const usageStats = await getFeatureUsageStats({
    feature: CreditType.research
  })

  const research = await db.researchStore.findFirst({
    where: {
      id: params.researchId
    }
  })

  if (!research) {
    return notFound()
  }

  const researchPropsRaw = research.content as unknown as ResearchStoreContent
  const researchProps: ResearchStoreContent = {
    model: researchPropsRaw.model,
    sources: researchPropsRaw.sources,
    court: researchPropsRaw.court || [],
    year: researchPropsRaw.year || [],
    sourcesForMessages: researchPropsRaw.sourcesForMessages,
    sourceLabels: researchPropsRaw.sourceLabels,
    messages: researchPropsRaw.messages
  }
  researchProps.researchId = research.id

  return (
    <DashboardShell>
      <DashboardHeader heading={metadata.title} text={metadata.description}>
        <div className="flex items-center space-x-4">
          <Link
            href="/dashboard/research"
            className={cn(buttonVariants(), 'w-max')}
          >
            New Research
          </Link>
          <CopyHrefButton />
        </div>
      </DashboardHeader>
      <div className="grid gap-10">
        <Card>
          <ChatWindow
            user={user}
            stats={usageStats}
            researchType={ResearchType.case}
            researchProps={researchProps}
            showIngestForm={true}
            placeholder="Ask a question..."
            showFilters={true}
            namespace={research.source}
          />
        </Card>
      </div>
    </DashboardShell>
  )
}
