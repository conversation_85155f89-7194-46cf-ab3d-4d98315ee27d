import { CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'

export const BNSSInfoCard = () => {
  return (
    <section className="lg:p-8">
      <CardHeader>
        <CardTitle>New Criminal Laws Research Tool</CardTitle>
        <CardDescription>
          Dive into India&apos;s legal transformations with our New Criminal
          Laws Research Tool. Designed for legal professionals, academics, and
          enthusiasts, this platform helps you navigate and understand the
          intricacies of the Bharatiya Nyaya Sanhita, Bharatiya Nagarik Suraksha
          Sanhita, and Bharatiya Sakshya Adhiniyam.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ol className="list-decimal list-inside mb-4">
          <li className="mb-2">
            Enter your specific legal question or topic related to the new laws
            in the text box provided.
          </li>
          <li className="mb-2">
            Press the <strong>&quot;Submit&quot;</strong> button to process your
            query.
          </li>
          <li className="mb-2">
            Analyze the detailed response, complete with citations and reference
            documents where applicable.
          </li>
          <li>
            Adjust and resubmit your query as needed to refine your results and
            deepen your understanding.
          </li>
        </ol>

        {/* <p className="italic">
          Privacy Notice: Your privacy is paramount. All queries are securely
          processed and no personal data is retained after your session.
        </p> */}
      </CardContent>
    </section>
  )
}
