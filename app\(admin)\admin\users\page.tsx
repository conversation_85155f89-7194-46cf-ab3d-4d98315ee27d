import { db } from '@/lib/db'
import { EmptyPlaceholder } from '@/components/elements/custom-components/empty-placeholder'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { DataTablePagination } from '@/components/elements/data-table-pagination'
import {
  FilterSearchInput,
  FilterSearchInputProps
} from '@/components/elements/custom-components/filter-search-input'
import { UserItem } from '@/components/elements/user/user-item'

export const metadata = {
  title: 'Users List'
}

export default async function UsersListPage({
  searchParams
}: {
  searchParams: { [key: string]: string | string[] }
}) {
  let pageLimiter = searchParams.limit ? Number(searchParams.limit) : 20
  const page = searchParams.page ? Number(searchParams.page) : 1
  const search =
    searchParams.search && searchParams.search.length > 2
      ? String(searchParams.search)
      : undefined

  const region = searchParams.region ? String(searchParams.region) : undefined

  let where: any = search
    ? {
        OR: [
          {
            name: {
              contains: search
            }
          },
          {
            email: {
              contains: search
            }
          },
          {
            team: {
              name: {
                contains: search
              }
            }
          }
        ]
      }
    : {}

  if (region) {
    where = {
      ...where,
      region: region
    }
  }

  const users = await db.user.findMany({
    where,
    include: {
      team: true
    },
    orderBy: {
      createdAt: 'desc'
    },
    skip: (page - 1) * pageLimiter,
    take: pageLimiter
  })

  const totalTeams = await db.user.count({
    where
  })
  const totalPage = Math.ceil(totalTeams / pageLimiter)

  const startIndex = totalTeams - (page - 1) * pageLimiter

  const usersWithIndex = users?.map((user, index) => ({
    index: startIndex - index,
    ...user
  }))

  const searchFields: FilterSearchInputProps[] = [
    {
      param: 'search',
      placeholder: 'Search by name, email, or team name',
      value: searchParams.search ? String(searchParams.search) : undefined
    }
  ]

  const regionSelect: FilterSearchInputProps[] = [
    {
      param: 'region',
      placeholder: 'Select region',
      options: [
        { value: 'IN', label: 'India' },
        { value: 'US', label: 'United States' }
      ]
    }
  ]

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Users"
        text="List of active users on SmartCounsel"
      >
        <FilterSearchInput
          textinput={searchFields}
          selectinput={regionSelect}
        />
      </DashboardHeader>
      <div>
        {usersWithIndex?.length ? (
          <div className="divide-y divide-border rounded-md border bg-white dark:bg-slate-950">
            {usersWithIndex.map((user) => (
              <UserItem key={user.id} user={user} />
            ))}
            <DataTablePagination
              currentPage={page}
              totalPage={totalPage}
              searchParams={searchParams}
            />
          </div>
        ) : (
          <EmptyPlaceholder>
            <EmptyPlaceholder.Icon name="post" />
            <EmptyPlaceholder.Title>
              No teams joined yet.
            </EmptyPlaceholder.Title>
            <EmptyPlaceholder.Description>
              Create a new team and invite members to join.
            </EmptyPlaceholder.Description>
          </EmptyPlaceholder>
        )}
      </div>
    </DashboardShell>
  )
}
