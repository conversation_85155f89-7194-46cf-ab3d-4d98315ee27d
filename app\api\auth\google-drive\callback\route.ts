import { NextRequest, NextResponse } from 'next/server'
import { generateGoogleDriveToken } from '@/lib/services/google-drive-service'
import { getCurrentUserResponse } from '@/lib/session'
import { UnauthorizedError } from '@/lib/exceptions'
import { env } from 'process'

export async function GET(req: NextRequest) {
  const searchParams = new URLSearchParams(req.nextUrl.searchParams)
  const code = searchParams.get('code')
  try {
    if (!code) {
      throw new Error('No code provided')
    }

    const user = await getCurrentUserResponse()
    if (!user.id) {
      throw new UnauthorizedError()
    }

    await generateGoogleDriveToken({ code, userId: user.id })

    return NextResponse.redirect(
      env.NEXTAUTH_URL + '/dashboard/research-private/documents'
    )
  } catch (error: any) {
    console.error(error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}
