import { useCallback } from 'react'
import { toast } from '@/components/ui/use-toast'
import type { QueryClarityCheckResponse } from '@/types'
import { ResearchType } from '@prisma/client'

interface UseQueryClarificationProps {
  researchType: ResearchType
  input: string
  setInput: (val: string) => void
  chatEndpointIsLoading: boolean
  setChatEndpointIsLoading: (val: boolean) => void
  messages: any[]
  sources: string[]
  court: string[] | undefined
  year: string[] | undefined
  namespace?: string
  setQuestions: (val: any[]) => void
  setOpenQueryClarification: (val: boolean) => void
}

/**
 * Encapsulates logic to clarify a user’s question by hitting the /api/gpt/assess endpoint.
 */
export function useQueryClarification({
  researchType,
  input,
  setInput,
  chatEndpointIsLoading,
  setChatEndpointIsLoading,
  messages,
  sources,
  court,
  year,
  namespace,
  setQuestions,
  setOpenQueryClarification
}: UseQueryClarificationProps) {
  const handleQueryClarification = useCallback(async () => {
    if (!input) {
      return toast({
        title: 'Please enter a question',
        description:
          'Enter a question you wish to research in one or more sentences.',
        variant: 'destructive'
      })
    }

    if (chatEndpointIsLoading) {
      return toast({
        title: 'Please wait',
        description: 'We are still processing your previous message.'
      })
    }

    setChatEndpointIsLoading(true)

    try {
      const assessment = await fetch('/api/gpt/assess', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          metadata: {
            sources,
            court,
            year
          },
          messages,
          researchType,
          question: input,
          namespace
        })
      })

      const data: QueryClarityCheckResponse | null = await assessment.json()

      if (data && data.relevance === 'yes') {
        // The API deems the query relevant; show the user possible rewrites
        setQuestions([
          {
            question: input,
            intent: 'general'
          },
          ...(data.rewritten_question_set ?? [])
        ])
        setOpenQueryClarification(true)
      } else {
        toast({
          title: 'Your question is not clear',
          description: 'Please try rephrasing your question.',
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'Something went wrong',
        description: 'Please try again.',
        variant: 'destructive'
      })
    }

    setChatEndpointIsLoading(false)
  }, [
    input,
    chatEndpointIsLoading,
    setChatEndpointIsLoading,
    messages,
    sources,
    court,
    year,
    namespace,
    setQuestions,
    setOpenQueryClarification
  ])

  return {
    handleQueryClarification
  }
}
