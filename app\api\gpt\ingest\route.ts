import { NextRequest, NextResponse } from 'next/server'
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter'
import { Pinecone } from '@pinecone-database/pinecone'
import { OpenAIEmbeddings } from '@langchain/openai'
import { PineconeStore } from '@langchain/pinecone'
import { UnauthorizedError } from '@/lib/exceptions'
import { env } from '@/env.mjs'
import { CaseData } from '@/types/document'

/**
 * This handler takes input text, splits it into chunks, and embeds those chunks
 * into a vector store for later retrieval. See the following docs for more information:
 *
 * https://js.langchain.com/docs/modules/data_connection/document_transformers/text_splitters/recursive_text_splitter
 * https://js.langchain.com/docs/modules/data_connection/vectorstores/integrations/supabase
 */

export async function POST(req: NextRequest) {
  if (env.NODE_ENV !== 'development') {
    throw new UnauthorizedError()
  }

  try {
    const body = await req.json()
    const {
      text,
      source,
      metadata
    }: {
      text: string
      source: string
      metadata: {
        documentRecordsId: string
        documentRecordsSource: string
        refId: string
      } & CaseData
    } = body

    const pinecone = new Pinecone()
    const pineconeIndex = pinecone.index(env.PINECONE_INDEX)

    const splitter = RecursiveCharacterTextSplitter.fromLanguage('markdown', {
      chunkSize: 1000,
      chunkOverlap: 200
    })

    const splitDocuments = await splitter.createDocuments([text])

    if (metadata) {
      splitDocuments.forEach((doc) => {
        for (const [key, value] of Object.entries(metadata)) {
          if (key !== 'parties' && key !== 'summary') {
            doc.metadata[key] = value
          }
        }
      })
    }

    await PineconeStore.fromDocuments(splitDocuments, new OpenAIEmbeddings(), {
      pineconeIndex,
      namespace: source
    })

    return NextResponse.json({ ok: true }, { status: 200 })
  } catch (e: any) {
    console.log(e)

    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}
