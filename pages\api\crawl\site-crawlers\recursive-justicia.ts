import type { NextApiRequest, NextApiResponse } from 'next'
import axios from 'axios'
import * as cheerio from 'cheerio'
import { db } from '@/lib/db'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const startTime = Date.now()
  let crawledLinksCount = 0
  let metrics = { startTime, endTime: 0, crawledLinksCount }

  try {
    let hasUnscrapedLinks = true

    while (hasUnscrapedLinks) {
      const unscrapedUrls = await db.$transaction(async (prisma) => {
        // Fetch and lock records in a single transaction
        const records = await prisma.linkRecords.findMany({
          where: {
            done: false,
            processing: false, // Only fetch records not already being processed
            source: { notIn: [''] }
          },
          take: 4
        })

        // If no more unscraped URLs are found, stop the process
        if (records.length === 0) {
          return []
        }

        // Set the `processing` flag to true for the selected records
        const ids = records.map((record) => record.id)
        await prisma.linkRecords.updateMany({
          where: { id: { in: ids } },
          data: { processing: true }
        })

        return records
      })

      if (unscrapedUrls.length === 0) {
        hasUnscrapedLinks = false
        break // No more unscraped URLs
      }

      let allUrlArray: string[] = []
      let allStore: any[] = []

      for (const record of unscrapedUrls) {
        const levelStartTime = Date.now()
        const subLinks = await fetchSubLinks(
          record.url,
          record.source,
          record.court,
          record.id
        ) // Use 'record.id' as parentId
        const uniqueLinks = Array.from(new Set(subLinks))

        console.log(`Found ${uniqueLinks.length} sublinks for ${record.url}`)

        //to process only final links
        var store: any[] = []
        // if (uniqueLinks.length == 0) {
        // const store = await fetchHtmlInBatches(uniqueLinks, 20, async ({ url, html }) => {
        //   return processCaseHtml({ url, html, source: record.source, court: record.court });
        // });
        // }

        allUrlArray = [...allUrlArray, ...uniqueLinks]
        allStore = [...allStore, ...store]
        crawledLinksCount += uniqueLinks.length
        metrics.crawledLinksCount = crawledLinksCount

        // Mark current URL as done after processing
        await db.linkRecords.update({
          where: { id: record.id },
          data: {
            done: true,
            duration: Date.now() - levelStartTime
          }
        })

        const randomDelay = Math.floor(Math.random() * 1000) + 2000
        await delay(randomDelay)
      }

      const endTime = Date.now()
      metrics.endTime = endTime

      console.log(`Scraping completed. Links crawled: ${crawledLinksCount}`)
      console.log(`Time taken: ${(endTime - startTime) / 1000}s`)

      res.status(200).json({ allUrlArray, allStore })
    }
  } catch (error: any) {
    console.log('Error encountered: ', metrics)
    console.log(error)

    const endTime = Date.now()
    metrics.endTime = endTime

    if (error.message.includes('403 Forbidden')) {
      res.status(403).json({ error: '403 Forbidden: Access denied' })
    } else {
      res.status(500).json({ error: 'Failed to fetch and parse the website.' })
    }
  }
}

function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

async function fetchSubLinks(
  url: string,
  source: string,
  court: string,
  parentId: number | null
): Promise<string[]> {
  const checkUrlSet: string[] = [url]
  const urlArray = await fetchHtmlInBatches(checkUrlSet, 20, fetchCaseUrls)
  const uniqueArray = Array.from(new Set(urlArray))

  for (const link of uniqueArray) {
    const existingRecord = await db.linkRecords.findFirst({
      where: { url: link }
    })

    if (!existingRecord) {
      await db.linkRecords.create({
        data: {
          url: link,
          source,
          court,
          parentId // Store parentId
        }
      })
    }
  }

  // Mark the base link as done after storing sublinks
  await db.linkRecords.updateMany({
    where: { url },
    data: {
      done: true,
      subLinksCount: uniqueArray.length,
      type: uniqueArray.length == 0 ? 'DATA' : 'LINKS'
    }
  })

  if (uniqueArray.length == 0) {
    const store = await fetchHtmlInBatches([url], 20, async ({ url, html }) => {
      return processCaseHtml({ url, html, source: source, court: court })
    })
  }

  return uniqueArray
}

async function processCaseHtml({
  url,
  html,
  source,
  court
}: {
  url: string
  html: string
  source: string
  court: string
}): Promise<string[]> {
  try {
    const regex = /^(?:.*?\/){4}(.*)$/
    const match = url.match(regex)
    const ref = match ? match[1] : ''

    const $ = cheerio.load(html)
    const primaryContent = $('.primary-content')
    const title = $('.heading-1').text().trim()
    const elements = primaryContent.find(
      '.case-details .flex-col.width-20.reset-width-below-tablet.item'
    )
    let date: Date | null = null

    elements.each((index, element) => {
      const text = $(element).text()
      if (text.includes('argued:')) {
        const span = $(element).find('span')
        const dateString = span.text()
        date = new Date(dateString)
        return false
      }
    })

    const htmlbody = primaryContent.html() || ''
    const content = primaryContent.text().trim()

    const validate = await db.documentRecords.findFirst({
      where: { source, ref }
    })

    if (!validate) {
      const store = await db.documentRecords.create({
        data: {
          source,
          ref,
          title,
          meta: JSON.stringify({ source, court }),
          date,
          url,
          content,
          html: htmlbody,
          region: 'US'
        }
      })

      // Mark this link as done after storing content
      // await db.linkRecords.updateMany({
      //   where: { url },
      //   data: { done: true },
      // });

      return [store.ref]
    } else {
      await db.documentRecords.update({
        where: { id: validate.id },
        data: {
          title,
          meta: JSON.stringify({ source, court }),
          date,
          content,
          html: htmlbody,
          region: 'US'
        }
      })

      // Mark as done even if it's an update
      // await db.linkRecords.updateMany({
      //   where: { url },
      //   data: { done: true },
      // });

      return [validate.ref]
    }
  } catch (error: any) {
    console.error(`Error processing case HTML for URL: ${url}`, error.message)
    return ['!FAILED!']
  }
}

async function fetchHtmlInBatches(
  urls: string[],
  batchSize: number,
  batchProcess: (props: { url: string; html: string }) => Promise<string[]>
): Promise<string[]> {
  const batches = Math.ceil(urls.length / batchSize)
  let results: string[] = []

  for (let i = 0; i < batches; i++) {
    const batchUrls = urls.slice(i * batchSize, (i + 1) * batchSize)
    const batchPromises = batchUrls.map(async (url) => {
      const result = await fetchAndProcessUrl(url, batchProcess)
      await delay(Math.floor(Math.random() * 1000) + 2000)
      return result
    })

    const batchResults = await Promise.all(batchPromises)
    results = results.concat(batchResults.flat())
  }

  return Array.from(new Set(results))
}

async function fetchAndProcessUrl(
  url: string,
  batchProcess: (payload: { url: string; html: string }) => Promise<string[]>
): Promise<string[]> {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent':
          // 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Mozilla/5.0 (iPad; CPU OS 18_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/130.0.6723.37 Mobile/15E148 Safari/604.1'
      }
    })

    if (response.status === 403) {
      throw new Error(`403 Forbidden: Access denied for ${url}`)
    }

    const payload = { url, html: response.data }
    return await batchProcess(payload)
  } catch (error: any) {
    console.error(`Failed to fetch ${url}:`, error.message)
    return []
  }
}

async function fetchCaseUrls({
  url,
  html
}: {
  url: string
  html: string
}): Promise<string[]> {
  const urlArray: string[] = []
  const $ = cheerio.load(html)

  $('.codes-listing a').each((_, element) => {
    const href = $(element).attr('href')
    if (href) {
      urlArray.push('https://law.justia.com' + href)
    }
  })

  return urlArray
}
