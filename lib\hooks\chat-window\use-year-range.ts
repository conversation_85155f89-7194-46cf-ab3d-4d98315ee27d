import { useState, useEffect } from 'react'
import type { ResearchStoreContent } from '@/types'

/**
 * Manages the startYear, endYear, and the derived 'year' array
 * based on the provided researchProps.
 */
export function useYearRange(researchProps: ResearchStoreContent) {
  const initialYear =
    researchProps.year && researchProps.year.length > 0
      ? researchProps.year[0]
      : null

  const initialEndYear =
    researchProps.year && researchProps.year.length > 1
      ? researchProps.year[researchProps.year.length - 1]
      : null

  const [startYear, setStartYear] = useState<string | null>(initialYear)
  const [endYear, setEndYear] = useState<string | null>(initialEndYear)
  const [year, setYear] = useState<ResearchStoreContent['year']>(
    researchProps.year || []
  )

  useEffect(() => {
    if (startYear && endYear) {
      const start = Number(startYear)
      const end = Number(endYear)

      if (start > end) {
        setEndYear(String(start))
      } else {
        const updatedYearRange = Array.from(
          { length: end - start + 1 },
          (_, i) => start + i
        ).map(String)
        setYear(updatedYearRange)
      }
    } else if (startYear) {
      setYear([startYear])
    } else if (endYear) {
      // If we only have an endYear, shift it by one so we have a range of at least 2 years
      setStartYear(String(Number(endYear) - 1))
      setYear([endYear])
    }
  }, [startYear, endYear])

  return {
    startYear,
    setStartYear,
    endYear,
    setEndYear,
    year,
    setYear
  }
}
