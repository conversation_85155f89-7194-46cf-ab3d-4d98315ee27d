// select all documents created since this month (skip federalcourts_ussupremecourt) and run cleaner.

import type { NextApiRequest, NextApiResponse } from 'next'
import * as cheerio from 'cheerio'
import { db } from '@/lib/db'
import { CaseData } from '@/types/document'
import { env } from '@/env.mjs'
import { GPTModel, TextDavinciResponse } from '@/types'
import { isJsonString } from '@/lib/utils'
import { DocumentRecords } from '@prisma/client'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (env.NODE_ENV !== 'development') {
      throw new Error('Unauthorized')
    }

    const CHUNK_SIZE = 20
    const BATCH_SIZE = 1000
    let lastId = 0
    console.log('here2')

    try {
      for (let j = 0; j < 200000; j += BATCH_SIZE) {
        const records = await db.documentRecords.findMany({
          // select: {
          //   id: true,
          //   ref: true,
          //   meta: true,
          //   content: true,
          //   summary: true
          // },
          where: {
            // id: {
            //   in: [75547]
            // },
            id: {
              gte: lastId
            },
            // source: 'alaska_supremecourt',
            // ref: 'MTQ5NTM1',
            // source: {
            //   in: [
            // 'justicia'
            // 'courtlistener',
            // 'taxindiaonline',
            // 'taxindiaonline-income-tax',
            // 'labourlawreporter'
            //   ]
            // },
            content: {
              not: ''
            },
            // html: {
            //   contains: 'Case Tracker'
            // }
            createdAt: {
              gte: new Date('2024-08-19')
            }
            // html_cleaned: false,
            // indexed: false
          },
          take: BATCH_SIZE,
          orderBy: {
            id: 'asc'
          }
        })

        if (records.length === 0) {
          break
        }

        lastId = records[records.length - 1].id
        console.log('Total records: ', records.length, ' in batch: ', j)
        const size = records.length

        for (let i = 0; i < records.length; i += CHUNK_SIZE) {
          const startTimestamp = new Date().getTime()
          console.log('Processing chunk: ', i, ' to ', i + CHUNK_SIZE, ' of ', size) // prettier-ignore
          const chunk = records.slice(i, i + CHUNK_SIZE)
          const promises = chunk.map((record) =>
            generateMetadata(record, GPTModel.GPT4oMini, 'metadata')
          )
          await Promise.allSettled(promises)
          const endTimestamp = new Date().getTime()
          const duration = (endTimestamp - startTimestamp) / 1000
          const chunkBatchesRemaining = Math.ceil((size - i) / CHUNK_SIZE)
          const estimatedTimeRemainingMins = (
            (chunkBatchesRemaining * duration) /
            60
          ).toFixed(2)
          const estimatedTimeRemainingHrs = (
            Number(estimatedTimeRemainingMins) / 60
          ).toFixed(2)
          console.info(`Chunk processed in ${duration} seconds. Estimated time remaining: ${estimatedTimeRemainingMins} mins or (${estimatedTimeRemainingHrs} hrs) of batch: ${j} to ${j + BATCH_SIZE}`) // prettier-ignore
        }
      }
    } catch (error: any) {
      console.log('error: ', error.message)
    }
    console.log('\n\n<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>\n')
    console.log('FINISHED')
    console.log('\n<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>\n')

    res.status(200).json({ message: 'Data saved successfully!' })
  } catch (error) {
    res.status(500).json({ error: 'Failed to index.', errorcontent: error })
  }
}

async function generateMetadata(
  record: Pick<DocumentRecords, 'id' | 'content' | 'meta' | 'html'>,
  model: GPTModel,
  type: 'metadata' | 'summary'
) {
  try {
    await new Promise((resolve) => setTimeout(resolve, 20 * 1000))
    const currentMetadata = record.meta
      ? (JSON.parse(record.meta) as CaseData)
      : {}

    let text = record.content
      ?.replace(/\n/g, ' ')
      .replace(/\s\s+/g, ' ')
      .replace(/\. \./g, '.')
      .trim()
      .substring(0, 25000)

    if (!text || text.length < 100) {
      const $ = cheerio.load(record.html)
      const vartext = $('body').text().substring(0, 25000)
      if (vartext.length < 100) {
        console.log('Invalid text: ', record.id)
        return
      } else {
        text = vartext
      }
    }

    const TIMEOUT = 30
    // Create a Promise that rejects in TIMEOUT seconds
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Request timed out')), TIMEOUT * 1000)
    )

    const fetchPromise = fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        messages: [
          { role: 'user', content: JSON.stringify(generatePrompt(text, type)) }
        ],
        temperature: 0.1,
        response_format: { type: 'json_object' }
      })
    })

    const response: any = await Promise.race([fetchPromise, timeoutPromise])

    const completion = await response.json()

    if (
      (completion satisfies TextDavinciResponse) &&
      completion?.choices &&
      isJsonString(completion.choices[0].message.content)
    ) {
      const newMetadata = JSON.parse(
        completion.choices[0].message.content
      ) as CaseData
      let meta = { ...newMetadata }

      let date
      if (meta.date) {
        let [year, month, day] = meta.date.toString().split('-')
        if (Number(month) > 12) {
          month = '12'
        }
        if (Number(day) > 31) {
          day = '31'
        }
        date = new Date(`${year}-${month}-${day}`)

        meta.date = date
        meta.year = meta.date.getFullYear()
      }
      meta = { ...currentMetadata, ...newMetadata }

      if (meta.title && meta.title.length > 5) {
        await db.documentRecords.update({
          where: {
            id: record.id
          },
          data: {
            title: meta.title?.substring(0, 100),
            date: meta.date ? meta.date : undefined,
            meta: JSON.stringify(meta),
            indexed: true
          }
        })
        console.log('Indexed: ', record.id, meta.case)
        console.log('>>>>> Metadata: ', record.id, ' >>>>>\n', meta)
      } else {
        console.log('Invalid metadata: ', record.id, JSON.stringify(meta))
      }
    } else {
      console.log(JSON.stringify(completion, null, 2))
    }
  } catch (error: any) {
    if (error.message === 'Request timed out') {
      console.log('Request timed out for record: ', record.id)
    } else {
      console.log('Error indexing: ', record.id, error.message)
    }
  }
}

function generatePrompt(text: string, type: 'metadata' | 'summary') {
  switch (type) {
    case 'metadata': {
      return `
    Process the following document's heading section to generate metadata for the document.
    -----------
    ${text}
    -----------
    The metadata should include the following fields: case, court, date, judges, parties, citation, headnotes. The metadata should be in JSON format. The following is an example, replace the values from context.
    {
      "title": "The Management, Sri Ambal Mills Ltd. vs The Workmen",
      "case":"case number / docket number",
      "date":"2023-04-07",
      "argument_date": "2023-07-07",
      "decision_date": "2023-10-05",
      "outcome": "Allowed",
      "court":"United States Federal Supreme Court",
      "judges":[
        "Hon'ble Mr. Madan B. Lokur, J.",
        "Hon'ble Mr. R.K. Agrawal, J."
      ],
      "parties":[
        "The Management, Sri Ambal Mills Ltd."
      ],
      "related_cases":["case docket number"],
      "precedent_citations":["citation docket number"],
      "citation":["case citation docket number"],
      "headnotes":"SHORT NOTE",
      "summary":"summary of case and judgement in 1 paragraph",
      "year": 2023
    }
    IMPORTANT: make sure all dates are valid and in the correct format. has to be in YYYY-MM-DD format (not DD-MM-YYYY or DD/MM/YYY) If given date is more than 31 (last day of the month) reduce it to last date.
    IMPORTANT: "headnotes" will be "SHORT NOTE" or "JUDGEMENT" or "ARTICLE" or "CASE COMMENT" or "BOOK REVIEW" or "NOTES".
    IMPORTANT: You need to come up with a title for the case.
    Do not assume the save values as above for the JSON. You have to get values from provided context. You can leave any field blank if you are unable to find the information in the document. But the key should be present in the metadata. Output has to be in JSON format, no other format on textual information will be accepted.
    `
    }

    case 'summary': {
      return `
      The following is a case document involving a legal matter. Summarize it by extracting the key aspects including the court's decision / judgement, the nature of the charges, the outcome of the investigation and enquiry, the argument of the defense, the response of the prosecution, and the final judgment by the court. Ensure the summary provides a clear and concise overview of the case's core issues and resolution in no more than 150 words:
      -----------
      ${text}
      -----------

      The summary should be in the following format:
      {
        "title": "..."
        "summary": "..."
      }
      Output has to be in JSON format, no other format on textual information will be accepted.
      `
    }

    default:
      break
  }
}
