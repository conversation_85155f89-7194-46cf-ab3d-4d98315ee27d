'use client'

import React, { createContext, useContext } from 'react'
import { DocumentTitle } from '@/types/case'

interface EditorContextValue {
  content: string
  setContent: (content: string) => void
  refDocuments: DocumentTitle[]
}

const EditorContext = createContext<EditorContextValue | null>(null)

export const useEditorContext = (): EditorContextValue => {
  const context = useContext(EditorContext)
  if (!context) {
    throw new Error(
      'useEditorContext must be used inside an EditorContextProvider'
    )
  }
  return context
}

interface EditorContextProviderProps {
  content: string
  setContent: (content: string) => void
  refDocuments: DocumentTitle[]
  children: React.ReactNode
}

export const EditorContextProvider: React.FC<EditorContextProviderProps> = ({
  content,
  setContent,
  refDocuments,
  children
}) => {
  return (
    <EditorContext.Provider value={{ content, setContent, refDocuments }}>
      {children}
    </EditorContext.Provider>
  )
}
