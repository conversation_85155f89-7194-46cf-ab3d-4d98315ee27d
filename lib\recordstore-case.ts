import { db, handlePrismaError } from './db'

export const mapDocumentToCase = async ({
  documentId,
  binderId,
  userId
}: {
  documentId: number
  binderId: string
  userId: string
}) => {
  try {
    console.log('Mapping document to case', documentId, binderId)
    const caseInfo = await db.binder.findUnique({
      where: {
        id: binderId
      }
    })

    let dbDataset = await db.dataset.findFirst({
      where: {
        binderId: binderId
      }
    })

    if (!dbDataset) {
      dbDataset = await db.dataset.create({
        data: {
          name: caseInfo?.name || 'Dataset',
          binderId: binderId,
          createdBy: userId
        }
      })
    }

    if (dbDataset) {
      await db.documentRecordDatasetMap.create({
        data: {
          DocumentRecords: {
            connect: {
              id: documentId
            }
          },
          Dataset: {
            connect: {
              id: dbDataset.id
            }
          }
        }
      })
    }
  } catch (error) {
    handlePrismaError(error)
  }
}
