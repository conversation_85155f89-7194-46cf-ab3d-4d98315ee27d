import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { caseBinderFeatures } from '@/config/dashboard'
import { db } from '@/lib/db'
import { CaseFileType, CreditType } from '@prisma/client'
import { redirect } from 'next/navigation'
import DemandLetterGenerator from '@/components/elements/doc-selector/demand-letter-generator'
import { getFeatureUsageStats } from '@/lib/session'

export const metadata = caseBinderFeatures['demandLetterGeneration']

interface DemandLetterPageProps {
  params: {
    binderId: string
  }
}

export default async function DemandLetterPage({
  params
}: DemandLetterPageProps) {
  const usageStats = await getFeatureUsageStats({
    feature: CreditType.case
  })

  const binderPromise = db.binder.findUnique({
    where: {
      id: params.binderId
    }
  })

  const caseEval = await db.caseFile.findUnique({
    where: {
      binderId_fileType: {
        binderId: params.binderId,
        fileType: CaseFileType.CASE_EVALUATION
      }
    }
  })

  const dataSourcePromise = db.dataset.findMany({
    where: {
      OR: [
        {
          createdBy: params.binderId
        },
        {
          binderId: params.binderId
        }
      ]
    },
    select: {
      DocumentRecordDatasetMap: {
        select: {
          DocumentRecords: {
            select: {
              id: true,
              title: true,
              indexed: true
            }
          }
        }
      }
    }
  })

  if (!caseEval) {
    redirect(`/dashboard/case/${params.binderId}/case-evaluation`)
  }

  const demandLetterPromise = db.caseFile.findUnique({
    where: {
      binderId_fileType: {
        binderId: params.binderId,
        fileType: CaseFileType.DEMAND_LETTER
      }
    }
  })

  const [binder, DemandLetter, dataSource] = await Promise.all([
    binderPromise,
    demandLetterPromise,
    dataSourcePromise
  ])

  const documents = dataSource
    .map((dataSet) =>
      dataSet.DocumentRecordDatasetMap.map((doc) => {
        return {
          id: doc.DocumentRecords.id,
          title: doc.DocumentRecords.title,
          indexed: doc.DocumentRecords.indexed
        }
      })
    )
    .flat()

  return (
    <DashboardShell>
      <DashboardHeader heading={metadata.title} text={metadata.description} />
      <div className="grid gap-10">
        <DemandLetterGenerator
          usageStats={usageStats}
          binder={binder!}
          demandLetter={DemandLetter}
          refDocuments={documents}
        />
      </div>
    </DashboardShell>
  )
}
