import { Session } from 'next-auth'
import { Card } from '../../ui/card'
import { ChatWindow } from './chat-window'
import { getFeatureUsageStats } from '@/lib/session'
import { CreditType, ResearchType } from '@prisma/client'
import type { ResearchStoreContent } from '@/types'
import { ReactElement } from 'react'

export async function LegalQuery({
  user,
  researchProps,
  showFilters,
  researchType,
  emptyStateComponent,
  namespace,
  masquerade
}: {
  user: Session['user']
  researchProps: ResearchStoreContent
  showFilters: boolean
  researchType: ResearchType
  emptyStateComponent?: ReactElement
  namespace?: string
  masquerade?: boolean
}) {
  const usageStats = await getFeatureUsageStats({
    feature: CreditType.research,
    user: masquerade ? user : undefined
  })

  return (
    <Card>
      <ChatWindow
        user={user}
        researchProps={researchProps}
        stats={usageStats}
        researchType={researchType}
        emptyStateComponent={emptyStateComponent}
        showIngestForm={true}
        placeholder="Ask a question..."
        namespace={namespace}
        showFilters={showFilters}
        masquerade={masquerade}
      />
    </Card>
  )
}
