import * as React from 'react'
import Link from 'next/link'

import { MainNavItem } from '@/types'
import { cn } from '@/lib/utils'
import { Icons } from '@/components/elements/icons'
import { useLockBody } from '@/lib/hooks/use-lock-body'

interface MobileNavProps {
  items: MainNavItem[]
  menuRef: React.MutableRefObject<HTMLDivElement | null>
  children?: React.ReactNode
}

export function MobileNav({ items, menuRef, children }: MobileNavProps) {
  useLockBody()

  return (
    <div
      className={cn(
        'fixed inset-0 top-16 z-50 grid h-[calc(100vh-4rem)] grid-flow-row auto-rows-max overflow-auto p-6 pb-32 shadow-md animate-in slide-in-from-top-80 lg:hidden'
      )}
    >
      <div
        className="relative z-20 grid gap-6 rounded-md bg-popover p-4 text-popover-foreground shadow-md border"
        ref={menuRef}
      >
        <Link href="/" className="flex items-center space-x-2">
          <Icons.logoWide className="h-10" width={250} />
        </Link>
        <nav className="grid grid-flow-row auto-rows-max text-sm">
          {items.map((item, index) => (
            <Link
              key={index}
              href={item.disabled ? '#' : item.href}
              className={cn(
                'flex my-1 w-full md:w-[30%] items-center rounded-md p-2 text-sm font-medium hover:underline border',
                item.disabled && 'cursor-not-allowed opacity-60'
              )}
            >
              {item.title}
            </Link>
          ))}
        </nav>
        {children}
      </div>
    </div>
  )
}
