import { NewsletterGeneratorConfig } from '@/types'

export const newsletterGeneratorProps: NewsletterGeneratorConfig = {
  type: [
    {
      label: 'Website Design',
      value: 'Website Design'
    },
    {
      label: 'Landing Page Design',
      value: 'Landing Page Design'
    },
    {
      label: 'Logo Design',
      value: 'Logo Design'
    },
    {
      label: 'Brand Identity Design',
      value: 'Brand Identity Design'
    },
    {
      label: 'Mobile App Design',
      value: 'Mobile App Design'
    },
    {
      label: 'Packaging Design',
      value: 'Packaging Design'
    },
    {
      label: 'Social Media Graphics',
      value: 'Social Media Graphics'
    },
    {
      label: 'Print Advertisement Design',
      value: 'Print Advertisement Design'
    },
    {
      label: 'Infographic Design',
      value: 'Infographic Design'
    },
    {
      label: 'Presentation Design',
      value: 'Presentation Design'
    },
    {
      label: 'Brochure Design',
      value: 'Brochure Design'
    },
    {
      label: 'Banner Design',
      value: 'Banner Design'
    },
    {
      label: 'Email Newsletter Design',
      value: 'Email Newsletter Design'
    },
    {
      label: 'Illustration Design',
      value: 'Illustration Design'
    },
    {
      label: 'T-shirt Design',
      value: 'T-shirt Design'
    },
    {
      label: 'Poster Design',
      value: 'Poster Design'
    },
    {
      label: 'Business Card Design',
      value: 'Business Card Design'
    },
    {
      label: 'Menu Design',
      value: 'Menu Design'
    },
    {
      label: 'Event Invitation Design',
      value: 'Event Invitation Design'
    },
    {
      label: 'Billboard Design',
      value: 'Billboard Design'
    },
    {
      label: 'Product Label Design',
      value: 'Product Label Design'
    }
  ],

  description: [
    {
      value: 'Product Launch Campaign',
      label:
        'Product Launch Campaign: Promote the launch of a new product with a comprehensive marketing campaign across various channels.'
    },
    {
      value: 'Rebranding Effort',
      label:
        'Rebranding Effort: Redesign the brand identity and marketing materials to reposition the company in the market.'
    },
    {
      value: 'E-commerce Website Redesign',
      label:
        'E-commerce Website Redesign: Enhance the user experience and visual appeal of an existing e-commerce website.'
    },
    {
      value: 'Social Media Awareness',
      label:
        'Social Media Awareness: Increase brand awareness and engagement through creative social media graphics and posts.'
    },
    {
      value: 'Corporate Presentation',
      label:
        'Corporate Presentation: Create a visually appealing and informative presentation for a business conference or pitch.'
    },
    {
      value: 'Event Promotion',
      label:
        'Event Promotion: Design promotional materials for an upcoming event or conference.'
    },
    {
      value: 'Mobile App User Interface',
      label:
        'Mobile App User Interface: Design an intuitive and visually appealing user interface for a mobile application.'
    },
    {
      value: 'Print Advertisement',
      label:
        'Print Advertisement: Create an eye-catching print ad for a magazine or newspaper.'
    },
    {
      value: 'Packaging Refresh',
      label:
        'Packaging Refresh: Redesign the packaging for an existing product to attract a wider audience.'
    },
    {
      value: 'Website Landing Page',
      label:
        'Website Landing Page: Design a high-converting landing page for a specific marketing campaign.'
    },
    {
      value: 'Infographic for Data Presentation',
      label:
        'Infographic for Data Presentation: Present complex data and statistics in a visually engaging infographic format.'
    },
    {
      value: 'Social Media Influencer Campaign',
      label:
        'Social Media Influencer Campaign: Design assets for a social media influencer marketing campaign.'
    },
    {
      value: 'Environmental Sustainability Campaign',
      label:
        'Environmental Sustainability Campaign: Create visuals for a sustainability-focused marketing campaign.'
    },
    {
      value: 'Educational Poster Design',
      label:
        'Educational Poster Design: Design an educational poster for schools or workshops.'
    },
    {
      value: 'Non-profit Fundraising Campaign',
      label:
        "Non-profit Fundraising Campaign: Create marketing materials for a charitable organization's fundraising efforts."
    },
    {
      value: 'Magazine Cover Design',
      label:
        'Magazine Cover Design: Design a captivating cover for a magazine publication.'
    },
    {
      value: 'Brand Logo Redesign',
      label:
        'Brand Logo Redesign: Revamp an existing brand logo to give it a fresh and modern look.'
    },
    {
      value: 'Restaurant Menu Redesign',
      label:
        'Restaurant Menu Redesign: Design an attractive and functional menu for a restaurant.'
    },
    {
      value: 'Holiday-Themed Promotion',
      label:
        'Holiday-Themed Promotion: Create holiday-specific marketing materials for seasonal promotions.'
    },
    {
      value: 'Artistic Illustration',
      label:
        'Artistic Illustration: Produce an artistic illustration for various purposes, such as book covers, album art, or wall art.'
    }
  ],
  languageStyle: [
    {
      value: 'formal',
      label: 'Formal: Use formal/professional language and tone.'
    },
    {
      value: 'casual',
      label: 'Casual: Use casual/playful language and tone.'
    }
  ]
}
