import type { NextApiRequest, NextApiResponse } from 'next'
import { env } from '@/env.mjs'
import axios from 'axios'
import * as cheerio from 'cheerio'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (env.NODE_ENV !== 'development') {
      throw new Error('Unauthorized')
    }

    const response: FacultyContactInfo[] = []

    const urls = [1, 2, 3, 4, 5, 6, 7, 8].map(
      (i) =>
        `https://hls.harvard.edu/faculty/?page=${i}&faculty_type=HLS%20Professors%2CEmeritus%20Professors%2CLecturers%2CVisiting%20Professors%2CHarvard%20University%20Affiliated%20Professors`
    )

    for (const url of urls) {
      const data = await scrapeFacultyContactInfo(url)
      response.push(...data)
    }

    res.status(200).json(response)
  } catch (error) {
    res.status(500).json({ error: 'Failed to index.', errorcontent: error })
  }
}

interface FacultyContactInfo {
  name: string
  email: string
  email2: string
  phone: string
  phone2: string
}

async function scrapeFacultyContactInfo(
  initialUrl: string
): Promise<FacultyContactInfo[]> {
  try {
    // Fetch the initial page
    const { data: initialPageHtml } = await axios.get(initialUrl)
    const $ = cheerio.load(initialPageHtml)

    // Get all the faculty page links
    const facultyLinks = $('a.faculty-feed__item-link')
      .map((_, el) => $(el).attr('href'))
      .get()

    // Array to store the contact information
    const contactInfoList: FacultyContactInfo[] = []

    // Function to fetch and parse each faculty page
    async function fetchAndParseFacultyPage(
      url: string
    ): Promise<FacultyContactInfo | null> {
      try {
        const { data: facultyPageHtml } = await axios.get(url)
        const page = cheerio.load(facultyPageHtml)

        // Extract the name
        const name = page('.faculty-page__name').text().trim()

        // Extract the email
        const email =
          page('p.contact__email a')
            .map((_, el) => page(el).text())
            .get()[0] || 'No email found'
        const email2 =
          page('p.contact__email a')
            .map((_, el) => page(el).text())
            .get()[1] || 'No email found'

        // Extract the phone number
        const phone =
          page('p.contact__phone')
            .map((_, el) => page(el).text())
            .get()[0] || 'No phone found'
        const phone2 =
          page('p.contact__phone')
            .map((_, el) => page(el).text())
            .get()[1] || 'No phone found'

        return { name, email, email2, phone, phone2 }
      } catch (error: any) {
        console.error(
          `Failed to fetch data from: ${url}, Error: ${error.message}`
        )
        return null
      }
    }

    // Loop over each faculty link and gather contact info
    for (const link of facultyLinks) {
      const fullUrl = new URL(link, initialUrl).href // Ensure proper URL format
      console.log(`Fetching data from: ${fullUrl}`)

      const contactInfo = await fetchAndParseFacultyPage(fullUrl)
      if (contactInfo) {
        contactInfoList.push(contactInfo)
      }
    }

    return contactInfoList
  } catch (error) {
    console.error(
      `Failed to scrape faculty contact info from ${initialUrl}:`,
      error
    )
    return []
  }
}
