import { PrismaClient as PostgreSQLClient } from '@/generated/postgres'
import { db } from '@/lib/db'
import { NextApiRequest, NextApiResponse } from 'next'
const postgres = new PostgreSQLClient()

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const BATCH_SIZE = 1000
    let lastId = 0
    const endId = 2700000

    for (let j = 0; j < 3000000; j += BATCH_SIZE) {
      const USVectorRecords: any = await postgres.$queryRaw`
        SELECT DISTINCT "id", x"docId"
        FROM "USVectorStore"
        WHERE "year" IS NULL
        AND "id" > ${lastId} AND "id" <= ${endId}
        ORDER BY "id"
        LIMIT ${BATCH_SIZE}`

      if (USVectorRecords.length === 0) break

      lastId = USVectorRecords[USVectorRecords.length - 1].id
      console.log('Total records: ', USVectorRecords.length, ' in batch: ', j)

      const docIds = USVectorRecords.map(
        (record: { docId: number }) => record.docId
      )

      // Fetch corresponding dates from DocumentRecords
      const records = await db.documentRecords.findMany({
        where: {
          id: {
            in: docIds
          }
        },
        select: {
          id: true,
          date: true
        }
      })

      // Update year in USVectorStore
      for (const record of records) {
        if (record.date) {
          const year = new Date(record.date).getFullYear()

          await postgres.$executeRaw`
            UPDATE "USVectorStore"
            SET "year" = ${year}
            WHERE "docId" = ${record.id}`
        }
      }
    }

    console.log('Year updates completed.')

    return res.status(200).json({ data: 'Year update completed' })
  } catch (error) {
    console.error('Error:', error)
    return res.status(500).json({ error })
  }
}
