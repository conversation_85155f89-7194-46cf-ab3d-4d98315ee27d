import type { NextApiRequest, NextApiResponse } from 'next'
import * as cheerio from 'cheerio'
import { db } from '@/lib/db'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

    if (req.method === 'OPTIONS') {
      return res.status(200).end()
    }

    const payload: {
      url: string
      ref: string
      source: string
      html: string
    } = req.body

    try {
      const $ = cheerio.load(payload.html)

      // select all <font color="#006600"> elements
      const titleElements = $('font[color="#006600"]')
        .map((_, el) => $(el).text())
        .get()
      const title = titleElements.join(' ').substring(0, 90)
      let date = new Date()
      // select all <p> with align right containing <strong> elements
      const caseDataElements = $('p[align="right"] strong')
        .map((_, el) => $(el).text())
        .get()
      caseDataElements.forEach((item, index) => {
        // if item starts with Dated: February 12, 2024
        if (item.startsWith('Dated:')) {
          const dateMatch = item.match(/Dated: ([\w]+) (\d+), (\d+)/)
          if (dateMatch && dateMatch.length > 0) {
            const [_, month, day, year] = dateMatch
            date = new Date(`${month} ${day}, ${year}`)
          }
        }
      })

      // collect all <p> elements that contains <strong> elements including the part outside of strong
      const meta = $('p:has(strong)')
        .map((_, el) => $(el).text())
        .get()
        .filter((item) => item !== '' && item.length > 10 && item.length < 100)

      // remove anchor tag with text Print This Document

      // grab all <p> elements

      const content = $('p')
        .map((_, el) => $(el).text())
        .get()
        .join('\n')

      const validate = await db.documentRecords.findUnique({
        where: {
          source_ref: {
            source: payload.source,
            ref: payload.ref
          }
        }
      })

      // create basic html template then embed payload.html inside it
      const htmlTemplate = `<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>${title}</title></head><body>${payload.html}</body></html>`

      let store = null
      if (validate) {
        store = await db.documentRecords.update({
          where: {
            id: validate.id
          },
          data: {
            title,
            date,
            content: content,
            meta: JSON.stringify(meta),
            html: htmlTemplate,
            source: payload.source,
            ref: payload.ref,
            url: payload.url
          }
        })
        console.log('Data updated for: ', store.id, store.title, store.ref)
      } else {
        store = await db.documentRecords.create({
          data: {
            title,
            date,
            content: content,
            meta: JSON.stringify(meta),
            html: htmlTemplate,
            source: payload.source,
            ref: payload.ref,
            url: payload.url
          }
        })
        console.log('Data stored for: ', store.id, store.title, store.ref)
      }

      return res
        .status(200)
        .json({ message: 'Data saved successfully!', store })
    } catch (error) {
      console.error('Error during request:', error)
    }

    res.status(200).json({ message: 'Data saved successfully!' })
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch and parse the website.' })
  }
}

function parseCaseData(dataSet1: string[], dataSet2?: string[]): any {
  let result: any = {
    case: '',
    court: '',
    judges: [],
    parties: [],
    citation: '',
    date: undefined,
    headnotes: ''
  }

  for (let item of dataSet1) {
    const cleanItem = item.replace(/[^ -~\n\r]|[\uFFFD]/g, '-').trim()
    if (cleanItem.includes('LLR')) {
      result.case = cleanItem
    } else if (cleanItem.includes(' COURT')) {
      result.court = cleanItem
    } else if (cleanItem.includes("Hon'ble")) {
      result.judges.push(cleanItem)
    } else if (cleanItem.includes('Dt/')) {
      result.citation = cleanItem
      let dateMatch = cleanItem.match(/\d+-\d+-\d+/)
      if (dateMatch && dateMatch.length > 0) {
        const [day, month, year] = dateMatch[0].trim().split('-').map(Number)
        result.date = new Date(year, month - 1, day)
      }
    } else if (
      !cleanItem.includes('v.') &&
      !cleanItem.includes('vs.') &&
      cleanItem !== '' &&
      !result.court
    ) {
      result.headnotes = cleanItem
    }
  }

  if (dataSet2) {
    const vIndex = dataSet2.findIndex(
      (item) => item.includes('v.') || item.includes('vs.')
    )
    if (vIndex !== -1) {
      result.parties = dataSet2
        .slice(0, vIndex)
        .map((item) => item.replace(/[^ -~\n\r]|[\uFFFD]/g, '-').trim())
    }
  }

  return result
}
