'use client'

import { DataTable } from '../data-table/data-table'
import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTableColumnHeader } from '../data-table/data-table-column-header'
import { Settings2 } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '../../ui/dropdown-menu'
import { useEffect, useState } from 'react'
import { Button } from '../../ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { toast } from '../../ui/use-toast'
import { deleteBinder } from '@/lib/actions/binder'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export function BinderList({
  binders
}: {
  binders: {
    id: string
    title: string
    numDocuments: number
    numResearches: number
    createdBy: string
    createdAt: Date
  }[]
}) {
  return (
    <DataTable
      columns={binderListingColumns}
      data={binders}
      bulkAction={(selectedRows) => (
        <>
          <DeleteSelectedBindersDialog
            selectedBinders={selectedRows as string[]}
          />
        </>
      )}
    />
  )
}

export function DeleteBinderDialog({
  id,
  open,
  setOpen
}: {
  id: string
  open: boolean
  setOpen: (open: boolean) => void
}) {
  const router = useRouter()
  const handleDeleteBinder = async (id: string) => {
    const deleteData = await deleteBinder(id)
    if (deleteData) {
      toast({
        title: 'Case deleted'
      })
    } else {
      toast({
        title: 'Failed to delete case',
        variant: 'destructive'
      })
    }

    router.refresh()
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Case</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this case?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="destructive" onClick={() => handleDeleteBinder(id)}>
            Confirm Delete
          </Button>
          <Button onClick={() => setOpen(false)} variant="secondary">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function DeleteSelectedBindersDialog({
  selectedBinders
}: {
  selectedBinders: string[]
}) {
  const router = useRouter()
  const handleDeleteBinders = async (ids: string[]) => {
    const deleteData = await Promise.all(ids.map((id) => deleteBinder(id)))
    if (deleteData) {
      toast({
        title: 'Cases deleted'
      })
    } else {
      toast({
        title: 'Failed to delete cases',
        variant: 'destructive'
      })
    }

    router.refresh()
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="destructive" size="xs">
          Delete Selected
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Case</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete these {selectedBinders.length}{' '}
            cases?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              variant="destructive"
              onClick={() => handleDeleteBinders(selectedBinders)}
            >
              Confirm Delete
            </Button>
          </DialogClose>
          <Button variant="secondary">Cancel</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function BinderTableActions({ id }: { id: string }) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  useEffect(() => {
    if (!isDeleteDialogOpen) {
      setTimeout(() => (document.body.style.pointerEvents = ''), 500)
    }
  }, [isDeleteDialogOpen])

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <Settings2 className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          <DropdownMenuItem>Edit</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => setIsDeleteDialogOpen(true)}
            className="hover:bg-red-400"
          >
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <DeleteBinderDialog
        id={id}
        open={isDeleteDialogOpen}
        setOpen={setIsDeleteDialogOpen}
      />
    </div>
  )
}

export const binderListingColumns: ColumnDef<any>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value: any) =>
          table.toggleAllPageRowsSelected(!!value)
        }
        aria-label="Select all"
        className="translate-y-[2px] scale-110"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value: any) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px] scale-110"
      />
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'title',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => (
      <div className="flex space-x-2">
        <Link
          href={`/dashboard/case/${row.original.id}`}
          className="max-w-[500px] font-medium hover:underline"
        >
          {row.getValue('title')}
        </Link>
      </div>
    )
  },
  {
    accessorKey: 'numResearches',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Linked Researches" />
    ),
    cell: ({ row }) => (
      <div className="flex space-x-2">
        <span className="font-medium">{row.getValue('numResearches')}</span>
      </div>
    )
  },
  {
    accessorKey: 'numDocuments',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Linked Documents" />
    ),
    cell: ({ row }) => (
      <div className="flex space-x-2">
        <span className="font-medium">{row.getValue('numDocuments')}</span>
      </div>
    )
  },
  {
    accessorKey: 'createdBy',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created By" />
    ),
    cell: ({ row }) => (
      <div className="flex space-x-2">
        <span className="max-w-[500px] truncate font-medium">
          {row.getValue('createdBy')}
        </span>
      </div>
    )
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created At" />
    ),
    cell: ({ row }) => (
      <div className="flex space-x-2">
        <span className="max-w-[500px] truncate font-medium">
          {new Date(row.getValue('createdAt')).toLocaleDateString('de-DE', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          })}
        </span>
      </div>
    )
  },
  {
    id: 'actions',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Actions" />
    ),
    cell: ({ row }) => <BinderTableActions id={row.original.id} />
  }
]
