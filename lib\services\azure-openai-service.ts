// lib/services/azure-openai-service.ts

import { env } from '@/env.mjs'
import { GPTModel } from '@/types'
import { trackTokenUsage } from './llm-token-usage'
import { AzureOpenAI, OpenAI } from 'openai'
import { ChatCompletionMessageParam } from 'openai/resources/chat/completions.mjs'
import { LLMProvider } from '@prisma/client'

// Retry configuration
const MAX_RETRIES = 5
const MIN_RETRY_DELAY = 30000 // 30 seconds
const MAX_RETRY_DELAY = 90000 // 90 seconds

// Helper function to check if error is rate limit related
function isRateLimitError(error: any): boolean {
  return (
    error?.status === 429 ||
    error?.code === 'rate_limit_exceeded' ||
    error?.message?.includes('rate limit') ||
    error?.message?.includes('Rate limit')
  )
}

// Helper function to wait for a random delay between min and max
function getRandomDelay(): number {
  return (
    Math.floor(Math.random() * (MAX_RETRY_DELAY - MIN_RETRY_DELAY + 1)) +
    MIN_RETRY_DELAY
  )
}

// Sleep function
function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

// Model to Azure deployment mapping
const MODEL_DEPLOYMENTS = {
  [GPTModel.GPT4x1]: 'gpt-4.1',
  [GPTModel.GPTo4Mini]: 'o4-mini'
} as const

// Models that don't support temperature/sampling parameters
const REASONING_MODELS = new Set<GPTModel>([GPTModel.GPTo4Mini])

const MODEL_VERSIONS = {
  [GPTModel.GPT4x1]: '2024-12-01-preview',
  [GPTModel.GPTo4Mini]: '2024-12-01-preview'
} as const

export async function createAzureCompletion({
  messages,
  model = GPTModel.GPTo4Mini,
  temperature = 0.3,
  max_tokens,
  frequency_penalty = 0,
  presence_penalty = 0,
  top_p = 1,
  json = true,
  teamId,
  purpose = 'completion',
  activity = 'chat'
}: {
  messages: ChatCompletionMessageParam[]
  model?: GPTModel.GPT4x1 | GPTModel.GPTo4Mini
  temperature?: number
  max_tokens?: number
  frequency_penalty?: number
  presence_penalty?: number
  top_p?: number
  json?: boolean
  teamId?: string
  purpose?: string
  activity?: string
}): Promise<any> {
  const deployment = MODEL_DEPLOYMENTS[model]
  if (!deployment) {
    throw new Error(
      `Model ${model} is not supported. Supported models: ${Object.keys(MODEL_DEPLOYMENTS).join(', ')}`
    )
  }
  const baseURL = `${env.AZURE_OPENAI_ENDPOINT}/${deployment}`

  // Initialize Azure OpenAI client
  const client = new AzureOpenAI({
    apiKey: env.AZURE_OPENAI_API_KEY,
    baseURL,
    apiVersion: MODEL_VERSIONS[model],
    defaultHeaders: { 'api-key': env.AZURE_OPENAI_API_KEY }
  })

  // Build parameters - exclude sampling params for reasoning models
  const isReasoningModel = REASONING_MODELS.has(model)

  const params: OpenAI.Chat.ChatCompletionCreateParams = {
    model: deployment,
    messages,
    max_tokens,
    ...(!isReasoningModel && {
      temperature,
      frequency_penalty,
      presence_penalty,
      top_p
    }),
    ...(json && { response_format: { type: 'json_object' } })
  }

  // Retry logic with exponential backoff for rate limits
  let lastError: any
  for (let attempt = 0; attempt <= MAX_RETRIES; attempt++) {
    try {
      const response = await client.chat.completions.create(params)

      // Track token usage
      if (teamId && response.usage) {
        await trackTokenUsage({
          teamId,
          provider: LLMProvider.OPENAI,
          model,
          purpose,
          activity,
          requestId: response.id,
          finishReason: response.choices[0]?.finish_reason || undefined,
          usage: response.usage
        }).catch((error) => {
          console.error('Failed to track Azure OpenAI token usage:', error)
        })
      }

      if (json) {
        return JSON.parse(response.choices[0]?.message?.content || '{}')
      }

      return response.choices[0]?.message?.content ?? ''
    } catch (error) {
      lastError = error

      // If it's not a rate limit error or we've exhausted retries, throw immediately
      if (!isRateLimitError(error) || attempt === MAX_RETRIES) {
        throw error
      }

      // Calculate delay and wait before retrying
      const delay = getRandomDelay()
      console.warn(
        `Rate limit hit for Azure OpenAI (attempt ${attempt + 1}/${MAX_RETRIES + 1}). ` +
          `Waiting ${Math.round(delay / 1000)}s before retry...`
      )
      await sleep(delay)
    }
  }

  // This should never be reached, but just in case
  throw lastError
}
