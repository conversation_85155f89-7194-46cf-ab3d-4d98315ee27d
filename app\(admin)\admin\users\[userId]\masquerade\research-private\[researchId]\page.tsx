import { notFound } from 'next/navigation'
import { db } from '@/lib/db'
import {
  getFeatureUsageStats,
  getMasqueradeUserNonNullable
} from '@/lib/session'
import { features } from '@/config/dashboard'
import { Card } from '@/components/ui/card'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { CopyHrefButton } from '@/components/elements/buttons/button-copy-href'
import { ChatWindow } from '@/components/elements/chat/chat-window'
import { CreditType, ResearchType } from '@prisma/client'
import { PrivateUploadButton } from '@/components/elements/buttons/private-upload-button'
import { AdminMasqueradeProps } from '../../../layout'
import type { ResearchStoreContent } from '@/types'

export const metadata = features['researchPrivate']

interface ResearchPageProps extends AdminMasqueradeProps {
  params: AdminMasqueradeProps['params'] & { researchId: string }
}

export default async function ResearchPage({ params }: ResearchPageProps) {
  const user = await getMasqueradeUserNonNullable(params.userId)

  const usageStats = await getFeatureUsageStats({
    feature: CreditType.research
  })

  const research = await db.researchStore.findFirst({
    where: {
      id: params.researchId
    }
  })

  if (!research) {
    return notFound()
  }

  const documents = await db.documentRecords.findMany({
    select: {
      id: true,
      title: true
    },
    where: {
      source: user.teamId,
      region: user.region
    }
  })

  const researchPropsRaw = research.content as unknown as ResearchStoreContent
  const researchProps: ResearchStoreContent = {
    model: researchPropsRaw.model,
    sources: researchPropsRaw.sources,
    court: researchPropsRaw.court || [],
    year: researchPropsRaw.year || [],
    sourcesForMessages: researchPropsRaw.sourcesForMessages,
    sourceLabels: documents.map((doc) => ({
      id: doc.id.toString(),
      title: doc.title
    })),
    messages: researchPropsRaw.messages
  }
  researchProps.researchId = research.id

  return (
    <DashboardShell>
      <div className="flex justify-between items-center">
        <DashboardHeader heading={metadata.title} text={metadata.description}>
          <PrivateUploadButton />
        </DashboardHeader>
        <CopyHrefButton />
      </div>
      <div className="grid gap-10">
        <Card>
          <ChatWindow
            user={user}
            stats={usageStats}
            researchType={ResearchType.private}
            researchProps={researchProps}
            showIngestForm={true}
            placeholder="Ask a question..."
            namespace={user.teamId}
            showFilters={false}
          />
        </Card>
      </div>
    </DashboardShell>
  )
}
