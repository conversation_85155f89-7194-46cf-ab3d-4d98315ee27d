import { AttorneyInsightsFormData } from '@/components/elements/doc-selector/attorney-strategy-form'

export const attorneyInsightsSectionMapping: Record<
  keyof AttorneyInsightsFormData,
  string[]
> = {
  courtDynamics: [
    'Jurisdiction-Specific Considerations',
    'Trial Strategy',
    'Attorney Insights & Local Considerations',
    'Litigation Strategy'
  ],
  defenseBehavior: [
    'Jurisdiction-Specific Considerations',
    'Attorney Insights & Local Considerations',
    'Litigation Strategy'
  ],
  clientCircumstances: [
    'Case strengths',
    'Case weaknesses',
    'Trial Strategy',
    'Attorney Insights & Local Considerations',
    'Litigation Strategy'
  ],
  pretrialStrategy: [
    'Case Strengths',
    'Case weaknesses',
    'Attorney Insights & Local Considerations',
    'Litigation Strategy'
  ],
  trialReadiness: [
    'Case Strengths',
    'Case weaknesses',
    'Trial Strategy',
    'Attorney Insights & Local Considerations',
    'Litigation Strategy'
  ],
  weakSpots: [
    'Case weaknesses',
    'Trial Strategy',
    'Attorney Insights & Local Considerations',
    'Litigation Strategy'
  ],
  comparableCases: [
    'Jurisdiction-Specific Considerations',
    'Attorney Insights & Local Considerations',
    'Litigation Strategy'
  ],
  gutFeel: [
    'Jurisdiction-Specific Considerations',
    'Attorney Insights & Local Considerations',
    'Litigation Strategy'
  ],
  miscObservations: [
    'Jurisdiction-Specific Considerations',
    'Attorney Insights & Local Considerations',
    'Litigation Strategy'
  ]
}
