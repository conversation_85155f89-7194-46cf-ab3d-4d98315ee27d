import type { NextApiRequest, NextApiResponse } from 'next'
import axios from 'axios'
import * as cheerio from 'cheerio'
import { db } from '@/lib/db'

// const PRIMARY_ROOT = 'federal/us'
const PRIMARY_ROOT = 'california/supreme-court'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const checkUrlSet: string[] = []

    for (let i = 2008; i <= 2024; i++) {
      const url = `https://supreme.justia.com/cases/${PRIMARY_ROOT}/${i}`
      checkUrlSet.push(url)
    }

    const urlArray = await fetchHtmlInBatches(checkUrlSet, 20, fetchCaseUrls)
    const uniqueArray = Array.from(new Set(urlArray))

    const store = await fetchHtmlInBatches(uniqueArray, 20, processCaseHtml)

    res.status(200).json({ urlArray, store })
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch and parse the website.' })
  }
}

async function processCaseHtml({
  url,
  html
}: {
  url: string
  html: string
}): Promise<string[]> {
  try {
    console.log('Processing case html:', url)
    const urlProps = url.split(
      `https://supreme.justia.com/cases/${PRIMARY_ROOT}/`
    )
    const ref = urlProps[1]
    const $ = cheerio.load(html)
    const primaryContent = $('.primary-content')

    const title = $('.heading-1').text().trim()

    const elements = primaryContent.find(
      '.case-details .flex-col.width-20.reset-width-below-tablet.item'
    )
    let date: Date | null = null

    elements.each((index, element) => {
      const text = $(element).text()
      if (text.includes('argued:')) {
        const span = $(element).find('span')
        const dateString = span.text()
        date = new Date(dateString)
        return false
      }
    })

    const htmlbody = primaryContent.html() || ''
    const content = primaryContent.text().trim()

    const validate = await db.documentRecords.findFirst({
      where: {
        source: 'justicia',
        ref: ref
      }
    })

    if (!validate) {
      const store = await db.documentRecords.create({
        data: {
          source: 'justicia',
          ref: ref,
          title: title,
          meta: JSON.stringify({}),
          date: date,
          url: url,
          content: content,
          html: htmlbody
        }
      })

      console.log('Data stored for: ', store.id, store.title)
      return [store.ref]
    } else {
      const update = await db.documentRecords.update({
        where: {
          id: validate.id
        },
        data: {
          title: title,
          meta: JSON.stringify({}),
          date: date,
          content: content,
          html: htmlbody,
          region: 'US'
        }
      })
      console.log('Data updated for: ', update.id, update.title)
      return [update.ref]
    }
  } catch (error: any) {
    const urlProps = url.split(
      `https://supreme.justia.com/cases/${PRIMARY_ROOT}/`
    )
    const ref = urlProps[1]
    console.error('Error during request: ', error.message, '  > Ref:', ref)
    return ['!FAILED!']
  }
}

async function fetchCaseUrls({
  url,
  html
}: {
  url: string
  html: string
}): Promise<string[]> {
  const urlArray: string[] = []
  const $ = cheerio.load(html)
  const yearmap = url.split('/').pop()
  const caseLinks = $('a').filter((index, element) => {
    return !!$(element)
      .attr('href')
      ?.includes(`/cases/${PRIMARY_ROOT}/${yearmap}/`)
  })

  caseLinks.each((index, element) => {
    const href = $(element).attr('href')
    if (href) {
      urlArray.push('https://supreme.justia.com' + href)
    }
  })

  const uniqueArray = Array.from(new Set(urlArray))
  console.log('Found', uniqueArray.length, 'case links for', url)

  return uniqueArray
}

async function fetchHtmlInBatches(
  urls: string[],
  batchSize: number,
  batchProcess: (props: { url: string; html: string }) => Promise<string[]>
): Promise<string[]> {
  const batches = Math.ceil(urls.length / batchSize)
  let results: string[] = []

  for (let i = 0; i < batches; i++) {
    const batchUrls = urls.slice(i * batchSize, (i + 1) * batchSize)
    console.log('Fetching batch', i + 1, 'of', batches)

    const batchPromises = batchUrls.map((url) =>
      fetchAndProcessUrl(url, batchProcess)
    )

    const batchResults = await Promise.all(batchPromises)
    results = results.concat(
      batchResults.filter((result) => result[0] !== '').flat()
    )
  }
  return results
}

async function fetchAndProcessUrl(
  url: string,
  batchProcess: (payload: { url: string; html: string }) => Promise<string[]>
): Promise<string[]> {
  try {
    const response = await axios.get(url)
    const payload = {
      url: url,
      html: response.data
    }
    return await batchProcess(payload)
  } catch (error) {
    console.error(`Failed to fetch ${url}: ${error}`)
    return ['']
  }
}
