import * as z from 'zod'

import { getCurrentUserResponse } from '@/lib/session'
import { UnauthorizedError } from '@/lib/exceptions'
import { errorHandler } from '@/lib/exception-handler'
import { generateNewsletterEJSWithOpenAI } from '@/lib/newsletter-gpt'
import {
  deleteNewsletterById,
  updateNewsletterById,
  verifyCurrentUserHasAccessToNewsletter
} from '@/lib/recordstore-newsletter'
import {
  newsletterPatchSchema,
  newsletterPostSchema
} from '@/lib/validations/newsletter'

const routeContextSchema = z.object({
  params: z.object({
    newsletterId: z.string()
  })
})

export async function DELETE(
  req: Request,
  context: z.infer<typeof routeContextSchema>
) {
  try {
    // Validate the route params.
    const { params } = routeContextSchema.parse(context)
    const user = await getCurrentUserResponse()

    if (
      !(await verifyCurrentUserHasAccessToNewsletter(
        params.newsletterId,
        user.id
      ))
    ) {
      throw new UnauthorizedError()
    }

    await deleteNewsletterById(params.newsletterId as string)

    return new Response(null, { status: 204 })
  } catch (error) {
    return errorHandler(error)
  }
}

export async function PATCH(
  req: Request,
  context: z.infer<typeof routeContextSchema>
) {
  try {
    // Validate route params.
    const { params } = routeContextSchema.parse(context)

    const user = await getCurrentUserResponse()
    if (
      !(await verifyCurrentUserHasAccessToNewsletter(
        params.newsletterId,
        user.id
      ))
    ) {
      throw new UnauthorizedError()
    }

    const json = await req.json()
    const body = newsletterPatchSchema.parse(json)

    await updateNewsletterById({
      id: params.newsletterId,
      title: body.title,
      content: body.content
    })

    return new Response(null, { status: 200 })
  } catch (error) {
    return errorHandler(error)
  }
}

export async function POST(
  req: Request,
  context: z.infer<typeof routeContextSchema>
) {
  try {
    // Validate route params.
    const { params } = routeContextSchema.parse(context)

    const user = await getCurrentUserResponse()
    if (
      !(await verifyCurrentUserHasAccessToNewsletter(
        params.newsletterId,
        user.id
      ))
    ) {
      throw new UnauthorizedError()
    }

    const json = await req.json()
    const body = newsletterPostSchema.parse(json)

    const gptResponse = await generateNewsletterEJSWithOpenAI({
      newsletterConfig: {
        title: body.title,
        theme: body.theme,
        targetAudience: body.targetAudience,
        content: body.content,
        keyLegalUpdates: body.keyLegalUpdates.split(','),
        editorialTone: body.editorialTone
      }
    })

    if (gptResponse) {
      await updateNewsletterById({
        id: params.newsletterId,
        title: body.title,
        content: gptResponse
      })
    }

    return new Response(null, { status: 200 })
  } catch (error) {
    return errorHandler(error)
  }
}
