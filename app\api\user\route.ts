import * as z from 'zod'
import { userNameSchema } from '@/lib/validations/user'
import { getCurrentUserResponse } from '@/lib/session'
import { UnauthorizedError } from '@/lib/exceptions'
import { errorHandler } from '@/lib/exception-handler'
import { updateUser } from '@/lib/recordstore-user'

const routeContextSchema = z.object({
  params: z.object({
    userId: z.string()
  })
})

export async function PATCH(
  req: Request,
  context: z.infer<typeof routeContextSchema>
) {
  try {
    // Validate route params.
    // const { params } = routeContextSchema.parse(context)

    const user = await getCurrentUserResponse()
    if (!user) {
      throw new UnauthorizedError()
    }

    const json = await req.json()
    const body = userNameSchema.parse(json)

    await updateUser({
      id: user.id,
      name: body.name
    })

    return new Response(null, { status: 200 })
  } catch (error) {
    return errorHandler(error)
  }
}
