import { NextRequest, NextResponse } from 'next/server'
import { ChatOpenAI } from '@langchain/openai'
import { getCurrentUserResponse } from '@/lib/session'
import { UnauthorizedError } from '@/lib/exceptions'
import { db } from '@/lib/db'
import { GPTModel } from '@/types'
import {
  extractFileContentAndHtmlWithMammoth,
  fileToBuffer
} from '@/lib/file-handler'

export const maxDuration = 800

export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUserResponse()
    if (!user.id) {
      throw new UnauthorizedError()
    }

    const formData = await req.formData()
    const title = formData.get('title') as string
    const showCauseNotice = await fileToBuffer(
      formData.get('showCauseNotice') as File
    )
    const showCauseReply = await fileToBuffer(
      formData.get('showCauseReply') as File
    )

    const showCauseNoticeExtract = await extractFileContentAndHtmlWithMammoth({
      file: showCauseNotice
    })
    const showCauseReplyExtract = await extractFileContentAndHtmlWithMammoth({
      file: showCauseReply
    })

    const jsonModeModel = new ChatOpenAI({
      model: GPTModel.GPT4o
    }).bind({
      response_format: {
        type: 'json_object'
      }
    })

    const showCauseNoticePromise = jsonModeModel.invoke([
      [
        'system',
        `The following is a Show cause notice from Indian GST to a vendor. Review the document and take our key points that needs to be addressed by the vendor. Include at least 10 relevant points from it. Provide response in JSON array as:
        {
          "points": [
            "point1",
            "point2",
            "point3"
          ]
        }`
      ],
      ['human', showCauseNoticeExtract.textContent]
    ])

    const showCauseReplyPromise = jsonModeModel.invoke([
      [
        'system',
        `The following is a response by a vendor to a Show cause notice from Indian GST to vendor vendor. Review the document and take our key points that needs to be addressed by the vendor. Include at least 10 relevant points from it. Provide response in JSON array as:
        {
          "points": [
            "point1",
            "point2",
            "point3"
          ]
        }`
      ],
      ['human', showCauseReplyExtract.textContent]
    ])

    const [showCauseNoticeResponse, showCauseReplyResponse] = await Promise.all(
      [showCauseNoticePromise, showCauseReplyPromise]
    )

    let showCauseNoticeSummary = showCauseNoticeResponse.content as any
    let showCauseReplySummary = showCauseReplyResponse.content as any

    try {
      showCauseNoticeSummary = JSON.parse(showCauseNoticeSummary).points
      showCauseReplySummary = JSON.parse(showCauseReplySummary).points
    } catch (error: any) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    const create = await db.showCauseNotice.create({
      data: {
        title: title,
        showCauseNoticeSummary: JSON.stringify(showCauseNoticeSummary),
        showCauseReplySummary: JSON.stringify(showCauseReplySummary),
        teamId: user.teamId,
        orderLength: 5,
        status: 'uploaded'
      }
    })

    return NextResponse.json({ id: create.id }, { status: 200 })
  } catch (e: any) {
    console.log(e)

    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}
