'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Icons } from '@/components/elements/icons'
import { Button } from '@/components/ui/button'
import { ChevronRight } from 'lucide-react'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip'

import type { SidebarNavItem } from '@/types'

interface DashboardNavProps {
  items: SidebarNavItem[]
}

export function DashboardNav({ items }: DashboardNavProps) {
  const path = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(true) // Default collapsed
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024)
      if (window.innerWidth < 1024) {
        setIsCollapsed(true)
      }
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  if (!items?.length) {
    return null
  }

  return (
    <aside
      className={cn(
        'sticky top-[64px] h-[calc(100vh-64px)] bg-background border-r transition-all duration-300 z-30',
        isCollapsed ? 'w-16' : 'w-[200px]'
      )}
    >
      <div className="flex h-full flex-col">
        {/* Toggle button */}
        <div className="flex items-center justify-end p-2">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            <ChevronRight
              className={cn(
                'h-4 w-4 transition-transform',
                !isCollapsed && 'rotate-180'
              )}
            />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 space-y-1 px-2">
          <TooltipProvider delayDuration={0}>
            {items.map((item, index) => {
              const Icon = Icons[item.icon || 'arrowRight']
              const isActive = path === item.href

              if (!item.href) return null

              const navLink = (
                <Link
                  key={index}
                  href={item.disabled ? '#' : item.href}
                  className={cn(
                    'flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-muted',
                    isActive && 'bg-muted text-primary',
                    item.disabled && 'cursor-not-allowed opacity-50'
                  )}
                  onClick={(e) => {
                    if (item.disabled) e.preventDefault()
                  }}
                >
                  <Icon
                    className={cn(
                      'h-4 w-4 flex-shrink-0',
                      isActive && 'text-primary'
                    )}
                  />
                  {!isCollapsed && (
                    <span className="truncate">{item.title}</span>
                  )}
                </Link>
              )

              if (isCollapsed) {
                return (
                  <Tooltip key={index}>
                    <TooltipTrigger asChild>{navLink}</TooltipTrigger>
                    <TooltipContent
                      side="right"
                      className="flex items-center gap-4"
                    >
                      {item.title}
                    </TooltipContent>
                  </Tooltip>
                )
              }

              return navLink
            })}
          </TooltipProvider>
        </nav>
      </div>
    </aside>
  )
}
