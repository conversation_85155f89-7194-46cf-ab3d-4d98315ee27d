import { NextRequest, NextResponse } from 'next/server'
import { UnauthorizedError } from '@/lib/exceptions'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { injestDocumentBuffer } from '@/lib/actions/injest'
import { developer } from '@/lib/utils'
import { mapDocumentToCase } from '@/lib/recordstore-case'

export const maxDuration = 800

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions)
  if (!(session && session.user)) {
    throw new UnauthorizedError()
  }

  try {
    const user = session.user
    const body = await req.json()
    const url_data: {
      url: string
      name: string
      size: number
    } = body.url_data
    const binderId = body.binderId

    // Download the PDF file from the URL
    const response = await fetch(url_data.url)
    if (!response.ok) {
      throw new Error(`Failed to fetch the PDF from URL: ${url_data.url}`)
    }

    const arrayBuffer = await response.arrayBuffer()

    developer.log(['Fetched Buffer from URL:', url_data.name])

    const create = await injestDocumentBuffer({
      file: {
        name: url_data.name,
        type: 'application/pdf',
        buffer: arrayBuffer
      },
      user
    })
    if (create?.id) {
      if (binderId && binderId !== '') {
        await mapDocumentToCase({
          documentId: create.id,
          binderId: binderId,
          userId: user.id
        })
      }
      return NextResponse.json({ ok: true }, { status: 200 })
    } else {
      throw new Error('Failed to create document')
    }
  } catch (e: any) {
    console.log(e)
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}
