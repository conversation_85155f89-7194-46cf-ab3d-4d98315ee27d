// event-based-medcron.ts
'use server'

import { CaseFileType, DocumentEvent, QueuedEventStatus } from '@prisma/client'
import { db } from '../../../db'
import { UnauthorizedError } from '../../../exceptions'
import { getCurrentUser } from '../../../session'
import pLimit from 'p-limit'
import { createGeminiCompletion } from '@/lib/services/gemini-service'
import { createAzureCompletion } from '@/lib/services/azure-openai-service'
import { AuthUser } from 'next-auth'
import { plaintiffInfoSchema } from './config'
import { GeminiModel, GPTModel } from '@/types'
import { MedicalChronologyStrategyFormData } from '@/components/elements/forms/medical-chronology-strategy-form'

const MAX_CONCURRENT_REQUESTS = 2 // Limit concurrent requests to avoid rate limiting issues
const CHUNK_SIZE = 2000 // characters per chunk for processing
const MAX_CONSECUTIVE_ERRORS = 20

// Memory optimization constants
const DOCUMENT_BATCH_SIZE = 5 // Process documents in batches to avoid memory overload
const CHUNK_BATCH_SIZE = 20 // Process chunks in batches of 20
const MAX_PARALLEL_CHUNK_BATCHES = 10 // Maximum chunk batches running in parallel
const LATEST_EVENTS_MEMORY_LIMIT = 5 // Keep only recent events for context to manage memory

// Error collector utility
function createErrorCollector(): ErrorCollector {
  return {
    errors: [],
    consecutiveErrors: 0,
    hasError: false,
    addError(error: string, context?: string) {
      const errorMessage = context ? `[${context}] ${error}` : error
      this.errors.push(errorMessage)
      this.consecutiveErrors++
      this.hasError = true
      console.error(`❌ Error #${this.consecutiveErrors}: ${errorMessage}`)
    },
    reset() {
      this.consecutiveErrors = 0
    },
    shouldExitProcess() {
      return this.consecutiveErrors >= MAX_CONSECUTIVE_ERRORS
    },
    getErrorSummary() {
      return {
        totalErrors: this.errors.length,
        consecutiveErrors: this.consecutiveErrors,
        recentErrors: this.errors.slice(-10),
        shouldExit: this.shouldExitProcess()
      }
    }
  }
}

interface ExtractedEvent {
  event: string
  eventType: string
  eventDescription: string
  timestamp: string
  estimatedTime: boolean
  confidence: number
  pageReference?: string
}

interface PlaintiffInfo {
  fullName: string
  dateOfBirth: string
  age?: number
  gender?: string
  occupation?: string
  preExistingConditions: string[]
  primaryInjuries: string[]
  incidentDate: string
  incidentDescription: string
}

interface ErrorCollector {
  errors: string[]
  consecutiveErrors: number
  hasError: boolean
  addError: (error: string, context?: string) => void
  reset: () => void
  shouldExitProcess: () => boolean
  getErrorSummary: () => {
    totalErrors: number
    consecutiveErrors: number
    recentErrors: string[]
    shouldExit: boolean
  }
}

// Main orchestration function
export async function requestEventBasedMedicalChronology(
  binderId: string,
  selectedDocumentsByType: Record<string, string[]>,
  strategyInputs: Partial<{
    treatmentTimelineGuidelines?: string | undefined
    prospectiveCareGuidelines?: string | undefined
  }>,
  options?: {
    forceRegenerate?: boolean // Force regeneration of all events, ignoring existing ones
  }
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    // Step 0: Clean up existing records
    // console.log('🧹 Cleaning up existing records...')
    // await cleanupExistingRecords(
    //   binderId,
    //   user.teamId,
    //   options?.forceRegenerate || false
    // ) // Force cleanup if requested

    // Create queue entry for event extraction
    const queue = await db.queuedEventProcess.create({
      data: {
        type: 'event-based-medical-chronology',
        payload: JSON.stringify({
          user,
          payload: { binderId, selectedDocumentsByType, strategyInputs },
          options
        })
      }
    })

    return {
      success: true,
      queueId: queue.id
    }
  } catch (error) {
    console.error('Failed to request event-based medical chronology:', error)
    return { success: false, error: 'Failed to queue chronology generation' }
  }
}

// Background processor
export async function processEventBasedMedicalChronology({
  processId,
  user,
  payload: { binderId, selectedDocumentsByType, strategyInputs },
  options
}: {
  processId: string
  user: AuthUser
  payload: {
    binderId: string
    selectedDocumentsByType: Record<string, string[]>
    strategyInputs: Partial<{
      treatmentTimelineGuidelines?: string | undefined
      prospectiveCareGuidelines?: string | undefined
    }>
  }
  options?: {
    forceRegenerate?: boolean
  }
}) {
  console.log(`🔍 Starting event-based chronology for binder: ${binderId}`)

  const errorCollector = createErrorCollector()

  // Helper function to update queue status with errors
  const updateQueueStatus = async (
    status: QueuedEventStatus,
    additionalData?: any
  ) => {
    const errorSummary = errorCollector.getErrorSummary()
    await db.queuedEventProcess.update({
      where: { id: processId },
      data: {
        status,
        response: JSON.stringify({
          completedAt:
            status === QueuedEventStatus.completed
              ? new Date().toISOString()
              : undefined,
          errorSummary: errorCollector.hasError ? errorSummary : undefined,
          error:
            status === QueuedEventStatus.failed
              ? additionalData?.error || 'Pipeline failed with errors'
              : undefined,
          ...additionalData
        })
      }
    })
  }

  try {
    // Step 1: Extract events from all documents
    console.log('📄 Extracting events from documents...')
    try {
      await extractEventsFromDocuments(
        binderId,
        selectedDocumentsByType,
        errorCollector,
        strategyInputs,
        user
      )
      if (errorCollector.shouldExitProcess()) {
        throw new Error(
          `Too many consecutive errors (${errorCollector.consecutiveErrors}) during document extraction`
        )
      }
    } catch (error) {
      console.error('❌ Major failure in document extraction:', error)
      await updateQueueStatus(QueuedEventStatus.failed, {
        error:
          error instanceof Error
            ? error.message
            : 'Unknown error in document extraction',
        stage: 'document_extraction'
      })
      return
    }

    // Step 2: Deduplicate events
    console.log('🔄 Deduplicating events...')
    try {
      await deduplicateEvents(binderId, errorCollector, strategyInputs, user)
      if (errorCollector.shouldExitProcess()) {
        throw new Error(
          `Too many consecutive errors (${errorCollector.consecutiveErrors}) during deduplication`
        )
      }

      await deduplicateEvents(binderId, errorCollector, strategyInputs, user)
      if (errorCollector.shouldExitProcess()) {
        throw new Error(
          `Too many consecutive errors (${errorCollector.consecutiveErrors}) during second deduplication pass`
        )
      }
    } catch (error) {
      console.error('❌ Major failure in deduplication:', error)
      await updateQueueStatus(QueuedEventStatus.failed, {
        error:
          error instanceof Error
            ? error.message
            : 'Unknown error in deduplication',
        stage: 'deduplication'
      })
      return
    }

    // Step 3: Generate plaintiff info
    console.log('👤 Extracting plaintiff information...')
    let plaintiffInfo: PlaintiffInfo
    try {
      plaintiffInfo = await extractPlaintiffInfo(binderId, errorCollector, user)
    } catch (error) {
      console.error('❌ Major failure in plaintiff info extraction:', error)
      await updateQueueStatus(QueuedEventStatus.failed, {
        error:
          error instanceof Error
            ? error.message
            : 'Unknown error in plaintiff info extraction',
        stage: 'plaintiff_extraction'
      })
      return
    }

    // Step 4: Generate reports from structured data
    console.log('📊 Generating medical chronology reports...')
    try {
      await generateChronologyReports(
        binderId,
        plaintiffInfo,
        user,
        errorCollector,
        strategyInputs
      )
    } catch (error) {
      console.error('❌ Major failure in report generation:', error)
      await updateQueueStatus(QueuedEventStatus.failed, {
        error:
          error instanceof Error
            ? error.message
            : 'Unknown error in report generation',
        stage: 'report_generation'
      })
      return
    }

    // Step 5: Record credit usage
    try {
      await db.teamCreditUsed.create({
        data: {
          teamId: user.teamId,
          type: 'case',
          refId: binderId,
          eventId: new Date().getTime().toString()
        }
      })
    } catch (error) {
      console.error('❌ Failed to record credit usage:', error)
      errorCollector.addError(
        error instanceof Error
          ? error.message
          : 'Unknown error recording credit',
        'credit_usage'
      )
      // Don't fail the entire process for credit recording issues
    }

    await updateQueueStatus(QueuedEventStatus.completed)

    const errorSummary = errorCollector.getErrorSummary()
    if (errorCollector.hasError) {
      console.log(
        `✅ Event-based chronology generation completed with ${errorSummary.totalErrors} errors`
      )
    } else {
      console.log('✅ Event-based chronology generation completed successfully')
    }
  } catch (error) {
    console.error('❌ Unexpected error in main process:', error)

    await updateQueueStatus(QueuedEventStatus.failed, {
      error:
        error instanceof Error
          ? error.message
          : 'Unexpected error in main process',
      stage: 'main_process'
    })
  }
}

// Step 0: Clean up existing records for the binder
async function cleanupExistingRecords(
  binderId: string,
  teamId: string,
  forceCleanup: boolean = false
) {
  if (forceCleanup) {
    // Delete existing document events for this binder
    await db.documentEvent.deleteMany({
      where: { binderId }
    })
    console.log(
      `🧹 Force cleanup: Deleted all existing document events for binder: ${binderId}`
    )
  } else {
    // Only delete processed chronology records, keep document events for reuse
    console.log(`🔄 Preserving existing document events for reuse`)
  }

  // Always delete existing processed chronology records for this binder
  await db.processedChronology.deleteMany({
    where: { binderId }
  })

  console.log(
    `✅ Cleaned up existing chronology records for binder: ${binderId}`
  )
}

// Step 1: Extract events from documents
async function extractEventsFromDocuments(
  binderId: string,
  selectedDocumentsByType: Record<string, string[]>,
  errorCollector: ErrorCollector,
  strategyInputs: Partial<{
    treatmentTimelineGuidelines?: string | undefined
    prospectiveCareGuidelines?: string | undefined
  }>,
  user: AuthUser
) {
  const allDocumentIds = Object.values(selectedDocumentsByType)
    .flat()
    .map(Number)

  // Check existing events status first (without loading document content)
  const eventsStatus = await checkExistingEventsStatus(binderId, allDocumentIds)

  console.log(
    `📊 Events Status: ${eventsStatus.documentsWithEvents.length}/${allDocumentIds.length} documents have events`
  )
  console.log(
    `📊 Total existing events: ${eventsStatus.totalExistingEvents} (${eventsStatus.totalProcessedEvents} processed)`
  )

  if (eventsStatus.documentsWithoutEvents.length === 0) {
    console.log(
      `✅ All documents already have events stored, skipping extraction phase`
    )
    return
  }

  console.log(
    `📄 Processing ${eventsStatus.documentsWithoutEvents.length} documents without existing events`
  )
  logMemoryUsage('Document processing start')

  // Process documents in batches to avoid memory overload
  const documentIdBatches = chunkArray(
    eventsStatus.documentsWithoutEvents,
    DOCUMENT_BATCH_SIZE
  )

  for (
    let batchIndex = 0;
    batchIndex < documentIdBatches.length;
    batchIndex++
  ) {
    const currentBatch = documentIdBatches[batchIndex]
    console.log(
      `� Processing document batch ${batchIndex + 1}/${documentIdBatches.length} (${currentBatch.length} documents)`
    )

    // Load only the current batch of documents
    const documents = await db.documentRecords.findMany({
      where: { id: { in: currentBatch } },
      select: { id: true, title: true, content: true }
    })

    const limit = pLimit(Math.min(MAX_CONCURRENT_REQUESTS, documents.length))
    const extractionPromises: Promise<void>[] = []

    for (const doc of documents) {
      extractionPromises.push(
        limit(async () => {
          try {
            await processDocumentForEvents(
              binderId,
              doc,
              errorCollector,
              strategyInputs,
              user
            )
            errorCollector.reset() // Reset consecutive counter on success
          } catch (error) {
            errorCollector.addError(
              error instanceof Error ? error.message : 'Unknown error',
              `document-${doc.id}`
            )
            if (errorCollector.shouldExitProcess()) {
              throw new Error(
                `Too many consecutive errors processing documents`
              )
            }
          }
        })
      )
    }

    await Promise.all(extractionPromises)

    // Force garbage collection between batches if available
    if (global.gc) {
      global.gc()
    }

    logMemoryUsage(`Document batch ${batchIndex + 1} completed`)
    console.log(
      `✅ Completed document batch ${batchIndex + 1}/${documentIdBatches.length}`
    )
  }
}

// Process single document into events
async function processDocumentForEvents(
  binderId: string,
  document: { id: number; title: string; content: string },
  errorCollector: ErrorCollector,
  strategyInputs: Partial<{
    treatmentTimelineGuidelines?: string | undefined
    prospectiveCareGuidelines?: string | undefined
  }>,
  user: AuthUser
) {
  // Double-check if events already exist for this document
  const existingEventCount = await db.documentEvent.count({
    where: {
      binderId,
      documentId: document.id
    }
  })

  if (existingEventCount > 0) {
    console.log(
      `⏭️ Document ${document.id} already has ${existingEventCount} events stored, skipping`
    )
    return
  }

  console.log(`📄 Processing document ${document.id}: ${document.title}`)
  const chunks = splitDocumentIntoChunks(document.content)
  let latestEvents: ExtractedEvent[] = []

  console.log(`📊 Document split into ${chunks.length} chunks`)
  logMemoryUsage(`Document ${document.id} chunking`)

  // Process chunks in batches of 20, with up to 10 batches running in parallel
  const chunkBatches = chunkArray(chunks, CHUNK_BATCH_SIZE)
  console.log(
    `🔄 Processing ${chunkBatches.length} chunk batches for document ${document.id}`
  )

  const batchLimit = pLimit(MAX_PARALLEL_CHUNK_BATCHES)
  const batchPromises: Promise<void>[] = []

  for (let batchIndex = 0; batchIndex < chunkBatches.length; batchIndex++) {
    batchPromises.push(
      batchLimit(async () => {
        const currentChunkBatch = chunkBatches[batchIndex]
        console.log(
          `🔍 Processing chunk batch ${batchIndex + 1}/${chunkBatches.length} (${currentChunkBatch.length} chunks) for document ${document.id}`
        )

        // Process chunks sequentially within each batch to maintain context
        for (let i = 0; i < currentChunkBatch.length; i++) {
          const chunk = currentChunkBatch[i]
          const globalChunkIndex = batchIndex * CHUNK_BATCH_SIZE + i
          const pageRange = calculatePageRange(globalChunkIndex, chunks.length)

          try {
            const events = await extractEventsFromChunk(
              chunk,
              document.title,
              latestEvents.slice(-5), // Pass only last 5 events for context
              strategyInputs,
              user
            )

            if (events.length > 0) {
              events.forEach((event) => {
                if (Date.parse(event.timestamp)) {
                  latestEvents.push(event)
                }
              })
              // Keep only recent events for context to manage memory
              if (latestEvents.length > LATEST_EVENTS_MEMORY_LIMIT) {
                latestEvents = latestEvents.slice(-LATEST_EVENTS_MEMORY_LIMIT)
              }
            }

            console.log(
              `✅ Extracted ${events.length} events from chunk ${globalChunkIndex + 1}/${chunks.length} (batch ${batchIndex + 1})`
            )

            // Store events in database
            for (const event of events) {
              try {
                await db.documentEvent.create({
                  data: {
                    binderId,
                    documentId: document.id,
                    pageRange,
                    event: event.event,
                    eventType: event.eventType,
                    eventDescription: event.eventDescription,
                    timestamp: new Date(event.timestamp),
                    estimatedTime: event.estimatedTime,
                    rawExtractedData: JSON.stringify(event),
                    processed: false
                  }
                })
                errorCollector.reset() // Reset consecutive counter on successful DB write
              } catch (error) {
                const errorMsg = `Failed to store documentEvent for doc ${document.id}, event: ${event.event}`
                errorCollector.addError(
                  error instanceof Error ? error.message : 'Unknown DB error',
                  `store-event-${document.id}`
                )
                console.log(errorMsg, {
                  binderId,
                  documentId: document.id,
                  pageRange,
                  event: event.event,
                  eventType: event.eventType,
                  eventDescription: event.eventDescription,
                  timestamp: new Date(event.timestamp),
                  estimatedTime: event.estimatedTime,
                  rawExtractedData: JSON.stringify(event),
                  processed: false
                })
              }
            }
            errorCollector.reset() // Reset consecutive counter on successful chunk processing
          } catch (error) {
            const errorMsg = `Error processing chunk ${globalChunkIndex + 1} of document ${document.id}`
            errorCollector.addError(
              error instanceof Error ? error.message : 'Unknown error',
              `chunk-${document.id}-${globalChunkIndex}`
            )
            console.error(errorMsg, error)

            if (errorCollector.shouldExitProcess()) {
              throw new Error(
                `Too many consecutive errors processing chunks for document ${document.id}`
              )
            }
          }
        }

        console.log(
          `✅ Completed chunk batch ${batchIndex + 1}/${chunkBatches.length} for document ${document.id}`
        )
      })
    )
  }

  await Promise.all(batchPromises)
  console.log(`✅ Completed processing all chunks for document ${document.id}`)
}

// Extract events from text chunk using LLM
async function extractEventsFromChunk(
  chunk: string,
  documentTitle: string,
  latestEvents: ExtractedEvent[],
  strategyInputs: Partial<{
    treatmentTimelineGuidelines?: string | undefined
    prospectiveCareGuidelines?: string | undefined
  }>,
  user: AuthUser
): Promise<ExtractedEvent[]> {
  const prompt = `Extract medical and legally relevant events from this document chunk for mass tort litigation and demand letter preparation. Focus on events that demonstrate injury causation, medical damages, pain and suffering, and liability.

IMPORTANT - EVENT CONSOLIDATION PRINCIPLE:
Consolidate multiple related activities that occur at the same time and location into a single comprehensive event. For example:
- If multiple medications are prescribed/purchased at one pharmacy visit → Single "Medication pickup" event listing all medications
- If multiple tests are performed during one medical appointment → Single "Diagnostic testing" event describing all tests
- If multiple treatments are received in one therapy session → Single "Therapy session" event covering all treatments
- If multiple procedures are done during one surgery → Single "Surgical procedure" event detailing all procedures

Only create separate events when:
- Activities occur at different times (even same day)
- Activities occur at different locations
- Activities involve different medical providers/facilities
- Activities are fundamentally different types (e.g., diagnostic vs treatment)

Document: ${documentTitle}
Content: ${chunk}

${latestEvents.length > 0 ? 'Recent events:\n' + latestEvents.map((e) => `- ${e.event} (${e.timestamp})`).join('\n') : 'No recent events.'}

${
  strategyInputs?.treatmentTimelineGuidelines?.trim()
    ? `
STRATEGY GUIDANCE - Treatment Timeline Guidelines:
${strategyInputs.treatmentTimelineGuidelines}

Follow these specific treatment timeline guidelines when extracting events. Pay special attention to the focus areas and time periods mentioned above.
`
    : ''
}

${
  strategyInputs?.prospectiveCareGuidelines?.trim()
    ? `
STRATEGY GUIDANCE - Prospective Care & Follow-Up Guidelines:
${strategyInputs.prospectiveCareGuidelines}

When extracting events related to future care, recommendations, or follow-up treatment, prioritize according to these prospective care guidelines.
`
    : ''
}

Return JSON array of events with this structure:
{
  "events": [
    {
      "event": "Comprehensive description of what happened (include all related activities)",
      "eventType": "medical_treatment|emergency_visit|diagnostic_test|injury_report|police_report|medication|surgery|therapy|expert_opinion|legal_consultation|disability_assessment|work_restriction|pain_documentation",
      "eventDescription": "Detailed description including ALL activities, treatments, prescriptions, tests, or procedures that occurred during this consolidated event. List specific medications, test names, treatment modalities, etc.",
      "timestamp": "YYYY-MM-DD HH:MM",
      "estimatedTime": true/false,
      "confidence": 0.1-1.0,
      "pageReference": "page number if available"
    }
  ]
}

CONSOLIDATED EVENT EXTRACTION GUIDELINES:

Consolidation Rules:
- Group all activities that happen during the same medical appointment/visit
- Combine all medications prescribed or picked up during a single pharmacy visit
- Merge all diagnostic tests performed during one testing session
- Consolidate all treatments received in a single therapy session
- Combine all procedures performed during one surgical operation
- Group all discussions/recommendations made during one consultation

Event Description Requirements:
- List ALL specific medications with dosages when multiple are involved
- Detail ALL tests performed (blood work, imaging, etc.) in one visit
- Describe ALL treatments received (physical therapy exercises, injections, etc.)
- Include ALL procedures performed during surgery (repair, removal, insertion, etc.)
- Mention ALL providers seen during a single facility visit

CONSOLIDATION EXAMPLES:

Example 1 - Pharmacy Visit:
❌ Wrong: Create separate events for "Picked up Oxycodone", "Picked up Ibuprofen", "Picked up Gabapentin"
✅ Correct: Single event "Medication pickup at CVS Pharmacy - Oxycodone 10mg, Ibuprofen 800mg, Gabapentin 300mg prescribed by Dr. Smith for post-surgical pain management"

Example 2 - Medical Appointment:
❌ Wrong: Separate events for "Blood test", "X-ray", "Doctor consultation", "Physical exam"
✅ Correct: Single event "Medical appointment with Dr. Johnson - Physical examination, blood work (CBC, metabolic panel), chest X-ray, discussed treatment options and pain management"

Example 3 - Surgery:
❌ Wrong: Separate events for "Anesthesia", "Incision", "Tendon repair", "Wound closure"
✅ Correct: Single event "Surgical repair procedure - Tendon reconstruction with graft placement, arthroscopic debridement, and suture anchor fixation under general anesthesia"

Legal Extraction Guidelines:
- Extract events demonstrating injury severity and ongoing medical needs for damage calculations
- Include pain documentation, functional limitations, and quality of life impacts
- Capture work restrictions, disability assessments, and economic losses
- Document treatment necessity and medical professional opinions
- Include expert medical opinions and prognosis statements
- Record psychological/emotional impacts and mental health treatment
- Note pre-existing condition discussions and aggravation claims
- Extract insurance denials, coverage issues, and financial hardships
- Include family impact statements and caregiver burden
- Document missed life events, social impacts, and lifestyle changes

Medical Focus Areas:
- Progressive worsening of conditions
- Failed treatments requiring escalation
- Chronic pain management attempts
- Surgical interventions and complications
- Permanent impairments and future care needs
- Medication dependencies and side effects

Date Requirements:
- Only extract events with identifiable dates in ISO format (YYYY-MM-DD HH:MM) parsable with Date.parse()
- For estimated dates, use context clues and mark estimatedTime: true
- DO NOT USE PLACEHOLDER DATES - use actual estimated dates based on context
- If exact time unknown, estimate or use 12:00 and mark estimatedTime: true

FINAL REMINDER - CONSOLIDATE BEFORE EXTRACTING:
Before creating your JSON response, review all potential events and consolidate those that occurred at the same time and place. Aim for fewer, more comprehensive events rather than many small, fragmented ones. Each event should tell a complete story of what happened during that specific time period at that location.`

  const response = await createAzureCompletion({
    messages: [
      {
        role: 'system',
        content: prompt
      }
    ],
    model: GPTModel.GPTo4Mini,
    json: true,
    teamId: user.teamId,
    purpose: 'med-cron',
    activity: 'event-extraction'
  })

  try {
    const parsed = response as { events?: ExtractedEvent[] }
    return parsed.events || []
  } catch (error) {
    console.error('Failed to parse events from LLM response:', error)
    console.log('>>>>>>>>>>>>>>>>>>>>>')
    console.log(response)
    console.log('>>>>>>>>>>>>>>>>>>>>>')

    return []
  }
}

// Step 2: Deduplicate events
export async function deduplicateEvents(
  binderId: string,
  errorCollector: ErrorCollector,
  strategyInputs: MedicalChronologyStrategyFormData,
  user: AuthUser
) {
  const BATCH_SIZE = 200
  let skip = 0

  const limit = pLimit(20) // Conservative for deduplication

  while (true) {
    const events = await db.documentEvent.findMany({
      where: { binderId, processed: false },
      orderBy: { timestamp: 'asc' },
      skip,
      take: BATCH_SIZE
    })

    if (events.length === 0) {
      console.log('✅ No more unprocessed events to deduplicate')
      break
    }

    const processingBatches = chunkArray(events, 30) // Process in smaller batches

    for (const batch of processingBatches) {
      await limit(async () => {
        try {
          await deduplicateBatch(batch, errorCollector, strategyInputs, user)
          errorCollector.reset() // Reset consecutive counter on success
        } catch (error) {
          errorCollector.addError(
            error instanceof Error ? error.message : 'Unknown error',
            `deduplicate-batch-${batch.length}`
          )
          if (errorCollector.shouldExitProcess()) {
            throw new Error(`Too many consecutive errors in deduplication`)
          }
        }
      })
    }

    skip += BATCH_SIZE
  }
}

// Deduplicate a batch of events
export async function deduplicateBatch(
  events: any[],
  errorCollector: ErrorCollector,
  strategyInputs: Partial<{
    treatmentTimelineGuidelines?: string | undefined
    prospectiveCareGuidelines?: string | undefined
  }>,
  user: AuthUser
) {
  if (events.length < 2) return

  const prompt = `Analyze these medical and legal events for duplicates in the context of mass tort litigation. Events from different documents describing the same occurrence should be merged to strengthen the legal narrative and avoid redundancy in demand letter preparation.

IMPORTANT - CONSOLIDATION-AWARE DEDUPLICATION:
Since events are already consolidated by time/location during extraction, focus on identifying duplicates that describe:
- The same appointment/visit described in multiple documents
- The same medical procedure mentioned in different records
- The same medication pickup referenced across documents
- The same consultation described from different perspectives

DO NOT merge events that represent genuinely different occasions, even if similar in nature (e.g., separate therapy sessions on different dates).

CONSOLIDATION LOGIC REMINDER:
When merging duplicate events, apply the same consolidation principles used during extraction:
- Combine all activities that happened at the same time and location
- Include ALL medications, tests, treatments, or procedures in the merged description
- Preserve specific details like dosages, test names, provider names
- Create comprehensive descriptions that tell the complete story

${
  strategyInputs?.treatmentTimelineGuidelines?.trim()
    ? `
STRATEGY GUIDANCE - Treatment Timeline Focus:
${strategyInputs.treatmentTimelineGuidelines}

When merging duplicate events, prioritize information that supports the treatment timeline strategy above. Ensure merged descriptions emphasize the strategic focus areas and maintain timeline coherence. If the guidelines mention specific time periods or treatment types focus, ensure those are highlighted in the merged event descriptions. If the guideline mentions specific types of treatment or care, ensure those are highlighted in the output. If guideline mentiones to exclude certain types of events or date range, ensure those are not included in the output.
`
    : ''
}

${
  strategyInputs?.prospectiveCareGuidelines?.trim()
    ? `
STRATEGY GUIDANCE - Prospective Care Strategy:
${strategyInputs.prospectiveCareGuidelines}

When merging events related to ongoing or future care, ensure the merged descriptions highlight prospective care elements outlined above. Emphasize forward-looking treatment plans and recommendations.
`
    : ''
}

Events:
${JSON.stringify(
  events.map((e) => ({
    id: e.id,
    event: e.event,
    eventType: e.eventType,
    timestamp: e.timestamp,
    description: e.eventDescription,
    documentId: e.documentId
  })),
  null,
  2
)}

Return JSON with:
{
  "duplicateGroups": [
    {
      "keepEventId": "id_to_keep",
      "mergeEventIds": ["id1", "id2"],
      "mergedEvent": "combined description emphasizing legal significance",
      "mergedDescription": "comprehensive description highlighting medical necessity and legal damages"
    }
  ],
  "uniqueEventIds": ["id1", "id2"]
}

Legal Deduplication Rules:
- Merge events describing the same medical occurrence but preserve legal significance
- Prioritize events with stronger legal language and damage documentation
- When merging, create comprehensive descriptions that include ALL activities from the duplicate events
- Combine pain descriptions, functional limitations, and impact statements
- Preserve expert medical opinions and prognosis statements
- Maintain causation language linking injuries to incident
- Keep most detailed descriptions of economic losses and work restrictions
- Combine family impact and lifestyle change documentation
- Preserve insurance and coverage denial information
- Include ALL medications, dosages, tests, and procedures in merged descriptions
- Maintain provider names, facility details, and specific medical terminology
- Ensure merged events tell the complete story of what happened during that time period`

  try {
    console.log(`🔄 Deduplicating ${events.length} events...`)

    const response = await createAzureCompletion({
      messages: [
        {
          role: 'system',
          content: prompt
        }
      ],
      model: GPTModel.GPTo4Mini,
      json: true,
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'event-deduplication'
    })

    const result = response as {
      duplicateGroups?: {
        keepEventId: string
        mergeEventIds: string[]
        mergedEvent: string
        mergedDescription: string
      }[]
      uniqueEventIds?: string[]
    }

    // filter out any empty or invalid results
    result.duplicateGroups =
      result.duplicateGroups?.filter(
        (group) =>
          group.keepEventId &&
          group.mergeEventIds.length > 0 &&
          group.mergedEvent &&
          group.mergedDescription
      ) || []

    // Process duplicate groups
    for (const group of result.duplicateGroups || []) {
      try {
        // Update the kept event with merged information
        await db.documentEvent.update({
          where: { id: group.keepEventId },
          data: {
            event: group.mergedEvent,
            eventDescription: group.mergedDescription,
            processed: true
          }
        })

        // Delete duplicate events
        await db.documentEvent.deleteMany({
          where: { id: { in: group.mergeEventIds } }
        })
        errorCollector.reset() // Reset on successful DB operation
      } catch (error) {
        errorCollector.addError(
          error instanceof Error ? error.message : 'Unknown DB error',
          `merge-group-${group.keepEventId}`
        )
      }
    }

    await db.documentEvent.updateMany({
      where: { id: { in: events.map((e) => e.id) } },
      data: { processed: true }
    })

    errorCollector.reset() // Reset on successful completion
  } catch (error) {
    errorCollector.addError(
      error instanceof Error ? error.message : 'Unknown error',
      'deduplication-llm'
    )
    console.error('Error in deduplication:', error)
    // Mark all as processed to avoid infinite loops
    try {
      await db.documentEvent.updateMany({
        where: { id: { in: events.map((e) => e.id) } },
        data: { processed: true }
      })
    } catch (dbError) {
      errorCollector.addError(
        dbError instanceof Error ? dbError.message : 'Unknown DB error',
        'mark-processed-fallback'
      )
    }
  }
}

// Step 3: Extract plaintiff information
async function extractPlaintiffInfo(
  binderId: string,
  errorCollector: ErrorCollector,
  user: AuthUser
): Promise<PlaintiffInfo> {
  // Get a sample of events and document content for plaintiff info extraction
  const events = await db.documentEvent.findMany({
    where: { binderId, processed: true },
    orderBy: { timestamp: 'asc' }
  })

  const contextData = {
    events: events.map((e) => ({
      event: e.event,
      eventType: e.eventType,
      timestamp: e.timestamp,
      description: e.eventDescription
    }))
  }

  const prompt = `Extract comprehensive plaintiff information from medical/legal events and documents for mass tort demand letter preparation. Focus on information that supports damage claims and establishes legal standing.

Data: ${JSON.stringify(contextData)}

Return JSON:
{
  "fullName": "patient full name",
  "dateOfBirth": "YYYY-MM-DD or 'Unknown'",
  "age": number_or_null,
  "gender": "M/F/Unknown",
  "occupation": "specific occupation and work status",
  "preExistingConditions": ["condition1 with severity assessment", "condition2"],
  "primaryInjuries": ["injury1 with legal significance", "injury2 with permanency"],
  "incidentDate": "YYYY-MM-DD of main incident causing injuries",
  "incidentDescription": "detailed incident description emphasizing defendant liability and plaintiff's innocence"
}

Legal Context Extraction:
- Occupation: Include job requirements, physical demands, and earning capacity
- Pre-existing conditions: Note severity and distinguish from incident-related aggravation
- Primary injuries: Emphasize permanency, functional impact, and ongoing treatment needs
- Incident description: Focus on causation, liability factors, and plaintiff's innocence
- Include economic status and financial impact potential
- Note family responsibilities and dependents
- Document quality of life before incident for comparison`

  try {
    const response = await createGeminiCompletion({
      systemInstruction: prompt,
      message: 'Extract plaintiff demographic and medical information',
      config: plaintiffInfoSchema,
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'plaintiff-info-extraction'
    })

    return response as PlaintiffInfo
  } catch (error) {
    errorCollector.addError(
      error instanceof Error ? error.message : 'Unknown error',
      'plaintiff-info-extraction'
    )
    console.error('Error extracting plaintiff info:', error)
    return {
      fullName: 'Unknown',
      dateOfBirth: 'Unknown',
      preExistingConditions: [],
      primaryInjuries: [],
      incidentDate: 'Unknown',
      incidentDescription: 'Unknown'
    }
  }
}

// Step 4: Generate chronology reports
async function generateChronologyReports(
  binderId: string,
  plaintiffInfo: PlaintiffInfo,
  user: AuthUser,
  errorCollector: ErrorCollector,
  strategyInputs: Partial<{
    treatmentTimelineGuidelines?: string | undefined
    prospectiveCareGuidelines?: string | undefined
  }>
) {
  // Get all processed events
  const events = await db.documentEvent.findMany({
    where: { binderId, processed: true },
    orderBy: { timestamp: 'asc' },
    include: { document: { select: { title: true } } }
  })

  // Generate treatment calendar from events
  const treatmentCalendar = generateTreatmentCalendar(events)

  // Generate timeline markdown
  const timelineMarkdown = generateTimelineMarkdown(events)

  // Generate focused report sections using LLM
  const [caseInfo, incidentSummary, medicalOverview, legalGaps] =
    await Promise.allSettled([
      generateCaseInformation(
        plaintiffInfo,
        errorCollector,
        strategyInputs,
        user
      ),
      generateIncidentSummary(
        plaintiffInfo,
        events,
        errorCollector,
        strategyInputs,
        user
      ),
      generateMedicalOverview(
        plaintiffInfo,
        events,
        errorCollector,
        strategyInputs,
        user
      ),
      generateLegalGaps(
        plaintiffInfo,
        events,
        errorCollector,
        strategyInputs,
        user
      )
    ])

  // Extract results from settled promises, using fallbacks for failures
  const caseInfoResult =
    caseInfo.status === 'fulfilled'
      ? caseInfo.value
      : `## Case Information & Demographics\n\nError generating case information. Manual review required.`

  const incidentSummaryResult =
    incidentSummary.status === 'fulfilled'
      ? incidentSummary.value
      : `## Incident Summary\n\nError generating incident summary. Manual review required.`

  const medicalOverviewResult =
    medicalOverview.status === 'fulfilled'
      ? medicalOverview.value
      : `## Medical Overview\n\nError generating medical overview. Manual review required.`

  const legalGapsResult =
    legalGaps.status === 'fulfilled'
      ? legalGaps.value
      : `## Strategic Case Analysis\n\nError generating legal gaps analysis. Manual review required.`

  // Log any failures
  if (caseInfo.status === 'rejected') {
    errorCollector.addError(
      `Case info generation failed: ${caseInfo.reason}`,
      'case-info-generation'
    )
  }
  if (incidentSummary.status === 'rejected') {
    errorCollector.addError(
      `Incident summary generation failed: ${incidentSummary.reason}`,
      'incident-summary-generation'
    )
  }
  if (medicalOverview.status === 'rejected') {
    errorCollector.addError(
      `Medical overview generation failed: ${medicalOverview.reason}`,
      'medical-overview-generation'
    )
  }
  if (legalGaps.status === 'rejected') {
    errorCollector.addError(
      `Legal gaps generation failed: ${legalGaps.reason}`,
      'legal-gaps-generation'
    )
  }

  // Stitch together the complete report
  const fullReport = `# Medical Chronology Report

${caseInfoResult}

${incidentSummaryResult}

${medicalOverviewResult}

${treatmentCalendar}

${timelineMarkdown}

${legalGapsResult}

---
*Report generated on ${new Date().toLocaleDateString()} for legal demand preparation*`

  console.log('📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄')
  console.log(
    `Generated medical chronology report: ${events.length} events, ${fullReport.length} characters`
  )
  console.log('📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄📄')

  // Store results
  try {
    await Promise.all([
      // Store plaintiff info
      db.processedChronology.upsert({
        where: {
          binderId_fileType: { binderId, fileType: CaseFileType.PLAINTIFF_INFO }
        },
        update: {
          content: JSON.stringify(plaintiffInfo),
          eventIds: events.map((e) => e.id),
          metadata: { generatedAt: new Date() }
        },
        create: {
          binderId,
          fileType: CaseFileType.PLAINTIFF_INFO,
          content: JSON.stringify(plaintiffInfo),
          eventIds: events.map((e) => e.id),
          metadata: { generatedAt: new Date() }
        }
      }),

      // Store medical chronology
      db.processedChronology.upsert({
        where: {
          binderId_fileType: {
            binderId,
            fileType: CaseFileType.MEDICAL_CHRONOLOGY_PRO
          }
        },
        update: {
          content: fullReport,
          eventIds: events.map((e) => e.id),
          metadata: { eventCount: events.length, generatedAt: new Date() }
        },
        create: {
          binderId,
          fileType: CaseFileType.MEDICAL_CHRONOLOGY_PRO,
          content: fullReport,
          eventIds: events.map((e) => e.id),
          metadata: { eventCount: events.length, generatedAt: new Date() }
        }
      }),

      db.caseFile.upsert({
        where: {
          binderId_fileType: {
            binderId,
            fileType: CaseFileType.MEDICAL_CHRONOLOGY_PRO
          }
        },
        update: {
          content: fullReport,
          updatedAt: new Date()
        },
        create: {
          binderId,
          creatorId: user.id,
          fileType: CaseFileType.MEDICAL_CHRONOLOGY_PRO,
          content: fullReport
        }
      })
    ])
  } catch (error) {
    errorCollector.addError(
      error instanceof Error ? error.message : 'Unknown error',
      'final-db-storage'
    )
    console.error('Error storing final results:', error)
    throw error // Re-throw to fail the process since we couldn't save results
  }
}

// Generate treatment calendar table from events
function generateTreatmentCalendar(events: DocumentEvent[]): string {
  type TreatmentGroup = {
    provider: string
    department: string
    visits: (typeof events)[0][]
    firstVisit: Date
    lastVisit: Date
    documentRefs: Set<string>
  }

  const extractPageFromReference = (pageRef: string): string => {
    const match = pageRef.match(/^(\d+)$|^(\d+)-(\d+)$/)
    return match ? pageRef : '1'
  }

  const extractDocumentId = (pageRef: string): string => {
    const parts = pageRef.split(':')
    return parts.length > 1 ? parts[0] : pageRef
  }

  const treatmentGroups = events
    .filter((e) => !['police_report', 'injury_report'].includes(e.eventType))
    .reduce<Record<string, TreatmentGroup>>((groups, event) => {
      const provider =
        extractProvider(event.eventDescription) || 'Unknown Provider'
      const department = mapEventTypeToDepartment(event.eventType)
      const key = `${provider}-${department}`

      const documentId = extractDocumentId(event.pageRange)
      const page = extractPageFromReference(
        event.pageRange.split(':').pop() || ''
      )
      const documentRef = `${documentId}:${page}`

      if (!groups[key]) {
        groups[key] = {
          provider,
          department,
          visits: [],
          firstVisit: event.timestamp,
          lastVisit: event.timestamp,
          documentRefs: new Set()
        }
      }

      groups[key].visits.push(event)
      groups[key].documentRefs.add(documentRef)
      if (new Date(event.timestamp) < new Date(groups[key].firstVisit)) {
        groups[key].firstVisit = event.timestamp
      }
      if (new Date(event.timestamp) > new Date(groups[key].lastVisit)) {
        groups[key].lastVisit = event.timestamp
      }

      return groups
    }, {})

  let markdown = '## Treatment Calendar\n\n'
  markdown +=
    '| Medical Provider | Department | Treatment Period | Number of Visits | Reference Document |\n'
  markdown +=
    '|------------------|------------|------------------|------------------|--------------------|\n'

  Object.values(treatmentGroups).forEach((group: TreatmentGroup) => {
    const emoji = getDepartmentEmoji(group.department)
    const period = formatDateRange(group.firstVisit, group.lastVisit)
    const docRefs = Array.from(group.documentRefs)
      .map((ref) => `[${ref}]`)
      .join(', ')

    markdown += `| ${group.provider} | ${emoji} ${group.department} | ${period} | ${group.visits.length} | ${docRefs} |\n`
  })

  return markdown
}

// Generate timeline markdown from events
function generateTimelineMarkdown(events: any[]): string {
  let markdown = '## Medical Timeline\n\n'

  // Add table headers
  markdown += '| Date | Event | Type | Description | Source |\n'
  markdown += '|------|-------|------|-------------|--------|\n'

  events.forEach((event) => {
    const date = new Date(event.timestamp).toLocaleString()
    // const timeIndicator = event.estimatedTime ? ' ⏰' : ''
    const eventEmoji = getEventEmoji(event.eventType)

    // Escape pipe characters in content and limit description to 100 chars
    const escapedEvent = event.event.replace(/\|/g, '\\|')
    const escapedDescription =
      event.eventDescription.replace(/\|/g, '\\|').substring(0, 100) +
      (event.eventDescription.length > 100 ? '...' : '')

    markdown += `| ${date} | ${eventEmoji} ${escapedEvent} | ${event.eventType.replace(/_/g, ' ')} | ${escapedDescription} | [${event.documentId}] |\n`
  })

  return markdown
}

function getEventEmoji(eventType: string): string {
  const emojis: Record<string, string> = {
    medical_treatment: '🏥',
    emergency_visit: '🚨',
    diagnostic_test: '🔬',
    injury_report: '📋',
    police_report: '👮',
    medication: '💊',
    surgery: '⚕️',
    therapy: '🤲'
  }
  return emojis[eventType] || '📄'
}

// Generate case information and demographics section
async function generateCaseInformation(
  plaintiffInfo: PlaintiffInfo,
  errorCollector: ErrorCollector,
  strategyInputs: Partial<{
    treatmentTimelineGuidelines?: string | undefined
    prospectiveCareGuidelines?: string | undefined
  }>,
  user: AuthUser
): Promise<string> {
  const prompt = `Generate a comprehensive Case Information & Demographics section for a mass tort legal demand letter that establishes the plaintiff's credibility and damage potential.

Plaintiff Data: ${JSON.stringify(plaintiffInfo)}

Create a professional section with:
- Complete patient demographics emphasizing life stage and responsibilities
- Occupation and earning capacity details for economic damage calculations
- Pre-existing medical conditions with clear distinction from incident-related injuries
- Primary injuries with emphasis on severity, permanency, and functional impact
- Key case identifiers and incident details establishing liability timeline

Legal Demand Letter Requirements:
- Establish plaintiff as credible, hard-working individual
- Emphasize life disruption and loss of normal activities
- Highlight economic productivity and financial responsibilities
- Demonstrate significant life impact beyond medical treatment
- Create sympathetic narrative while maintaining professional tone
- Support damage calculations with demographic context

Format as markdown with ## Case Information & Demographics header.
Write in third person with authoritative legal tone suitable for opposing counsel and insurance adjusters.`

  try {
    const response = await createGeminiCompletion({
      modelName: GeminiModel.Gemini25Pro,
      systemInstruction: prompt,
      message:
        'Generate case information and demographics section for legal demand',
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'case-info-generation'
    })
    return response as string
  } catch (error) {
    errorCollector.addError(
      error instanceof Error ? error.message : 'Unknown error',
      'case-info-generation'
    )
    console.error('Error generating case information:', error)
    return `## Case Information & Demographics

**Patient:** ${plaintiffInfo.fullName}
**Date of Birth:** ${plaintiffInfo.dateOfBirth}
**Age:** ${plaintiffInfo.age || 'Unknown'}
**Gender:** ${plaintiffInfo.gender || 'Unknown'}
**Occupation:** ${plaintiffInfo.occupation || 'Unknown'}

**Pre-existing Conditions:** ${plaintiffInfo.preExistingConditions?.join(', ') || 'None documented'}
**Primary Injuries:** ${plaintiffInfo.primaryInjuries?.join(', ') || 'Under evaluation'}`
  }
}

// Generate incident summary section
async function generateIncidentSummary(
  plaintiffInfo: PlaintiffInfo,
  events: any[],
  errorCollector: ErrorCollector,
  strategyInputs: Partial<{
    treatmentTimelineGuidelines?: string | undefined
    prospectiveCareGuidelines?: string | undefined
  }>,
  user: AuthUser
): Promise<string> {
  const incidentEvents = events
    .filter(
      (e) =>
        e.eventType === 'injury_report' ||
        e.eventType === 'police_report' ||
        new Date(e.timestamp).toDateString() ===
          new Date(plaintiffInfo.incidentDate).toDateString()
    )
    .slice(0, 5)

  const prompt = `Generate a compelling Incident Summary section for a mass tort legal demand letter that establishes clear liability and causation.

Incident Details:
- Date: ${plaintiffInfo.incidentDate}
- Description: ${plaintiffInfo.incidentDescription}

Related Events: ${JSON.stringify(
    incidentEvents.map((e) => ({
      event: e.event,
      date: e.timestamp,
      description: e.eventDescription
    }))
  )}

${
  strategyInputs?.treatmentTimelineGuidelines?.trim()
    ? `
STRATEGY GUIDANCE - Treatment Timeline Focus:
${strategyInputs.treatmentTimelineGuidelines}

When connecting the incident to subsequent treatment, incorporate the treatment timeline focus areas to strengthen the causation narrative.
`
    : ''
}

Create a persuasive section that:
- Establishes clear timeline and causation chain from incident to injuries
- Emphasizes defendant's liability and duty of care breach
- Highlights plaintiff's innocence and lack of contributory negligence
- Documents immediate medical attention showing injury severity
- References supporting documentation and witness accounts
- Demonstrates incident was preventable with proper defendant conduct

Legal Strategy Focus:
- Use definitive language establishing causation ("resulted in," "caused," "directly led to")
- Emphasize foreseeability of harm to support negligence claims
- Highlight industry standards or regulations that were violated
- Demonstrate plaintiff's reasonable reliance on defendant's duty of care
- Show immediate medical seeking behavior proving genuine injury
- Establish incident as proximate cause of all subsequent medical treatment

Format as markdown with ## Incident Summary header.
Write with conviction and legal authority to support liability arguments.`

  try {
    const response = await createGeminiCompletion({
      modelName: GeminiModel.Gemini25Pro,
      systemInstruction: prompt,
      message: 'Generate incident summary for legal demand letter',
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'incident-summary-generation'
    })
    return response as string
  } catch (error) {
    errorCollector.addError(
      error instanceof Error ? error.message : 'Unknown error',
      'incident-summary-generation'
    )
    console.error('Error generating incident summary:', error)
    return `## Incident Summary

**Incident Date:** ${plaintiffInfo.incidentDate}
**Incident Description:** ${plaintiffInfo.incidentDescription}

The incident resulted in documented injuries requiring ongoing medical treatment and intervention.`
  }
}

// Generate medical overview section
async function generateMedicalOverview(
  plaintiffInfo: PlaintiffInfo,
  events: any[],
  errorCollector: ErrorCollector,
  strategyInputs: Partial<{
    treatmentTimelineGuidelines?: string | undefined
    prospectiveCareGuidelines?: string | undefined
  }>,
  user: AuthUser
): Promise<string> {
  const medicalEvents = events.filter((e) =>
    [
      'medical_treatment',
      'surgery',
      'emergency_visit',
      'diagnostic_test',
      'therapy'
    ].includes(e.eventType)
  )

  const treatmentSummary = {
    totalTreatments: medicalEvents.length,
    emergencyVisits: events.filter((e) => e.eventType === 'emergency_visit')
      .length,
    surgeries: events.filter((e) => e.eventType === 'surgery').length,
    diagnosticTests: events.filter((e) => e.eventType === 'diagnostic_test')
      .length,
    therapySessions: events.filter((e) => e.eventType === 'therapy').length,
    treatmentPeriod:
      events.length > 0
        ? {
            start: events[0].timestamp,
            end: events[events.length - 1].timestamp
          }
        : null
  }

  const prompt = `Generate a compelling Medical Overview section for a mass tort legal demand letter that maximizes damage presentation and demonstrates ongoing harm.

Patient: ${plaintiffInfo.fullName}
Primary Injuries: ${plaintiffInfo.primaryInjuries?.join(', ')}
Pre-existing Conditions: ${plaintiffInfo.preExistingConditions?.join(', ')}

Treatment Summary: ${JSON.stringify(treatmentSummary)}

Recent Medical Events: ${JSON.stringify(
    medicalEvents.slice(-10).map((e) => ({
      date: e.timestamp,
      type: e.eventType,
      event: e.event,
      description: e.eventDescription.substring(0, 200)
    }))
  )}

${
  strategyInputs?.treatmentTimelineGuidelines?.trim()
    ? `
STRATEGY GUIDANCE - Treatment Timeline Focus:
${strategyInputs.treatmentTimelineGuidelines}

When presenting the medical overview, emphasize the treatment timeline elements specified above. Structure the medical narrative to highlight these strategic focus areas.
`
    : ''
}

${
  strategyInputs?.prospectiveCareGuidelines?.trim()
    ? `
STRATEGY GUIDANCE - Prospective Care Emphasis:
${strategyInputs.prospectiveCareGuidelines}

When discussing ongoing and future medical needs, prioritize the prospective care elements outlined above. Use this guidance to strengthen the future medical expense and ongoing care arguments.
`
    : ''
}

Create a powerful section that:
- Emphasizes severity and permanency of injuries for maximum damage recovery
- Documents extensive medical intervention demonstrating significant harm
- Highlights ongoing treatment needs and future medical expenses
- Quantifies pain, suffering, and functional limitations in compelling terms
- Demonstrates medical necessity of all treatments to counter defense arguments
- Shows progression from acute to chronic conditions establishing permanency
- Documents failed conservative treatments requiring escalated interventions
- Emphasizes impact on daily living, work capacity, and quality of life

Legal Damage Documentation:
- Past medical expenses: Document all treatment costs and necessity
- Future medical care: Establish ongoing treatment needs and life care planning
- Pain and suffering: Use medical language to support non-economic damages
- Functional limitations: Detail specific activity restrictions and lifestyle impact
- Work capacity: Document vocational impact and earning loss potential
- Permanency: Establish lifetime consequences and ongoing medical needs
- Aggravation of pre-existing conditions: Show worsening beyond baseline
- Mental health impact: Document psychological consequences and treatment

Format as markdown with ## Medical Overview header.
Use authoritative medical and legal language that justifies substantial damage awards.`

  try {
    const response = await createGeminiCompletion({
      modelName: GeminiModel.Gemini25Pro,
      systemInstruction: prompt,
      message: 'Generate medical overview for legal demand letter',
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'medical-overview-generation'
    })
    return response as string
  } catch (error) {
    errorCollector.addError(
      error instanceof Error ? error.message : 'Unknown error',
      'medical-overview-generation'
    )
    console.error('Error generating medical overview:', error)
    return `## Medical Overview

**Primary Injuries:** ${plaintiffInfo.primaryInjuries.join(', ') || 'Multiple injuries documented'}
**Treatment Period:** ${
      treatmentSummary.treatmentPeriod
        ? `${new Date(treatmentSummary.treatmentPeriod.start).toLocaleDateString()} - ${new Date(treatmentSummary.treatmentPeriod.end).toLocaleDateString()}`
        : 'Ongoing'
    }
**Total Medical Interventions:** ${treatmentSummary.totalTreatments}

The plaintiff has required extensive medical treatment including ${treatmentSummary.emergencyVisits} emergency visits, ${treatmentSummary.surgeries} surgical procedures, and ${treatmentSummary.therapySessions} therapy sessions.`
  }
}

// Generate legal gaps analysis section
async function generateLegalGaps(
  plaintiffInfo: PlaintiffInfo,
  events: any[],
  errorCollector: ErrorCollector,
  strategyInputs: Partial<{
    treatmentTimelineGuidelines?: string | undefined
    prospectiveCareGuidelines?: string | undefined
  }>,
  user: AuthUser
): Promise<string> {
  const documentationGaps = {
    missingReports:
      events.filter((e) => e.eventType === 'injury_report').length === 0,
    limitedDiagnostics:
      events.filter((e) => e.eventType === 'diagnostic_test').length < 3,
    noPoliceReport:
      events.filter((e) => e.eventType === 'police_report').length === 0,
    treatmentGaps: analyzeTimeGaps(events),
    estimatedDates: events.filter((e) => e.estimatedTime).length
  }

  const prompt = `Conduct a strategic legal documentation analysis for mass tort demand letter preparation, identifying evidence gaps and strengthening opportunities.

Case: ${plaintiffInfo.fullName}
Incident Date: ${plaintiffInfo.incidentDate}

Documentation Analysis: ${JSON.stringify(documentationGaps)}

Event Timeline: ${JSON.stringify(
    events.map((e) => ({
      date: e.timestamp,
      type: e.eventType,
      estimated: e.estimatedTime
    }))
  )}

${
  strategyInputs?.treatmentTimelineGuidelines?.trim()
    ? `
STRATEGY GUIDANCE - Treatment Timeline Focus:
${strategyInputs.treatmentTimelineGuidelines}

When identifying documentation gaps, consider whether the available evidence supports the treatment timeline strategy outlined above. Recommend additional documentation that would strengthen these strategic focus areas.
`
    : ''
}

${
  strategyInputs?.prospectiveCareGuidelines?.trim()
    ? `
STRATEGY GUIDANCE - Prospective Care Strategy:
${strategyInputs.prospectiveCareGuidelines}

In analyzing future care documentation needs, prioritize evidence that supports the prospective care strategy outlined above. Identify gaps in documentation for future medical needs and ongoing treatment plans.
`
    : ''
}

Analyze from legal strategy perspective:
- Evidence gaps that may weaken damage claims or liability arguments
- Missing documentation that opposing counsel might exploit
- Timeline inconsistencies that require legal explanation or expert testimony
- Opportunities to strengthen causation arguments with additional evidence
- Medical record gaps that might benefit from expert medical testimony
- Economic loss documentation needs for wage loss and future earning capacity
- Life care planning requirements for future medical expense claims
- Expert witness needs for causation, permanency, and vocational impact

Strategic Recommendations:
- Additional medical records or expert opinions needed
- Economic documentation to support financial damage claims
- Witness statements or testimony to strengthen liability
- Expert medical testimony for causation and prognosis
- Vocational rehabilitation assessment for earning capacity loss
- Life care planning for future medical expense quantification
- Psychological evaluation for mental health damage claims

Legal Risk Assessment:
- Potential defense arguments based on documentation gaps
- Statute of limitations compliance verification
- Contributory negligence risk factors
- Pre-existing condition defense vulnerabilities
- Medical necessity challenges and responses needed

Format as markdown with ## Strategic Case Analysis header.
Focus on actionable legal strategy for maximizing recovery in mass tort litigation.`

  try {
    const response = await createGeminiCompletion({
      modelName: GeminiModel.Gemini25Pro,
      systemInstruction: prompt,
      message:
        'Analyze legal gaps and documentation for demand letter preparation',
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'legal-gaps-analysis'
    })
    return response as string
  } catch (error) {
    errorCollector.addError(
      error instanceof Error ? error.message : 'Unknown error',
      'legal-gaps-generation'
    )
    console.error('Error generating legal gaps analysis:', error)
    return `## Case Documentation Analysis

**Documentation Status:** Medical records span ${events.length} documented events
**Estimated Dates:** ${documentationGaps.estimatedDates} events have estimated timestamps
**Evidence Strength:** ${documentationGaps.missingReports ? 'Additional incident reports recommended' : 'Incident documentation complete'}

The medical chronology provides a comprehensive foundation for legal demand preparation.`
  }
}

// Analyze time gaps between treatments
function analyzeTimeGaps(
  events: any[]
): { gapDays: number; description: string }[] {
  const gaps: { gapDays: number; description: string }[] = []

  for (let i = 1; i < events.length; i++) {
    const prevDate = new Date(events[i - 1].timestamp)
    const currDate = new Date(events[i].timestamp)
    const gapDays = Math.floor(
      (currDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24)
    )

    if (gapDays > 30) {
      gaps.push({
        gapDays,
        description: `${gapDays}-day gap between ${events[i - 1].event} and ${events[i].event}`
      })
    }
  }

  return gaps.slice(0, 5) // Return top 5 significant gaps
}

// Utility functions
function splitDocumentIntoChunks(content: string): string[] {
  const chunks: string[] = []
  for (let i = 0; i < content.length; i += CHUNK_SIZE) {
    chunks.push(content.substring(i, i + CHUNK_SIZE))
  }
  return chunks
}

function calculatePageRange(chunkIndex: number, totalChunks: number): string {
  const pagesPerChunk = Math.ceil(10 / totalChunks) || 1
  const startPage = chunkIndex * pagesPerChunk + 1
  const endPage = (chunkIndex + 1) * pagesPerChunk
  return startPage === endPage ? `${startPage}` : `${startPage}-${endPage}`
}

function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size))
  }
  return chunks
}

function extractProvider(description: string): string | null {
  // Simple pattern matching for provider names
  const patterns = [
    /Dr\.\s+([A-Z][a-z]+\s+[A-Z][a-z]+)/,
    /([A-Z][a-z]+\s+Hospital)/,
    /([A-Z][a-z]+\s+Medical\s+Center)/,
    /([A-Z][a-z]+\s+Clinic)/
  ]

  for (const pattern of patterns) {
    const match = description.match(pattern)
    if (match) return match[1]
  }

  return null
}

function mapEventTypeToDepartment(eventType: string): string {
  const mapping: Record<string, string> = {
    emergency_visit: 'Emergency Medicine',
    medical_treatment: 'Medical Treatment',
    diagnostic_test: 'Diagnostic Imaging',
    surgery: 'Surgical Services',
    therapy: 'Rehabilitation Services',
    medication: 'Pharmacy/Pain Management',
    expert_opinion: 'Medical Expert Opinion',
    legal_consultation: 'Legal Services',
    disability_assessment: 'Disability Evaluation',
    work_restriction: 'Occupational Medicine',
    pain_documentation: 'Pain Management'
  }
  return mapping[eventType] || 'Medical Treatment'
}

function getDepartmentEmoji(department: string): string {
  const emojis: Record<string, string> = {
    'Emergency Medicine': '🟩',
    'Medical Treatment': '🟨',
    'Diagnostic Imaging': '🟦',
    'Surgical Services': '🔴',
    'Rehabilitation Services': '🟪',
    'Pharmacy/Pain Management': '🟫',
    'Medical Expert Opinion': '⭐',
    'Legal Services': '⚖️',
    'Disability Evaluation': '📋',
    'Occupational Medicine': '🏢',
    'Pain Management': '🔥'
  }
  return emojis[department] || '⚪'
}

function formatDateRange(start: Date, end: Date): string {
  const startStr = new Date(start).toLocaleString()
  const endStr = new Date(end).toLocaleString()
  return startStr === endStr ? startStr : `${startStr} - ${endStr}`
}

// Status check function
export async function checkEventChronologyStatus(
  binderId: string,
  lastUpdatedAt?: Date
) {
  try {
    const chronology = await db.processedChronology.findUnique({
      where: {
        binderId_fileType: {
          binderId,
          fileType: CaseFileType.MEDICAL_CHRONOLOGY_PRO
        }
      }
    })

    const plaintiffInfo = await db.processedChronology.findUnique({
      where: {
        binderId_fileType: {
          binderId,
          fileType: CaseFileType.PLAINTIFF_INFO
        }
      }
    })

    // get content from caseFile for UI display
    const medicalChronologyPro = await db.caseFile.findUnique({
      where: {
        binderId_fileType: {
          binderId,
          fileType: CaseFileType.MEDICAL_CHRONOLOGY_PRO
        }
      },
      select: {
        content: true,
        updatedAt: true,
        queueId: true
      }
    })

    if (!chronology && !medicalChronologyPro) {
      return { success: false, content: null, isUpdated: false }
    }

    const isUpdated =
      lastUpdatedAt !== undefined
        ? (medicalChronologyPro?.updatedAt ?? chronology?.generatedAt ?? 0) >
          lastUpdatedAt
        : !!(chronology?.content || medicalChronologyPro?.content)

    return {
      success: true,
      content: medicalChronologyPro?.content || chronology?.content,
      updatedAt: medicalChronologyPro?.updatedAt || chronology?.generatedAt,
      isUpdated:
        isUpdated && !!(chronology?.content || medicalChronologyPro?.content),
      chronologyReady: !!chronology?.content,
      plaintiffInfoReady: !!plaintiffInfo?.content,
      lastGenerated: chronology?.generatedAt || null
    }
  } catch (error) {
    console.error('Error checking chronology status:', error)
    return { success: false, content: null, isUpdated: false }
  }
}

// Memory usage helper function
function logMemoryUsage(context: string) {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    const usage = process.memoryUsage()
    const formatBytes = (bytes: number) =>
      Math.round((bytes / 1024 / 1024) * 100) / 100
    console.log(
      `🧠 [${context}] Memory: RSS=${formatBytes(usage.rss)}MB, Heap=${formatBytes(usage.heapUsed)}/${formatBytes(usage.heapTotal)}MB`
    )
  }
}

// Utility function to check existing events status
async function checkExistingEventsStatus(
  binderId: string,
  documentIds: number[]
) {
  const existingEvents = await db.documentEvent.findMany({
    where: {
      binderId,
      documentId: { in: documentIds }
    },
    select: {
      documentId: true,
      processed: true
    }
  })

  const eventsByDoc = existingEvents.reduce(
    (acc, event) => {
      if (!acc[event.documentId]) {
        acc[event.documentId] = { total: 0, processed: 0 }
      }
      acc[event.documentId].total++
      if (event.processed) {
        acc[event.documentId].processed++
      }
      return acc
    },
    {} as Record<number, { total: number; processed: number }>
  )

  const documentsWithEvents = Object.keys(eventsByDoc).map(Number)
  const documentsWithoutEvents = documentIds.filter(
    (id) => !documentsWithEvents.includes(id)
  )

  return {
    documentsWithEvents,
    documentsWithoutEvents,
    eventsByDoc,
    totalExistingEvents: existingEvents.length,
    totalProcessedEvents: existingEvents.filter((e) => e.processed).length
  }
}
