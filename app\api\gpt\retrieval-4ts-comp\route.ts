import { NextRequest, NextResponse } from 'next/server'
import { StreamingTextResponse } from 'ai'
import { ChatOpenAI, OpenAIEmbeddings } from '@langchain/openai'
import { PromptTemplate } from '@langchain/core/prompts'
import { Pinecone } from '@pinecone-database/pinecone'
import { PineconeStore } from '@langchain/pinecone'
import { LLMChain } from 'langchain/chains'
import { env } from '@/env.mjs'
import {
  formatVercelMessages,
  processSerialisedSources
} from '@/lib/retriever-utils'
import {
  GPTModel,
  type TextDavinciResponse,
  type VercelChatMessage
} from '@/types'
import { isJsonString } from '@/lib/utils'

// export const runtime = 'edge'
export const dynamic = 'force-dynamic'
export const maxDuration = 120

const GPT_MODEL = GPTModel.GPT4Turbo

const ANSWER_TEMPLATE = `You are now operating under the persona of "SmartCounsel," a highly specialized legal research bot. Your responses must adhere strictly to a legal context and must be delivered professionally and informatively.

The Centre has implemented new criminal laws effective July 1, 2024. The Bharatiya Nyaya <PERSON>hita (BNS), Bharatiya Nagarik Suraksha <PERSON>hita (BNSS), and Bharatiya Sakshya Adhiniyam (BSA) have replaced the Indian Penal Code (IPC), Code of Criminal Procedure (CrPC), and Indian Evidence Act, respectively. Provide a comparison between new and old law if possible. If a query is not related to criminal laws context, remind the user of your current mode and politely refrain from engaging in off-topic discussions.

Answer the question based only on the following context and chat history (No need to explicitly say "based on context"). Do not respond to queries that are not provided in context, even if you know. Your response will automatically include links to relevant legal documents. Based on the context if you think additional context is required, ask for it before answering the question. Be very factual in response, do not provide any legal advice or opinion.

{newCriminalLaw}

{oldCriminalLaw}

Given the following conversation and a follow up question, use the discussion for additional context. Skip messages that are completely unrelated to the follow up question.
<chat_history>
  {chat_history}
</chat_history>

Question: {question}
`

export async function POST(req: NextRequest) {
  try {
    const url = req.nextUrl
    const queryParams = url.searchParams
    const namespace = queryParams.get('ns') || '<>'
    const namespaceParts = namespace?.split('<>')
    const body = await req.json()

    const messages = body.messages ?? []
    const previousMessages = messages.slice(0, -1)
    const userQuestion = messages[messages.length - 1].content
    let currentMessageContent = userQuestion

    const chatHistory = formatVercelMessages(previousMessages)

    const assessment = await assessQuestion({
      messages: previousMessages,
      question: currentMessageContent
    })

    if (
      assessment.rewritten_question &&
      assessment.rewritten_question.length > 5
    ) {
      currentMessageContent = assessment.rewritten_question
    }

    const model = new ChatOpenAI({
      modelName: GPT_MODEL,
      temperature: 0.2,
      streaming: true
    })

    const prompt = PromptTemplate.fromTemplate(ANSWER_TEMPLATE)
    const chain = new LLMChain({ llm: model, prompt })
    const textEncoder = new TextEncoder()
    let [compareSet1Promise, compareSet2Promise]: [
      Promise<
        | {
            context: string
            serializedSources: string
          }
        | undefined
      > | null,
      Promise<
        | {
            context: string
            serializedSources: string
          }
        | undefined
      > | null
    ] = [null, null]

    if (assessment.sector === 'new' || assessment.sector === 'comparison') {
      compareSet1Promise = fetchReferenceDocuments({
        assessment,
        currentMessageContent,
        namespace: namespaceParts[0]
      })
    }

    if (assessment.sector === 'old' || assessment.sector === 'comparison') {
      compareSet2Promise = fetchReferenceDocuments({
        assessment,
        currentMessageContent,
        namespace: namespaceParts[1]
      })
    }

    const [compareSet1, compareSet2] = await Promise.all([
      compareSet1Promise,
      compareSet2Promise
    ])

    const stream = new ReadableStream({
      async start(controller) {
        controller.enqueue(textEncoder.encode(''))

        await chain.call(
          {
            newCriminalLaw: String(compareSet1?.context) || '',
            oldCriminalLaw: String(compareSet2?.context) || '',
            chat_history: chatHistory,
            question: userQuestion
          },
          [
            {
              handleLLMNewToken(token: string) {
                controller.enqueue(textEncoder.encode(token))
              }
            }
          ]
        )
        controller.close()
      }
    })

    const serializedSources1 = compareSet1?.serializedSources
      ? JSON.parse(
          Buffer.from(compareSet1.serializedSources, 'base64').toString()
        )
      : []
    const serializedSources2 = compareSet2?.serializedSources
      ? JSON.parse(
          Buffer.from(compareSet2.serializedSources, 'base64').toString()
        )
      : []

    const serializedSourcesComp = Buffer.from(
      JSON.stringify([...serializedSources1, ...serializedSources2])
    ).toString('base64')

    return new StreamingTextResponse(stream, {
      headers: {
        'x-message-index': (previousMessages.length + 1).toString(),
        'x-sources': serializedSourcesComp,
        'x-namespace': namespace
      }
    })
  } catch (e: any) {
    console.log(e)
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}

async function fetchReferenceDocuments({
  assessment,
  currentMessageContent,
  namespace
}: {
  assessment: QueryAssessmentResponse
  currentMessageContent: string
  namespace: string
}) {
  try {
    const pinecone = new Pinecone()
    const pineconeIndex = pinecone.Index(env.PINECONE_INDEX)
    const vectorstore = await PineconeStore.fromExistingIndex(
      new OpenAIEmbeddings(),
      {
        pineconeIndex,
        namespace
      }
    )
    const docsToSearch = assessment.sector === 'comparison' ? 4 : 8
    const documents = await vectorstore.similaritySearch(
      currentMessageContent,
      docsToSearch
    )

    const context = `
<${namespace}>
  ${documents.map((doc) => doc.pageContent).join('\n\n')}
</${namespace}>
`
    const serializedSources = processSerialisedSources({
      documents,
      titleSet: []
    })

    return { context, serializedSources }
  } catch (error: any) {
    console.log(error.message)
  }
}

interface QueryAssessmentResponse {
  relevance: 'yes' | 'no'
  sector: 'new' | 'old' | 'comparison'
  independent: 'yes' | 'no'
  specific_case_section: 'NA' | string
  rewritten_question?: string
}

async function assessQuestion({
  messages,
  question
}: {
  messages: VercelChatMessage[]
  question: string
}): Promise<QueryAssessmentResponse> {
  const chatHistory = formatVercelMessages(messages, true)
  try {
    const PROMPT = `The following is a conversation between SmartCounsel AI, a legal research bot, and a user. SmartCounsel AI is a highly specialized criminal law legal research bot. The Centre has implemented new criminal laws effective July 1, 2024 and the you are assisting the user to understand the new case laws. Your job is to assist SmartCounsel with a preliminary assessment of the latest question from the user. Unless specified from context the implied search will always be on the new criminal laws. Your assessment would be on:
  - Is the question relevant to the legal matter and fall under "criminal law"?
  - Is this question asking about new laws or old laws or a comparison?
  - Is this question completely independent of the previous questions?
  - Is this question looking for a specific case or a general answer from multiple cases or if answer can be found in multiple cases?
  - Come up with a refined question should distill the key elements of the user's original query, enabling more precise document lookup and filtering out irrelevant information. This process should extract the essence of the query to facilitate accurate information retrieval, which will be crucial for generating a relevant and precise answer in the subsequent stages.
  
  <chat_history>
  ${chatHistory}
  </chat_history>
  
  Your response should be in the following JSON format:
  {
      "relevance": "yes/no",
      "sector": "new/old/comparison",
      "independent": "yes/no",
      "specific_case_section": "NA/section xx",
      "rewritten_question": "..."
  }
  
  Question: ${question}
  `

    const fetchGpt = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: GPTModel.GPT4Turbo,
        messages: [{ role: 'system', content: PROMPT }],
        temperature: 0.2,
        response_format: { type: 'json_object' }
      })
    })

    const assessmentRaw = await fetchGpt.json()

    if (
      (assessmentRaw satisfies TextDavinciResponse) &&
      assessmentRaw?.choices &&
      isJsonString(assessmentRaw.choices[0].message.content)
    ) {
      const assessment = JSON.parse(assessmentRaw.choices[0].message.content)

      if (env.NODE_ENV === 'development') {
        console.log('assessment', assessment)
      }

      return {
        relevance: assessment.relevance,
        sector: assessment.sector,
        independent: assessment.independent,
        specific_case_section: assessment.specific_case_section,
        rewritten_question: assessment.rewritten_question
      }
    } else {
      console.log('assessmentRaw', JSON.stringify(assessmentRaw, null, 2))
      throw new Error('Assessment failed')
    }
  } catch (error) {
    return {
      relevance: 'yes',
      sector: 'new',
      independent: 'no',
      specific_case_section: 'NA',
      rewritten_question: ''
    }
  }
}
