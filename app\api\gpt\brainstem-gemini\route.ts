import { NextRequest, NextResponse } from 'next/server'
import { StreamingTextResponse } from 'ai'
import {
  GPTModel,
  ResearchStoreContent,
  YesNo,
  type VercelChatMessage
} from '@/types'
import { getCurrentUserResponse } from '@/lib/session'
import { assessQuestionQuality } from '@/lib/cerebrum/gpt-assisted-processes'
import {
  REGIONAL_PROMPT_TYPES,
  GENERAL_PROMPT_TYPES,
  generalPromptTemplateProvider,
  regionalPromptTemplateProvider
} from '@/lib/cerebrum/prompt-template'

import { developer } from '@/lib/utils'
import { fetchResearchContext } from '@/lib/cerebrum/vector-processes'
import { ResearchType } from '@prisma/client'
import {
  createCompletionStream,
  transformChatMessages
} from '@/lib/services/gemini-service'

export const dynamic = 'force-dynamic'
export const maxDuration = 120

const GPT_MODEL = GPTModel.GPT4o

interface ChatRequestBody {
  researchType: ResearchType
  namespace?: string
  summarise?: boolean
  metadata: Pick<ResearchStoreContent, 'court' | 'year' | 'sources'>
  messages?: VercelChatMessage[]
}

export async function POST(req: NextRequest) {
  try {
    // Validate if user is logged in
    const user = await getCurrentUserResponse()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const {
      namespace = undefined,
      summarise = false,
      researchType,
      metadata: { sources = [], court = [], year = [] },
      messages = []
    } = (await req.json()) as ChatRequestBody

    const previousMessages = messages.slice(0, -1)
    const userQuestion = messages[messages.length - 1].content

    developer.log(['metadata', { sources, court, year }])
    developer.log(['userQuestion', userQuestion])

    const assessment = await assessQuestionQuality({
      messages: previousMessages,
      question: userQuestion,
      region: user.region,
      namespace: namespace,
      model: GPT_MODEL,
      researchType,
      dummy: summarise ? true : false
    })

    const isPrivateResearch = researchType === ResearchType.private

    const assessmentSector = namespace || assessment.sector
    const baseFilter = {
      court,
      year,
      sources: assessment.independent ? sources : []
    }

    const fetchOptions = {
      summarise: isPrivateResearch ? false : summarise,
      question: userQuestion,
      // relatedQuestions: isPrivateResearch
      //   ? assessment.related_searches || []
      //   : [],
      relatedQuestions: [],
      region: user.region,
      researchType,
      isPrivate: isPrivateResearch || undefined,
      assessment: isPrivateResearch
        ? {
            relevance: YesNo.YES,
            sector: assessmentSector,
            independent: YesNo.NO,
            specific_case: YesNo.NO,
            rewritten_question: userQuestion
          }
        : assessment,
      namespace: assessmentSector,
      filter: baseFilter
    }

    const { serializedSources, context } =
      await fetchResearchContext(fetchOptions)

    if (!context) {
      return NextResponse.json({ error: 'No context found' }, { status: 404 })
    }

    // developer.log(['context', context])

    const prompt = isPrivateResearch
      ? generalPromptTemplateProvider({ type: GENERAL_PROMPT_TYPES.ANSWER })
      : summarise
        ? generalPromptTemplateProvider({ type: GENERAL_PROMPT_TYPES.SUMMERY })
        : regionalPromptTemplateProvider({
            promptType: REGIONAL_PROMPT_TYPES.ANSWER,
            researchType,
            region: user.region
          })

    const textEncoder = new TextEncoder()
    const chatMessagesFiltered: VercelChatMessage[] = [
      ...previousMessages.filter(
        (messages) => messages.role === 'user' || messages.role === 'assistant'
      )
    ]

    developer.log(['chatMessagesFiltered', chatMessagesFiltered])

    const stream = new ReadableStream({
      async start(controller) {
        controller.enqueue(textEncoder.encode(''))

        try {
          const completionStream = createCompletionStream({
            systemInstruction: `${prompt}
            ------------------
            ${context}
            ------------------
            Please ensure that all the information you provide is strictly sourced from the texts provided above or from the previous conversation. Do not include any information that is not contained in these sources. Whenever you present information, you should include a citation in the following format: [[Citation ID]]. Provide citations wherever possible, including within paragraphs and bullet points, similar to how citations are used in books. It's acceptable to cite the same document multiple times in different parts of your response.
            `,
            history: transformChatMessages(chatMessagesFiltered),
            message: userQuestion
          })

          for await (const token of completionStream) {
            controller.enqueue(textEncoder.encode(token))
          }
        } catch (error) {
          console.error('Error during completion stream:', error)
          controller.error(error)
        } finally {
          controller.close()
        }
      }
    })

    return new StreamingTextResponse(stream, {
      headers: {
        'x-message-index': (previousMessages.length + 1).toString(),
        'x-sources': serializedSources,
        'x-namespace': assessment.sector
      }
    })
  } catch (e: any) {
    console.log(e)
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}
