'use server'

import { CaseBinderData } from '@/types/case'
import { db } from '../db'
import { getCurrentUserResponse } from '../session'

export async function createBinder({
  name,
  data
}: {
  name: string
  data: CaseBinderData
}) {
  const user = await getCurrentUserResponse()
  const binder = await db.binder.create({
    data: {
      name: name,
      teamId: user.teamId,
      data: data as any,
      createdBy: user.id
    }
  })

  return binder
}

export async function deleteBinder(id: string) {
  const user = await getCurrentUserResponse()
  const binder = await db.binder.findUnique({
    where: {
      id: id
    }
  })

  if (!binder || binder.teamId !== user.teamId) {
    return false
  }

  await db.caseFile.deleteMany({
    where: { binderId: id }
  })

  await db.binder.delete({
    where: {
      id: id
    }
  })

  return true
}

export async function listAllBinders() {
  const user = await getCurrentUserResponse()
  const binders = await db.binder.findMany({
    where: {
      teamId: user.teamId
    },
    orderBy: {
      name: 'asc'
    }
  })

  return binders
}

export async function fetchLastBinder() {
  const user = await getCurrentUserResponse()
  const binder = await db.binder.findFirst({
    where: {
      teamId: user.teamId
    },
    orderBy: {
      createdAt: 'desc'
    }
  })

  return binder
}

export async function updateResearchBinder({
  researchId,
  binderId
}: {
  researchId: string
  binderId: string
}) {
  const user = await getCurrentUserResponse()
  const research = await db.researchStore.findUnique({
    where: {
      id: researchId
    }
  })

  if (!research || research.userId !== user.id) {
    throw new Error('Research not found')
  }

  const update = await db.researchStore.update({
    where: {
      id: researchId
    },
    data: {
      binderId: binderId
    }
  })

  return update
}
