import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { UnauthorizedError } from './exceptions'
import { CreditType } from '@prisma/client'
import { AuthUser, Session } from 'next-auth'
import {
  findTeamPeriodicCreditOnFeature,
  findTeamPeriodicCreditHistoryOnFeature
} from './recordstore-team'
import { db } from './db'

export async function getCurrentUser() {
  const session = await getServerSession(authOptions)

  return session?.user
}

export async function getFeatureUsageStats({
  feature,
  user
}: {
  feature: CreditType
  user?: Session['user']
}) {
  if (user) {
    const creditsAvailable = await findTeamPeriodicCreditOnFeature({
      teamId: user.teamId,
      type: feature
    })

    const creditsUsed = await findTeamPeriodicCreditHistoryOnFeature({
      teamId: user.teamId,
      type: feature,
      startDate: creditsAvailable?.startDate
    })

    return {
      available: creditsAvailable?.credit || 0,
      used: creditsUsed || 0
    }
  } else {
    const session = await getServerSession(authOptions)
    const usage = session?.usage?.[feature]

    return (
      usage || {
        available: 0,
        used: 0
      }
    )
  }
}

export async function getCurrentUserResponse() {
  const session = await getServerSession(authOptions)
  if (session && session.user.teamId) {
    return session.user
  } else {
    throw new UnauthorizedError()
  }
}

export async function getCurrentUserSession() {
  const session = await getServerSession(authOptions)
  return session
}

export async function getMasqueradeUser(userId: string) {
  const userData = await db.user.findUnique({
    where: {
      id: userId
    },
    include: {
      team: true
    }
  })

  if (!(userData && userData.teamId && userData.team)) {
    return null
  }

  const user: AuthUser = {
    id: userData.id,
    name: userData.name,
    email: userData.email,
    image: userData.image,
    teamId: userData.teamId,
    plan: userData.team.plan,
    userType: userData.userType,
    region: userData.region,
    settings: {
      questionAssessment: true
    }
  }

  return user
}

export async function getMasqueradeUserNonNullable(userId: string) {
  const user = await getMasqueradeUser(userId)
  return user!
}
