import type { NextApiRequest, NextApiResponse } from 'next'
import axios from 'axios'
import * as cheerio from 'cheerio'
import { db } from '@/lib/db'
import { CaseData } from '@/types/document'
import { env } from '@/env.mjs'
import { DocumentRecords } from '@prisma/client'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (env.NODE_ENV !== 'development') {
      throw new Error('Unauthorized')
    }

    const source = req.query.source as string
    console.log('Indexing source: ', source)

    const CHUNK_SIZE = 15
    const BATCH_SIZE = 500
    let lastId = 0

    const size = await db.documentRecords.count({
      where: {
        indexed: false,
        source: source
      }
    })

    console.log('Total records to index: ', size)

    let totalDuration = 0
    let totalChunksProcessed = 0

    if (size === 0) {
      return res.status(200).json({ message: 'No data to index!' })
    }

    for (let i = 0; i < 20000; i += BATCH_SIZE) {
      console.log('Processing batch: ', i)
      const records = await db.documentRecords.findMany({
        select: {
          id: true,
          content: true,
          source: true,
          ref: true,
          meta: true,
          summary: true
        },
        where: {
          indexed: false,
          id: {
            gt: lastId
          },
          source: source
        },
        take: BATCH_SIZE,
        orderBy: {
          id: 'asc'
        }
      })

      if (records.length === 0) {
        break
      }

      lastId = records[records.length - 1].id

      for (let j = 0; j < records.length; j += CHUNK_SIZE) {
        const startTimestamp = new Date().getTime()
        const chunk = records.slice(j, j + CHUNK_SIZE)
        const promises = chunk.map((record) => indexDocument(record))
        await Promise.allSettled(promises)
        const endTimestamp = new Date().getTime()
        const duration = (endTimestamp - startTimestamp) / 1000

        totalDuration += duration
        totalChunksProcessed += 1

        const totalBatchesRemaining = Math.ceil(
          (size - totalChunksProcessed * CHUNK_SIZE) / CHUNK_SIZE
        )
        const averageChunkDuration = totalDuration / totalChunksProcessed
        const estimatedTimeRemainingMins = (
          (totalBatchesRemaining * averageChunkDuration) /
          60
        ).toFixed(2)
        const estimatedTimeRemainingHrs = (
          Number(estimatedTimeRemainingMins) / 60
        ).toFixed(2)

        console.info(
          `Chunk processed in ${duration} seconds. Estimated time remaining: ${estimatedTimeRemainingMins} mins or (${estimatedTimeRemainingHrs} hrs)`
        )
      }
    }

    console.info('Indexing complete!')

    res.status(200).json({ message: 'Data saved successfully!' })
  } catch (error) {
    res.status(500).json({ error: 'Failed to index.' })
  }
}

async function indexDocument(
  record: Pick<
    DocumentRecords,
    'id' | 'content' | 'source' | 'ref' | 'meta' | 'summary'
  >
) {
  try {
    let refId = ''
    switch (record.source) {
      case 'labourlawreporter':
        refId = 'llr-' + record.ref
        break
      case 'taxindiaonline':
      case 'taxindiaonline-income-tax':
        refId = 'tio-' + record.ref
        break
    }

    const metadata = JSON.parse(record.meta) as CaseData
    const { data: response } = await axios.post(
      env.NEXTAUTH_URL + '/api/gpt/ingest',
      {
        text: record.content,
        source: record.source,
        metadata: {
          documentRecordsId: record.id,
          documentRecordsSource: record.source,
          refId: refId,
          ...metadata,
          summary: undefined
        }
      }
    )
    if (response.ok) {
      await db.documentRecords.update({
        where: {
          id: record.id
        },
        data: {
          indexed: true
        }
      })
      console.log('Indexed: ', record.id)
    }
  } catch (error: any) {
    console.log('Error indexing: ', record.id, error.message)
  }
}

async function indexDocumentHtml(record: DocumentRecords) {
  try {
    const $ = cheerio.load(record.content)
    const text = $('body').text()

    let refId = ''
    switch (record.source) {
      case 'labourlawreporter':
        refId = 'llr-' + record.ref
        break
      case 'taxindiaonline':
      case 'taxindiaonline-income-tax':
        refId = 'tio-' + record.ref
        break
    }

    const metadata = JSON.parse(record.meta) as CaseData
    const { data: response } = await axios.post(
      env.NEXTAUTH_URL + '/api/gpt/ingest',
      {
        text: record.summary || text,
        source: record.source,
        metadata: {
          documentRecordsId: record.id,
          documentRecordsSource: record.source,
          refId: refId,
          ...metadata
        }
      }
    )
    if (response.ok) {
      await db.documentRecords.update({
        where: {
          id: record.id
        },
        data: {
          indexed: true
        }
      })
      console.log('Indexed: ', record.id)
    }
  } catch (error: any) {
    console.log('Error indexing: ', record.id, error.message)
  }
}
