// lib/processes/case-eval-generator/case-evaluation-prompts.ts
import { PromptStore } from '@/types/case'

export const caseEvalDamageDetailPrompts: PromptStore[] = [
  {
    id: 'past_medical_expenses',
    title: 'Past Medical Expenses',
    contextNeeded: [
      'MEDICAL_CHRONOLOGY',
      'ECONOMIC_DAMAGES',
      'EXTRACTED_FINANCIAL_DATA'
    ],
    prompt: `Generate a detailed 'Past Medical Expenses' section with the following specific components and format:

        1) Begin with the heading 'Past Medical Expenses' in bold, followed by a line break

        2) Start with this opening sentence: 'To date, Mr./Ms. [LAST NAME] has incurred medical expenses as itemized below.'

        3) Create a comprehensive, properly formatted table with these exact columns:
           - Provider (name of medical provider/facility)
           - Date of Service (either specific date or date range in format 'MM/DD/YYYY' or 'MM/DD/YYYY to MM/DD/YYYY')
           - Amount Charged (dollar amount formatted with dollar sign, commas for thousands, and two decimal places)
           - Supporting Document(s) (reference to specific exhibit numbers)

        4) Include every medical provider mentioned in the MEDICAL_CHRONOLOGY, organized chronologically by first date of service

        5) For providers with multiple visits over a period, use the format:
           - Provider: Full facility/practice name
           - Date of Service: 'MM/DD/YYYY to MM/DD/YYYY'
           - Amount: Total for all services
           - Supporting Document(s) / Exibits

        6) At the bottom of the table, include a 'Total' row that correctly sums all amounts

        7) After the table, include any necessary explanatory notes about potentially missing documentation, formatted as:
           '[NTD: As noted in the medical summary for [Provider Name], we believe there is missing documentation for [description of what's missing], which may include additional medical billing.]'

        8) Conclude with this exact paragraph:
           'If you dispute any of Mr./Ms. [LAST NAME]'s medical treatment or bills as unnecessary or unreasonable, please specify the disputed items in writing. Otherwise, we will assume you agree with the necessity and reasonableness of Mr./Ms. [LAST NAME]'s medical treatments and bills.'

        9) Format the table with:
           - Clear borders around all cells
           - Column headers in bold
           - Consistent cell padding
           - Proper alignment (left-align text, right-align numbers)
           - Proper spacing between table rows

        Ensure all dollar amounts are formatted consistently and match the totals referenced elsewhere in the document. The table should be comprehensive, accurate, and professional in appearance, exactly matching the format seen in the sample demand letter. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'future_medical_expenses',
    title: 'Future Medical Expenses',
    contextNeeded: [
      'MEDICAL_CHRONOLOGY',
      'ECONOMIC_DAMAGES',
      'DAMAGES_CALCULATION'
    ],
    prompt: `Create the 'Future Medical Expenses' section with these specific elements and formatting:

        1) Begin with the heading 'Future Medical Expenses' in bold, followed by a line break

        2) Open with this sentence: 'Mr./Ms. [LAST NAME] will require additional future treatment as identified below.'

        3) Create a detailed table with these exact columns:
           - Procedure (name of the medical procedure/treatment/appointment)
           - Years (duration for which the treatment is needed, expressed as a number)
           - Per Year (frequency of treatment needed each year, expressed as a number)
           - Cost (cost per procedure, formatted with dollar sign, commas for thousands, and two decimal places)
           - Total (calculated by multiplying Years × Per Year × Cost, formatted with dollar sign, commas, and two decimal places)

        4) Include these specific categories of future medical expenses (if applicable based on the context):
           - Follow-up appointments with specialists (orthopedic, neurology, pain management, etc.)
           - Physical therapy or rehabilitation sessions
           - Diagnostic imaging (MRIs, X-rays, CT scans)
           - Injections or pain management procedures
           - Surgical interventions
           - Medication costs
           - Assistive devices or home modifications
           - Chiropractic care or alternative treatments

        5) For each row, include a superscript footnote number at the end of the procedure name that corresponds to the source document

        6) Calculate and include a 'Total' row at the bottom that correctly sums all amounts in the Total column

        7) After the table, include this exact paragraph:
           'Healthcare and medication costs are expected to rise, and we reserve the right to update or extend our estimate to account for further care needs.'

        8) After this paragraph, include footnotes that cite the source documents for each future expense, formatted as:
           'Exhibit - [Provider Name] - [Document Type] [doc-id:page]'
           'Exhibit - [Provider Name] - [Document Type] [doc-id:page]'
           etc.

        9) Format the table with:
           - Clear borders around all cells
           - Column headers in bold and centered
           - Proper alignment (left-align text, right-align numbers)
           - Consistent cell padding and spacing

        Ensure all projected treatments are based on physician recommendations documented in the medical records. The calculations should be mathematically correct, and the formatting should exactly match the sample demand letter's future medical expenses section. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'loss_of_income',
    title: 'Loss of Income',
    contextNeeded: [
      'PLAINTIFF_INFO',
      'ECONOMIC_DAMAGES',
      'EXTRACTED_FINANCIAL_DATA'
    ],
    prompt: `Generate the 'Loss of Income' section with these specific components and formatting:
        
        1) Begin with the heading 'Loss of Income' in bold, followed by a line break

        2) Write a concise paragraph (4-6 lines) that includes these specific elements:
           - Reference to an enclosed Economic Loss Report (e.g., 'Based on the enclosed Economic Loss Report estimating that Mr./Ms. [LAST NAME] will be unable to return to work until at least [DATE].')
           - State the exact total amount of lost income (e.g., 'Mr./Ms. [LAST NAME] suffered a loss of income totaling $XX,XXX.XX')
           - Explain why the plaintiff was unable to work (e.g., 'when he/she was rendered unable to work due to the injuries he/she sustained in the [INCIDENT DATE] [incident type]')
        
        3) Format the dollar amount in bold to highlight it visually

        4) Include a footnote reference after mentioning the Economic Loss Report, formatted as a superscript number

        5) At the bottom of the page, include the footnote with the exact reference from documents:
           'Exhibit XX - Loss of Income Documentation and Economic Loss Report. [doc-id:page]'

        6) If the context provides details about:
           - The plaintiff's occupation, include it (e.g., 'as a [OCCUPATION]')
           - Pre-injury wage/salary, include it (e.g., 'where he/she earned $XX.XX per hour/week/year')
           - Specific time periods missed, include them (e.g., 'from [START DATE] to [END DATE]')
           - Partial vs. total work disability, clarify this (e.g., 'was completely unable to work' or 'was restricted to light duty resulting in reduced hours')

        Keep this section brief but precise. The tone should be factual and matter-of-fact. Format with appropriate spacing before and after the section. If specific details about income loss calculation are not provided in the context, keep the language more general but still professionally formatted, following the pattern in the sample demand letter.`
  },
  {
    id: 'loss_of_household_services',
    title: 'Loss of Household Services',
    contextNeeded: ['PLAINTIFF_INFO', 'ECONOMIC_DAMAGES'],
    prompt: `Create the 'Loss of Household Services' section with these specific elements and formatting:

        1) Begin with the heading 'Loss of Household Services' in bold, followed by a line break

        2) Start with this sentence: 'Mr./Ms. [LAST NAME] has been unable to contribute valuable household services for which he/she is entitled to the below compensation:'

        3) Include a numbered list (1-3) explaining the basis for the calculation:
           - Item 1: Establish pre-incident baseline contribution (e.g., 'Prior to [INCIDENT DATE], Mr./Ms. [LAST NAME] contributed an estimated average of [X.XX] hours of household services per day to his/her household, in line with the average contribution of similarly aged persons in [STATE].')
           - Item 2: Establish monetary value (e.g., 'The monetary value of Mr./Ms. [LAST NAME]'s labor is consistent with the average housekeeping salary of $[XX.XX] an hour in [STATE], as estimated from the [MONTH YEAR] Report on Occupational Employment and Wages by the U.S. Bureau of Labor Statistics.⁷')
           - Item 3: Detail the periods and percentages of impairment (e.g., 'Mr./Ms. [LAST NAME] suffered a [XX]% impairment from contributing to household services from [START DATE] to [END DATE], and a conservative [XX]% impairment for [X] years thereafter.')
        
        4) Add a paragraph summarizing the total value, using this format:
           'Assuming no further impairment of household services after [END DATE], we calculate the value of Mr./Ms. [LAST NAME]'s loss of household services between [START DATE] and [END DATE] at $[XX,XXX.XX].'

        5) Create a table titled 'Loss of Household Services Schedule' with these columns:
           - Start of Loss Date (formatted as MM/DD/YYYY)
           - End of Loss Date (formatted as MM/DD/YYYY)
           - Hourly Rate (dollar amount with dollar sign and two decimal places)
           - Hours Per Day (number with two decimal places)
           - % Impaired (percentage)
           - Net Loss (dollar amount with dollar sign, commas, and two decimal places)
        
        6) Include a row for each period of different impairment level

        7) Add a 'Total Loss of Household Services' row at the bottom with the sum

        8) After the table, include a paragraph citing legal precedent for household services damages in your jurisdiction, formatted as:
           'Injured plaintiffs can recover damages for loss of household services in [STATE], which encompass tasks such as [examples of household tasks]. See [doc-id:page]; [doc-id:page]; [doc-id:page].'

        9) Include a footnote after the reference to the Bureau of Labor Statistics with this exact format:
           '⁷ U.S. DEPT. OF LABOR., Div. of Occupational Emp't & Wage Statistics, Rep. on Occupational Emp't & Wages (May 2024) (accessed May 2024). BLS.gov cannot vouch for the data or analyses derived from these data after the data have been retrieved from BLS.gov.'
        
        Format the table with clear borders, proper alignment (left-align text, right-align numbers), and consistent spacing. The calculations should be mathematically correct, with the Net Loss being Hourly Rate × Hours Per Day × Days in Period × % Impaired. The formatting should exactly match the sample demand letter's household services section. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'pain_and_suffering',
    title: 'Past and Future Pain and Suffering',
    contextNeeded: [
      'PLAINTIFF_INFO',
      'MEDICAL_CHRONOLOGY',
      'NON_ECONOMIC_DAMAGES',
      'CASE_EVALUATION'
    ],
    prompt: `Generate the 'Past and Future Pain and Suffering' section with these specific elements and formatting:

        1) Begin with the heading 'Past and Future Pain and Suffering' in bold, followed by a line break

        2) Write an opening paragraph that states:
           'The [INCIDENT DATE] [incident type] resulted in Mr./Ms. [LAST NAME] sustaining significant noneconomic damages, including considerable physical and emotional pain and suffering, for which he/she is entitled to compensation.'
        
        3) Include a comprehensive paragraph (6-8 lines) explaining the legal basis for pain and suffering damages in your jurisdiction, citing specific state law and case precedent. Use this format:
           '[STATE] law allows for an injured plaintiff to recover noneconomic damages, which includes physical pain and suffering, as a result personal injuries tortiously and proximately caused by the wrongful acts/omissions of a defendant. [doc-id:page].'

        4) Add a paragraph explaining how future pain and suffering is calculated, with relevant legal citations
        
        5) Include the statutory definition of noneconomic damages from your state law, formatted as:
           'The term "noneconomic damages" means and includes, for example, "[QUOTE DIRECTLY FROM STATUTE]" [STATUTE CITATION].'

        6) Add a paragraph explaining who determines noneconomic damages (e.g., 'The determination of an injured plaintiff's noneconomic damages, including past and future pain and suffering, is decidedly left to the sound discretion of the trier of fact.') with relevant case citations

        7) Include a paragraph about per multiplier calculation methods being accepted in your jurisdiction, with case citations

        8) Include a quote of the standard jury instruction for noneconomic damages in your jurisdiction, formatted in a text box or indented

        9) Write 2-3 detailed paragraphs describing the plaintiff's specific pain and suffering, including:
           - Physical pain experienced (specific locations, intensity, frequency)
           - Medication required for pain management
           - Impact on daily activities and independence
           - Emotional distress, anxiety, or depression resulting from injuries
           - Specific activities the plaintiff can no longer enjoy or must modify
           - Comparison of quality of life before and after the incident
           - Impact on family relationships and social life
           - Sleep disturbances or other secondary effects
           - If applicable, include a compelling photograph that illustrates the plaintiff's pre-injury lifestyle

        10) Create a 'Verdict Analysis' subsection with:
            - A heading 'Verdict Analysis' in bold
            - A statement: 'Mr./Ms. [LAST NAME] is entitled to pain and suffering damages, as supported by the following verdict rendered under similar circumstances:'
            - A table with these rows:
              * Verdict Citation (case name and citation)
              * Jurisdiction (county and state)
              * Award (dollar amount awarded)
              * Case Facts (2-3 sentence summary of the case)
              * Injuries (bullet points of similar injuries)
            - A concluding paragraph comparing the cited case to the plaintiff's situation

        11) Create a 'Noneconomic Damages - Pain and Suffering' subsection with:
            - A statement of the total value: 'As illustrated in the below multiplier method analysis, Mr./Ms. [LAST NAME]'s claim for pain and suffering is conservatively valued at $[XXX,XXX.XX].'
            - List the economic damages used as the base for calculation (e.g., '● Total Medical Expenses: $[XX,XXX.XX]', '● Lost Wages: $[XX,XXX.XX]')
            - A detailed calculation table titled 'Pain & Suffering' with these sections:
              * Pain & Suffering (with rows for Total Economic Damages, Applied Multiplier(between 1 to 5 based on severity, duration of suffering, medical documentation, and lasting limitations), Total Pain & Suffering Value)
            - A concluding statement: 'In light of the multiplier calculation and representative verdict analysis above, Mr./Ms. [LAST NAME] is entitled to $[XXX,XXX.XX] as compensation for his/her pain and suffering.'

        Format all tables with clear borders, proper alignment, and consistent spacing. Use appropriate legal terminology throughout. The explanation of the plaintiff's specific pain and suffering should be detailed and compelling without being overdramatic. All calculations should be mathematically correct. Follow the exact formatting seen in the sample demand letter's pain and suffering section. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  }
]

export const case_evaluation_prompts: {
  [key: string]: {
    prompt: string
    expected_output: string
  }
} = {
  // Attorney Insights Processing
  process_attorney_insights: {
    prompt:
      'Analyze the following attorney insights and structure them for use in case evaluation:\n\n' +
      'Local Court Dynamics: {{localCourtDynamics}}\n' +
      'Defendant Info: {{defendantInfo}}\n' +
      'Client Circumstances: {{clientCircumstances}}\n' +
      'Pre-Trial Strategy: {{preTrialStrategy}}\n' +
      'Trial Readiness: {{trialReadiness}}\n' +
      'Potential Weaknesses: {{potentialWeaknesses}}\n' +
      'Comparable Cases: {{comparableCases}}\n' +
      'Attorney Gut Feel: {{attorneyGutFeel}}\n' +
      'Miscellaneous Observations: {{miscellaneousObservations}}\n\n' +
      'Return a structured JSON object with categorized insights that can be referenced by different components of the case evaluation.',
    expected_output:
      '{\n' +
      '  "courtFactors": {\n' +
      '    "judgePreferences": "Judge Smith tends to be skeptical of soft tissue claims",\n' +
      '    "juryComposition": "Conservative jurors, typically award lower damages",\n' +
      '    "localPrecedents": "Similar cases typically settle for 2-3x specials"\n' +
      '  },\n' +
      '  "opposingParty": {\n' +
      '    "defenseAttorney": "Jones & Associates - known for aggressive depositions",\n' +
      '    "insurer": "AllState - typically makes low initial offers but will increase pre-trial",\n' +
      '    "defendant": "Corporate entity with negative local reputation"\n' +
      '  },\n' +
      '  "clientFactors": {\n' +
      '    "testimony": "Client is articulate and sympathetic",\n' +
      '    "timeConstraints": "Needs resolution within 6 months due to relocation",\n' +
      '    "healthFactors": "Ongoing treatment complicates valuation"\n' +
      '  },\n' +
      '  "strategyConsiderations": {\n' +
      '    "settlementApproach": "Aggressive demand package with video demand",\n' +
      '    "expertWitnesses": "Consider neurologist for TBI component",\n' +
      '    "timelineGoals": "Push for early mediation"\n' +
      '  }\n' +
      '}'
  },

  // Liability Assessment
  assess_liability: {
    prompt:
      'Based on the following case data, assess liability for this personal injury case:\n\n' +
      'Case Data: {{context}}\n\n' +
      'Please provide:\n' +
      '1. Fault distribution (percentage for plaintiff, defendant, and any third parties)\n' +
      '2. Supporting evidence for liability\n' +
      '3. Potential defenses the opposing party might raise\n' +
      '4. Legal theory of the case\n' +
      '5. Any jurisdiction-specific considerations\n\n' +
      'Return as a JSON object matching the LiabilityAssessment interface.',
    expected_output:
      '{\n' +
      '  "faultDistribution": {\n' +
      '    "plaintiff": 10,\n' +
      '    "defendant": 90,\n' +
      '    "other": 0\n' +
      '  },\n' +
      '  "supportingEvidence": [\n' +
      '    "Police report indicates defendant ran red light",\n' +
      '    "Eyewitness testimony corroborates plaintiff\'s account",\n' +
      '    "Dashcam footage shows defendant using cell phone"\n' +
      '  ],\n' +
      '  "potentialDefenses": [\n' +
      '    "Plaintiff was speeding based on witness statement",\n' +
      '    "Weather conditions contributed to visibility issues",\n' +
      '    "Plaintiff failed to use turn signal"\n' +
      '  ],\n' +
      '  "legalTheory": "Negligence per se due to violation of traffic laws and distracted driving",\n' +
      '  "jurisdictionSpecifics": "Comparative negligence state - plaintiff\'s recovery reduced by 10%"\n' +
      '}'
  },

  // Economic Damages Calculation
  calculate_economic_damages: {
    prompt:
      'Based on the following case data, calculate economic damages:\n\n' +
      'Case Data: {{context}}\n\n' +
      'Please provide:\n' +
      '1. Medical expenses (past and future)\n' +
      '2. Lost wages/income (past and future)\n' +
      '3. Property damage\n' +
      '4. Any other economic losses\n' +
      '5. Total economic damages\n\n' +
      'Return as a JSON object matching the EconomicDamages interface.',
    expected_output:
      '{\n' +
      '  "medicalExpenses": {\n' +
      '    "past": 45000,\n' +
      '    "future": 25000,\n' +
      '    "details": "Past expenses include ER visit ($5,200), surgery ($18,500), physical therapy ($12,300), diagnostic imaging ($9,000). Future treatment includes additional physical therapy and follow-up appointments."\n' +
      '  },\n' +
      '  "lostWages": {\n' +
      '    "past": 15000,\n' +
      '    "future": 10000,\n' +
      '    "details": "Client missed 3 months of work at $5,000/month. Future lost wages account for part-time schedule for 4 months during recovery."\n' +
      '  },\n' +
      '  "propertyDamage": 22000,\n' +
      '  "otherEconomicLosses": {\n' +
      '    "transportation": 1200,\n' +
      '    "homeAssistance": 2500\n' +
      '  },\n' +
      '  "total": 120700\n' +
      '}'
  },

  // Non-Economic Damages Evaluation
  evaluate_noneconomic_damages: {
    prompt:
      'Based on the following case data, evaluate non-economic damages:\n\n' +
      'Case Data: {{context}}\n\n' +
      'Please provide:\n' +
      '1. Pain and suffering (with multiplier and justification)\n' +
      '2. Emotional distress (if applicable)\n' +
      '3. Loss of enjoyment of life (if applicable)\n' +
      '4. Disfigurement (if applicable)\n' +
      '5. Total non-economic damages\n\n' +
      'Return as a JSON object matching the NonEconomicDamages interface.',
    expected_output:
      '{\n' +
      '  "painAndSuffering": {\n' +
      '    "multiplier": 3.5,\n' +
      '    "amount": 245000,\n' +
      '    "justification": "Severe injuries requiring surgery, significant rehabilitation period of 8 months, ongoing pain and limitations"\n' +
      '  },\n' +
      '  "emotionalDistress": {\n' +
      '    "amount": 50000,\n' +
      '    "justification": "Documented anxiety and depression following accident, requiring therapy and medication"\n' +
      '  },\n' +
      '  "lossOfEnjoyment": {\n' +
      '    "amount": 75000,\n' +
      '    "justification": "Client unable to participate in previously enjoyed activities including tennis, hiking, and playing with children"\n' +
      '  },\n' +
      '  "disfigurement": {\n' +
      '    "amount": 30000,\n' +
      '    "justification": "Visible scarring from surgery and facial lacerations"\n' +
      '  },\n' +
      '  "total": 400000\n' +
      '}'
  },

  // Punitive Damages Assessment
  assess_punitive_damages: {
    prompt:
      'Based on the following case data, assess potential for punitive damages:\n\n' +
      'Case Data: {{context}}\n\n' +
      'Please provide:\n' +
      '1. Eligibility for punitive damages (true/false)\n' +
      '2. Justification for eligibility or ineligibility\n' +
      '3. Recommended multiplier (if eligible)\n' +
      '4. Estimated amount (if eligible)\n\n' +
      'Return as a JSON object matching the PunitiveDamagesData interface.',
    expected_output:
      '{\n' +
      '  "eligibility": true,\n' +
      '  "justification": "Defendant was texting while driving and had a prior DUI conviction. This demonstrates conscious disregard for safety of others.",\n' +
      '  "recommendedMultiplier": 0.5,\n' +
      '  "estimatedAmount": 260350\n' +
      '}'
  },

  // Case Risks Identification
  identify_case_risks: {
    prompt:
      'Based on the following case data, identify risks, strengths, and weaknesses:\n\n' +
      'Case Data: {{context}}\n\n' +
      'Please provide:\n' +
      '1. Case strengths (list)\n' +
      '2. Case weaknesses (list)\n' +
      '3. Mitigation strategies for each weakness\n' +
      '4. Overall risk assessment (Low, Medium, High)\n' +
      '5. Recommended exhibits or evidence to emphasize\n' +
      '6. Challenging evidence that might need to be addressed\n\n' +
      'Return as a JSON object matching the RiskAssessment interface.',
    expected_output:
      '{\n' +
      '  "caseStrengths": [\n' +
      '    "Clear liability established by police report",\n' +
      '    "Objective injuries documented by imaging",\n' +
      '    "Credible client with no prior claims history",\n' +
      '    "Strong demonstrable economic damages"\n' +
      '  ],\n' +
      '  "caseWeaknesses": [\n' +
      '    "Pre-existing back condition",\n' +
      '    "Gap in treatment between months 3-4",\n' +
      '    "Potential comparative fault for speeding",\n' +
      '    "Social media posts showing physical activity during recovery period"\n' +
      '  ],\n' +
      '  "mitigationStrategies": {\n' +
      '    "Pre-existing back condition": "Obtain expert testimony differentiating new injuries from pre-existing condition",\n' +
      '    "Gap in treatment": "Document client explanation for gap and obtain physician statement about continued pain",\n' +
      '    "Comparative fault": "Emphasize eyewitness testimony about defendant running red light",\n' +
      '    "Social media posts": "Prepare client to explain context of activities and limitations encountered"\n' +
      '  },\n' +
      '  "overallRisk": "Medium",\n' +
      '  "recommendedExhibits": [\n' +
      '    "3D rendering of surgical procedure",\n' +
      '    "Before/after day-in-life video",\n' +
      '    "Medical illustrations of injuries",\n' +
      '    "Physician video deposition"\n' +
      '  ],\n' +
      '  "challengingEvidence": [\n' +
      '    "Plaintiff\'s text messages sent moments before collision",\n' +
      '    "Prior worker\'s compensation claim for similar body region",\n' +
      '    "Surveillance footage from month 5 showing improved mobility"\n' +
      '  ]\n' +
      '}'
  },

  // Litigation Strategy Development
  develop_litigation_strategy: {
    prompt:
      'Based on the following case assessment, develop a comprehensive litigation strategy:\n\n' +
      'Case Assessment: {{context}}\n\n' +
      'Please provide:\n' +
      '1. Settlement recommendation (minimum acceptable amount, target amount, timing, approach)\n' +
      '2. Trial strategy (if case proceeds to trial)\n' +
      '   a. Key themes\n' +
      '   b. Recommended witness order\n' +
      '   c. Expert recommendations\n' +
      '   d. Jury considerations\n' +
      '3. Timeline recommendation for case progression\n\n' +
      'Return as a JSON object matching the LitigationStrategy interface.',
    expected_output:
      '{\n' +
      '  "settlementRecommendation": {\n' +
      '    "minAcceptable": 425000,\n' +
      '    "target": 650000,\n' +
      '    "timing": "Initial demand after discovery completion; consider mediation 60 days before trial date",\n' +
      '    "approach": "Initial demand of $950,000 with comprehensive demand package including day-in-life video"\n' +
      '  },\n' +
      '  "trialStrategy": {\n' +
      '    "keyThemes": [\n' +
      '      "Defendant\'s reckless behavior and disregard for safety",\n' +
      '      "Life-altering consequences for hardworking plaintiff",\n' +
      '      "Ongoing pain and limitations despite best medical intervention",\n' +
      '      "Economic security threatened by injury"\n' +
      '    ],\n' +
      '    "witnessOrder": [\n' +
      '      "Investigating officer",\n' +
      '      "Eyewitness to collision",\n' +
      '      "Treating physician",\n' +
      '      "Orthopedic expert",\n' +
      '      "Economic damages expert",\n' +
      '      "Plaintiff",\n' +
      '      "Plaintiff\'s spouse"\n' +
      '    ],\n' +
      '    "expertRecommendations": [\n' +
      '      "Dr. Johnson - orthopedic surgeon to address causation and future prognosis",\n' +
      '      "Dr. Smith - pain management specialist to address ongoing treatment needs",\n' +
      '      "Sarah Williams - economic loss expert to calculate lifetime impact",\n' +
      '      "Dr. Peterson - accident reconstructionist"\n' +
      '    ],\n' +
      '    "juryConsiderations": [\n' +
      '      "Conservative venue - focus on objective injuries and economic impacts",\n' +
      '      "Use visual aids extensively",\n' +
      '      "Address comparative fault proactively in voir dire"\n' +
      '    ]\n' +
      '  },\n' +
      '  "timelineRecommendation": "File within 30 days, complete written discovery within 90 days, depose key witnesses by month 5, mediation in month 7, trial preparation beginning month 9 for potential trial in month 12"\n' +
      '}'
  },

  // Report Sections
  case_overview: {
    prompt:
      'Generate a **Case Overview** section in Markdown format based on the following data:\n\n' +
      '{{context}}\n\n' +
      'Include a summary of the case, parties involved, incident details, and key injuries.',
    expected_output:
      '# Case Overview\n\n' +
      '**Client:** John Smith  \n' +
      '**Incident Date:** June 15, 2023  \n' +
      '**Incident Type:** Motor Vehicle Collision (Rear-End)  \n' +
      '**Venue:** Superior Court of Washington, King County  \n' +
      '**Defendant(s):** Jane Doe and ABC Delivery Company  \n' +
      '**Insurance Carrier:** National Insurance Company (Policy Limit: $500,000)  \n\n' +
      '## Incident Summary\n\n' +
      "On June 15, 2023, the client was stopped at a red light at the intersection of Main Street and 1st Avenue when his vehicle was struck from behind by a delivery van operated by Jane Doe, an employee of ABC Delivery Company. The impact pushed the client's vehicle into the intersection. Police were called to the scene and cited the defendant for following too closely. The client experienced immediate neck and back pain and was transported by ambulance to Regional Medical Center.\n\n" +
      '## Primary Injuries\n\n' +
      '- Cervical disc herniation (C5-C6)\n' +
      '- Lumbar strain\n' +
      '- Right shoulder rotator cuff tear\n' +
      '- Post-traumatic headaches\n\n' +
      '## Treatment Summary\n\n' +
      'Client has undergone extensive treatment including emergency care, orthopedic evaluation, MRI diagnostics, physical therapy (36 sessions), and arthroscopic shoulder surgery. Treatment is ongoing with continued physical therapy and pain management.'
  },

  liability_assessment: {
    prompt:
      'Generate a **Liability Assessment** section in Markdown format based on the following data:\n\n' +
      '{{context}}\n\n' +
      'Include fault distribution, supporting evidence, potential defenses, and legal theory.',
    expected_output:
      '# Liability Assessment\n\n' +
      '## Fault Distribution\n\n' +
      '- **Plaintiff:** 10%\n' +
      '- **Defendant:** 90%\n\n' +
      '## Supporting Evidence\n\n' +
      '1. **Police Report:** Officer Rodriguez documented that defendant failed to maintain safe following distance and was issued citation #T-23456.\n' +
      '2. **Eyewitness Statements:** Two independent witnesses confirmed that plaintiff was properly stopped at the red light when impact occurred.\n' +
      "3. **Vehicle Damage Photos:** Extensive rear-end damage to plaintiff's vehicle consistent with significant impact force.\n" +
      '4. **Defendant\'s Admission:** Defendant stated to responding officer that she was "briefly looking at GPS" before collision.\n\n' +
      '## Potential Defenses\n\n' +
      '1. **Sudden Stop:** Defendant may claim plaintiff stopped abruptly.\n' +
      '2. **Road Conditions:** Recent rain may be cited as contributing factor.\n' +
      "3. **Brake Light Function:** Defendant may question whether plaintiff's brake lights were functioning properly.\n\n" +
      '## Legal Theory\n\n' +
      'Negligence per se based on violation of traffic statute requiring safe following distance. Additionally, employer liability under respondeat superior as defendant was operating company vehicle during course and scope of employment.'
  },

  damages_calculation: {
    prompt:
      'Generate a **Damages Calculation** section in Markdown format based on the following data:\n\n' +
      'Economic: {{economicDamages}}\n' +
      'Non-Economic: {{nonEconomicDamages}}\n' +
      'Punitive: {{punitiveDamages}}\n' +
      'Total: {{damagesCalculation}}\n\n' +
      'Include a breakdown of all damage categories and the final calculation range.',
    expected_output: `
Your output needs to be in the following tabular markdown format:
    
# Damages Calculation

## Economic Damages

| Category | Subcategory | Amount |
|----------|-------------|--------|
| **Medical Expenses** | Emergency Room | $5,200 |
|  | Surgery | $18,500 |
|  | Physical Therapy | $12,300 |
|  | Diagnostic Imaging | $9,000 |
|  | **Past Medical Expenses Subtotal** | **$45,000** |
|  | Additional Physical Therapy | $8,000 |
|  | Pain Management | $10,000 |
|  | Future Diagnostic Testing | $7,000 |
|  | **Future Medical Expenses Subtotal** | **$25,000** |
| **Lost Income** | Past Lost Wages | $15,000 |
|  | Future Lost Earning Capacity | $10,000 |
| **Loss of Household Services** |  | $4,000 |
| **Property Damage and Other Losses** | Vehicle Damage | $22,000 |
|  | Transportation Costs | $1,200 |
|  | Home Assistance | $2,500 |
|  | **Other Economic Losses Subtotal** | **$3,700** |
| **Total Economic Damages** |  | **$120,700** |

## Non-Economic Damages

| Category | Amount | Notes |
|----------|--------|-------|
| Pain and Suffering | $245,000 | 3.5× multiplier |
| Emotional Distress | $50,000 | |
| Loss of Enjoyment | $75,000 | |
| Disfigurement | $30,000 | |
| **Total Non-Economic Damages** | **$400,000** | |

## Punitive Damages

| Category | Amount | Notes |
|----------|--------|-------|
| Punitive Damages | $260,350 | 0.5× multiplier of combined economic and non-economic damages |
| **Total Punitive Damages** | **$260,350** | Based on defendant's texting while driving and prior DUI conviction |

## Damages Summary

| Category | Amount |
|----------|--------|
| Economic Damages | $120,700 |
| Non-Economic Damages | $400,000 |
| Punitive Damages | $260,350 |
| **Total Damages** | **$781,050** |

### Comparative Fault Adjustment

| Category | Amount |
|----------|--------|
| Reduction for 10% plaintiff fault | $78,105 |

### Final Damages Range

| Estimate | Amount |
|----------|--------|
| Low-End Estimate | $492,160 |
| Expected Value | $702,945 |
| High-End Estimate | $913,830 |
      `
  },

  risk_analysis: {
    prompt:
      'Generate a **Risk Analysis** section in Markdown format based on the following data:\n\n' +
      '{{context}}\n\n' +
      'Include case strengths, weaknesses, mitigation strategies, and overall risk assessment.',
    expected_output:
      '# Risk Analysis\n\n' +
      '## Case Strengths\n\n' +
      '✅ **Clear liability** established by police report and eyewitness testimony\n' +
      '✅ **Objective injuries** documented through MRI and surgical reports\n' +
      '✅ **Credible client** with no prior claims history and consistent testimony\n' +
      '✅ **Strong economic damages** with comprehensive documentation\n\n' +
      '## Case Weaknesses\n\n' +
      '⚠️ **Pre-existing back condition** documented in medical records from 2019\n' +
      '⚠️ **Gap in treatment** between months 3-4 following the accident\n' +
      '⚠️ **Potential comparative fault** for possible speeding (10%)\n' +
      '⚠️ **Social media posts** showing physical activity during recovery period\n\n' +
      '## Mitigation Strategies\n\n' +
      '| Weakness | Mitigation Strategy |\n' +
      '|----------|---------------------|\n' +
      '| Pre-existing back condition | Obtain expert testimony differentiating new injuries from pre-existing condition with supporting medical imaging comparison |\n' +
      '| Gap in treatment | Document client explanation for gap (financial constraints) and obtain physician statement about continued pain and at-home management |\n' +
      '| Comparative fault | Emphasize eyewitness testimony about defendant running red light; obtain accident reconstruction expert |\n' +
      '| Social media posts | Prepare client to explain context of activities, limitations encountered, and pain management required afterward |\n\n' +
      '## Recommended Evidence and Exhibits\n\n' +
      '🔍 3D rendering of surgical procedure\n' +
      "🔍 Before/after day-in-life video showing impact on client's daily activities\n" +
      '🔍 Medical illustrations clearly depicting injuries\n' +
      '🔍 Physician video deposition with demonstrative aids\n\n' +
      '## Challenging Evidence to Address\n\n' +
      "⚠️ Plaintiff's text messages sent moments before collision\n" +
      "⚠️ Prior worker's compensation claim for similar body region\n" +
      '⚠️ Surveillance footage from month 5 showing improved mobility\n\n' +
      '## Overall Risk Assessment\n\n' +
      '**Risk Level: Medium**\n\n' +
      'The case presents good liability and damages evidence, but faces some challenges regarding pre-existing conditions and potential comparative fault. With proper expert testimony and client preparation, most weaknesses can be effectively mitigated. The likelihood of a favorable settlement or verdict is good, with primary uncertainty focused on the valuation of non-economic damages in this relatively conservative jurisdiction.'
  },

  litigation_strategy: {
    prompt:
      'Generate a **Litigation Strategy** section in Markdown format based on the following data:\n\n' +
      '{{context}}\n\n' +
      'Include settlement recommendations, trial strategy if applicable, and timeline.',
    expected_output:
      '# Litigation Strategy\n\n' +
      '## Settlement Strategy\n\n' +
      '### Valuation and Targets\n' +
      '- **Minimum Acceptable Settlement:** $425,000\n' +
      '- **Target Settlement:** $650,000\n' +
      '- **Initial Demand:** $950,000\n\n' +
      '### Approach\n' +
      'Present comprehensive demand package including day-in-life video, medical illustrations, and detailed economic analysis. Initial demand should be sent after completion of primary discovery but before expensive expert depositions to incentivize early resolution.\n\n' +
      '### Timing\n' +
      '- **Initial Demand:** Month 4 (after written discovery completed)\n' +
      '- **Anticipated Counteroffers:** Months 5-6\n' +
      '- **Mediation:** Month 7 (approximately 60 days before trial date)\n' +
      '- **Settlement Window:** Months 7-9\n\n' +
      '## Trial Strategy\n\n' +
      '### Key Themes\n' +
      "1. Defendant's reckless behavior and disregard for safety\n" +
      '2. Life-altering consequences for hardworking plaintiff\n' +
      '3. Ongoing pain and limitations despite best medical intervention\n' +
      '4. Economic security threatened by injury\n\n' +
      '### Witness Order and Strategy\n' +
      "1. **Investigating Officer** - Establish liability facts and defendant's admission\n" +
      "2. **Eyewitness** - Corroborate plaintiff's proper driving behavior\n" +
      '3. **Treating Physician** - Document initial injury presentation and causation\n' +
      '4. **Orthopedic Expert** - Explain surgical intervention and future prognosis\n' +
      '5. **Economic Expert** - Quantify lifetime financial impact\n' +
      '6. **Plaintiff** - Humanize case and describe impact on daily life\n' +
      "7. **Plaintiff's Spouse** - Testify to changes in relationship and home life\n\n" +
      '### Expert Witnesses\n' +
      '- **Dr. Johnson** (Orthopedic Surgeon): Address causation and future prognosis\n' +
      '- **Dr. Smith** (Pain Management): Detail ongoing treatment needs and limitations\n' +
      '- **Sarah Williams** (Economist): Calculate lifetime financial impact\n' +
      '- **Dr. Peterson** (Accident Reconstruction): Counter comparative fault arguments\n\n' +
      '### Jury Considerations\n' +
      '- Conservative venue requires emphasis on objective injuries and economic impacts\n' +
      '- Use visual aids extensively to explain complex medical concepts\n' +
      '- Address comparative fault proactively in voir dire\n' +
      '- Prepare for juror skepticism about non-economic damages\n\n' +
      '## Case Timeline\n\n' +
      '| Timeframe | Key Actions |\n' +
      '|-----------|-------------|\n' +
      '| Month 1 | File complaint, serve defendant |\n' +
      '| Month 2-3 | Complete written discovery |\n' +
      '| Month 4 | Depose key witnesses, prepare demand package |\n' +
      '| Month 5-6 | Expert disclosures, settlement negotiations |\n' +
      '| Month 7 | Mediation |\n' +
      '| Month 8-9 | Expert depositions if no settlement |\n' +
      '| Month 10-11 | Final trial preparation |\n' +
      '| Month 12 | Trial date |\n\n' +
      '**Critical Path:** Focus on completing core discovery efficiently to enable early evaluation and settlement discussions while maintaining trial readiness.'
  },

  attorney_insights: {
    prompt:
      'Generate an **Attorney Insights & Local Considerations** section in Markdown format based on the following data:\n\n' +
      '{{context}}\n\n' +
      "Focus on the attorney's subjective insights about local court dynamics, defense counsel tendencies, etc.",
    expected_output:
      '# Attorney Insights & Local Considerations\n\n' +
      '## Local Court Dynamics\n\n' +
      '- **Judge Smith** tends to be skeptical of soft tissue claims but responds well to objective medical findings and clear expert testimony.\n' +
      '- **Jury Pool** in this jurisdiction is generally conservative, comprised primarily of middle-class professionals with household incomes in the $75-100K range.\n' +
      '- **Damage Awards** in similar cases have averaged 2-3× special damages for clear liability cases with objective injuries.\n' +
      '- **Trial Calendar** currently running 12-14 months from filing to trial date.\n\n' +
      '## Defense Counsel Assessment\n\n' +
      '- **Jones & Associates** represents defendant and is known for aggressive deposition tactics and last-minute motion practice.\n' +
      '- **Primary Defense Strategy** typically involves attacking causation and treatment proportionality rather than liability.\n' +
      '- **Settlement Authority** tends to increase significantly 30-45 days before trial.\n' +
      '- **Expert Witnesses** often include Dr. Williams, who routinely testifies that injuries resolve within 6-8 weeks.\n\n' +
      '## Client-Specific Considerations\n\n' +
      '- Client has **time-sensitive financial needs** affecting settlement flexibility.\n' +
      '- Client presents as **credible and sympathetic** in deposition preparation sessions.\n' +
      "- Client's **cultural background** may affect pain reporting and description of limitations.\n" +
      '- Client has expressed strong preference for **settlement over trial** due to privacy concerns.\n\n' +
      "## Attorney's Strategic Assessment\n\n" +
      "Based on prior experience with similar cases in this venue, there is approximately a **70% probability** of settlement before trial. The most effective approach involves aggressive discovery, early compilation of compelling demonstrative evidence, and strategic timing of settlement discussions to coincide with defense counsel's typical evaluation points. If the case proceeds to trial, emphasizing the defendant's distracted driving and using strong medical illustrations will be critical to overcoming local juror skepticism about non-economic damages."
  },

  final_recommendations: {
    prompt:
      'Generate a **Final Recommendations** section in Markdown format based on the following data:\n\n' +
      '{{context}}\n\n' +
      'Provide a concise summary of key recommendations, next steps, and important considerations.',
    expected_output:
      '# Final Recommendations\n\n' +
      '## Immediate Next Steps\n\n' +
      '1. **Complete Client Medical Release** for records from 2019 back injury to assess differentiation.\n' +
      '2. **Retain Dr. Johnson** as orthopedic expert to address causation and future limitations.\n' +
      '3. **Schedule Day-in-Life Video** filming within next 14 days.\n' +
      '4. **Prepare Client** for deposition with focus on addressing social media posts and activity limitations.\n' +
      '5. **File Supplemental Discovery Responses** for recently received medical records.\n\n' +
      '## Strategic Recommendations\n\n' +
      '1. **Pursue Mediation** in Month 7 with mediator who has experience with spinal injury cases.\n' +
      '2. **Set Settlement Floor** at $425,000 with expected resolution between $550,000-$650,000.\n' +
      '3. **Maintain Trial Preparation Timeline** concurrently with settlement discussions.\n' +
      '4. **Address Gaps in Treatment** with physician affidavit explaining continued symptoms.\n' +
      '5. **Consider Lien Negotiations** with major medical providers to maximize client recovery.\n\n' +
      '## Key Considerations\n\n' +
      "- Client's financial situation creates pressure for reasonable early resolution\n" +
      "- Defense counsel typically receives increased authority after plaintiff's expert designations\n" +
      '- Potential comparative fault can be effectively mitigated but should be factored into valuation\n' +
      '- Case strengths in liability and objective injuries outweigh documentation concerns\n\n' +
      '## Case Value Assessment\n\n' +
      '- **Settlement Range:** $550,000 - $650,000 (70% probability)\n' +
      '- **Expected Trial Value:** $700,000 - $750,000 (if successful on all major elements)\n' +
      '- **Risk-Adjusted Value:** $600,000\n\n' +
      'This case presents favorable settlement prospects with appropriate client expectations management. The recommended approach balances aggressive advocacy with strategic timing to maximize recovery while minimizing expense and client stress.'
  },

  // Defense Perspective Analysis
  analyze_defense_perspective: {
    prompt:
      'Analyze the following case evaluation from the perspective of a defense attorney:\n\n' +
      'Case Evaluation: {{context}}\n\n' +
      'Initial Report:\n' +
      '{{initialReport}}\n\n' +
      'Please identify:\n' +
      "1. Key vulnerabilities in the plaintiff's case (list with category, severity, and potential impact)\n" +
      '2. Counterstrategies the defense might use for each vulnerability\n' +
      '3. An overall assessment of how the defense might approach this case\n\n' +
      'Return as a JSON object matching the DefensePerspective interface.',
    expected_output:
      '{\n' +
      '  "vulnerabilities": [\n' +
      '    {\n' +
      '      "issue": "Pre-existing back condition from 2019",\n' +
      '      "category": "Medical Causation",\n' +
      '      "severity": "High",\n' +
      '      "potentialImpact": "Could significantly reduce recovery for back-related damages by arguing exacerbation rather than new injury"\n' +
      '    },\n' +
      '    {\n' +
      '      "issue": "3-4 month gap in treatment",\n' +
      '      "category": "Treatment Consistency",\n' +
      '      "severity": "Medium",\n' +
      '      "potentialImpact": "Suggests injuries not as severe as claimed or failure to mitigate damages"\n' +
      '    },\n' +
      '    {\n' +
      '      "issue": "Social media showing physical activities",\n' +
      '      "category": "Damages Credibility",\n' +
      '      "severity": "High",\n' +
      '      "potentialImpact": "Directly contradicts claims of limitations and pain levels"\n' +
      '    },\n' +
      '    {\n' +
      '      "issue": "Potential speeding by plaintiff",\n' +
      '      "category": "Comparative Fault",\n' +
      '      "severity": "Medium",\n' +
      '      "potentialImpact": "Could increase plaintiff\'s fault percentage above current 10% estimate"\n' +
      '    },\n' +
      '    {\n' +
      '      "issue": "Plaintiff\'s text messages before collision",\n' +
      '      "category": "Comparative Fault",\n' +
      '      "severity": "High",\n' +
      '      "potentialImpact": "Could establish plaintiff was distracted, significantly increasing comparative fault"\n' +
      '    },\n' +
      '    {\n' +
      '      "issue": "Overtreatment relative to injury severity",\n' +
      '      "category": "Damages Inflation",\n' +
      '      "severity": "Medium",\n' +
      '      "potentialImpact": "Could reduce medical special damages through medical necessity challenge"\n' +
      '    }\n' +
      '  ],\n' +
      '  "counterStrategies": [\n' +
      '    {\n' +
      '      "vulnerability": "Pre-existing back condition",\n' +
      '      "strategy": "Obtain complete prior medical history and IME with records review",\n' +
      '      "implementation": "Subpoena all records from 2017-present, retain Dr. Wilson (spine specialist) to differentiate prior from new injuries"\n' +
      '    },\n' +
      '    {\n' +
      '      "vulnerability": "Treatment gap",\n' +
      '      "strategy": "Emphasize inconsistency with claimed severity",\n' +
      '      "implementation": "Depose treating physicians about expected treatment protocols, highlight lack of medical necessity documentation"\n' +
      '    },\n' +
      '    {\n' +
      '      "vulnerability": "Social media evidence",\n' +
      '      "strategy": "Comprehensive social media discovery and surveillance",\n' +
      '      "implementation": "File specific discovery requests, potentially hire investigator for additional surveillance, prepare video compilation for settlement conference"\n' +
      '    },\n' +
      '    {\n' +
      '      "vulnerability": "Comparative fault",\n' +
      '      "strategy": "Accident reconstruction and witness impeachment",\n' +
      '      "implementation": "Retain accident reconstruction expert focused on plaintiff\'s speed, obtain cell phone records, depose witnesses about plaintiff\'s driving"\n' +
      '    }\n' +
      '  ],\n' +
      '  "overallAssessment": "This case presents several promising avenues for defense. The combination of pre-existing conditions, treatment inconsistencies, and potential comparative fault creates leverage for a substantial reduction from plaintiff\'s demand. The defense should focus on early and aggressive discovery of prior medical records and social media, followed by strategic expert retention to challenge causation and treatment necessity. Settlement value from defense perspective is likely in the $350,000-$400,000 range, with strong potential to increase plaintiff\'s comparative fault percentage at trial. The case should be positioned for mediation after completion of discovery but before incurring significant expert costs."\n' +
      '}'
  },

  // Revised Report Generation
  generate_revised_report: {
    prompt:
      'You are tasked with revising a case evaluation report based on an analysis from the defense perspective.\n' +
      "Your goal is to strengthen the plaintiff's case by addressing potential vulnerabilities.\n\n" +
      'Original Case Evaluation:\n' +
      '{{caseEvaluation}}\n\n' +
      'Defense Perspective Analysis:\n' +
      '{{defensePerspective}}\n\n' +
      'Original Report:\n' +
      '{{initialReport}}\n\n' +
      'Please generate a revised report that:\n' +
      '1. MUST include ALL sections from the original report in the EXACT same order:\n' +
      '   - Case Overview\n' +
      '   - Liability Assessment\n' +
      '   - Damages Calculation\n' +
      '   - Past Medical Expenses\n' +
      '   - Future Medical Expenses\n' +
      '   - Risk Analysis\n' +
      '   - Litigation Strategy\n' +
      '   - Attorney Insights & Local Considerations\n' +
      '   - Final Recommendations\n' +
      '2. For each section, either improve the content or keep the original content if no improvements are needed\n' +
      '3. Incorporates mitigation strategies for each vulnerability identified\n' +
      '4. Strengthens arguments where the defense might attack\n' +
      '5. Adds a new section called "Defense Counterarguments and Responses" at the end\n' +
      '6. Makes any other necessary adjustments to create a more robust case\n\n' +
      'IMPORTANT: Do NOT skip any sections. If a section shows "see original report" or similar placeholder text, you must replace it with actual detailed content based on the case evaluation data provided.\n\n' +
      'Return the revised report in Markdown format.',
    expected_output:
      '# Case Evaluation Report (Revised)\n\n' +
      '[All original sections with targeted improvements and strengthened arguments]\n\n' +
      '# Defense Counterarguments and Responses\n\n' +
      '## Anticipated Defense Arguments\n\n' +
      '### Pre-existing Back Condition\n' +
      "**Defense Argument:** Client's current back complaints are primarily related to pre-existing 2019 condition rather than the accident.\n\n" +
      '**Our Response:**\n' +
      '- Medical records show client was asymptomatic for 18 months prior to accident\n' +
      '- Dr. Johnson will testify to objective changes on MRI comparison (pre vs. post-accident)\n' +
      '- Will demonstrate distinct injury mechanism and different pain patterns\n' +
      '- Pre-accident activities (verify recreational activities participation records) demonstrate pre-accident functionality\n\n' +
      '### Treatment Gap\n' +
      '**Defense Argument:** 3-month gap in treatment indicates injuries were not severe or continuous.\n\n' +
      '**Our Response:**\n' +
      '- Secure affidavit from treating physician documenting home exercise protocol during gap\n' +
      '- Client diary entries documenting continued symptoms during gap period\n' +
      '- Insurance authorization records showing efforts to continue treatment\n' +
      '- Financial constraints documentation explaining temporary inability to continue treatment\n\n' +
      '[Additional defense arguments and responses]'
  }
}
