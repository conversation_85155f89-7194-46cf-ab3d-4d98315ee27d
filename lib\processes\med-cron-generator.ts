import { createCompletion } from '../services/openai-service'
import { createAzureCompletion } from '../services/azure-openai-service'
import {
  generateTreatmentCalendar,
  generateTimelineMarkdown
} from '../utils/med-cron-report-utils'
import pLimit from 'p-limit'
import { db } from '../db'
import {
  CaseGap,
  ChronologyEvent,
  DocumentContent,
  IncidentDetails,
  MedicalChronology,
  MedicalChronologyBase,
  PlaintiffInfo,
  SourceLink
} from '@/types/case'
import { MedicalChronologyStrategyFormData } from '@/components/elements/forms/medical-chronology-strategy-form'
import { AuthUser } from 'next-auth'
import { logger } from './utils-llm'
import { GPTModel } from '@/types'
import { deduplicateEvents } from '../actions/case/med-cron-pro'

// Maximum number of concurrent API requests
const MAX_CONCURRENT_REQUESTS = 4
const MAX_CONSECUTIVE_ERRORS = 20

// Chunk size for document processing
const CHUNK_SIZE = 2000

// Error collector utility
function createErrorCollector(): ErrorCollector {
  return {
    errors: [],
    consecutiveErrors: 0,
    hasError: false,
    addError(error: string, context?: string) {
      const errorMessage = context ? `[${context}] ${error}` : error
      this.errors.push(errorMessage)
      this.consecutiveErrors++
      this.hasError = true
      logger.error(
        'ErrorCollector',
        `Error #${this.consecutiveErrors}: ${errorMessage}`
      )
    },
    reset() {
      this.consecutiveErrors = 0
    },
    shouldExitProcess() {
      return this.consecutiveErrors >= MAX_CONSECUTIVE_ERRORS
    },
    getErrorSummary() {
      return {
        totalErrors: this.errors.length,
        consecutiveErrors: this.consecutiveErrors,
        recentErrors: this.errors.slice(-10),
        shouldExit: this.shouldExitProcess()
      }
    }
  }
}

interface ErrorCollector {
  errors: string[]
  consecutiveErrors: number
  hasError: boolean
  addError: (error: string, context?: string) => void
  reset: () => void
  shouldExitProcess: () => boolean
  getErrorSummary: () => {
    totalErrors: number
    consecutiveErrors: number
    recentErrors: string[]
    shouldExit: boolean
  }
}

// Interface for extracted events
interface ExtractedEvent {
  event: string
  eventType: string
  eventDescription: string
  timestamp: string
  estimatedTime: boolean
  confidence: number
  pageReference?: string
  sourceDocumentId?: number | string
  docType?: string
}

// Utility function to split documents into chunks
function splitDocumentIntoChunks(content: string): string[] {
  const chunks: string[] = []
  for (let i = 0; i < content.length; i += CHUNK_SIZE) {
    chunks.push(content.substring(i, i + CHUNK_SIZE))
  }
  return chunks
}

// Utility function to chunk arrays
function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size))
  }
  return chunks
}

// Main function to orchestrate the entire process
export async function generateMedicalChronology(
  documents: Record<string, DocumentContent[]>,
  user: AuthUser,
  strategyInputs: MedicalChronologyStrategyFormData,
  binderId?: string
): Promise<MedicalChronology> {
  logger.start('generateMedicalChronology', {
    documentCount: Object.values(documents).flat().length
  })

  try {
    // Create error collector for deduplication
    const errorCollector = createErrorCollector()

    // Clean up existing events if binderId is provided
    if (binderId) {
      logger.info('Cleaning up existing events')
      await cleanupExistingEvents(binderId, false)
    }

    // Step 1: Extract data from documents and store in database
    logger.info('Starting data extraction from documents')
    const extractedData = await extractDataFromDocuments(
      documents,
      user,
      strategyInputs,
      binderId
    )

    // Step 1.5: Deduplicate events if binderId is provided
    if (binderId) {
      logger.info('Starting event deduplication')
      await deduplicateEvents(binderId, errorCollector, strategyInputs, user)

      // Fetch deduplicated events from database
      logger.info('Fetching deduplicated events')
      const deduplicatedEvents = await fetchDeduplicatedEvents(
        binderId,
        documents
      )

      // Update extractedData with deduplicated events
      Object.keys(extractedData).forEach((docType) => {
        extractedData[docType] = extractedData[docType].map((doc: any) => {
          const docEvents = deduplicatedEvents.filter(
            (event: any) => event.sourceDocumentId === doc.sourceDocumentId
          )
          return {
            ...doc,
            events: docEvents
          }
        })
      })
    }

    // Steps 2 and 3 can run in parallel
    logger.info('Starting parallel processing of metadata and events')
    const [metadataResult, eventsResult] = await Promise.all([
      // Step 2: Structure metadata fields
      structureMetadata(extractedData),

      // Step 3: Organize events chronologically
      organizeChronologicalEvents(extractedData)
    ])

    // Process user strategy inputs if provided
    logger.info('Processing user strategy inputs')
    const processedStrategy = strategyInputs
      ? await processUserStrategyInputs(strategyInputs)
      : null

    const { plaintiffInfo, incidentDetails } = metadataResult
    const events = eventsResult

    // Step 4: Link source documents - This is quick and doesn't need an API call
    logger.info('Linking source documents')
    const sourceLinks = await linkSourceDocuments(events, documents)

    // Steps 5: Identify legal gaps and case strength
    logger.info(
      'Identifying legal gaps and case strength based on extracted data'
    )
    // Step 5: Identify legal gaps and case strength
    const caseGaps = await identifyLegalGaps(
      events,
      plaintiffInfo,
      incidentDetails,
      documents
    )

    logger.info('Generating final markdown report')

    // Step 6: Generate the final markdown report
    const markdownReport = await generateMarkdownReport(
      {
        plaintiffInfo,
        incidentDetails,
        events,
        sourceLinks,
        caseGaps
      },
      processedStrategy
    )

    const result = {
      plaintiffInfo,
      incidentDetails,
      events,
      sourceLinks,
      caseGaps,
      markdownReport,
      strategyInputs: strategyInputs ?? undefined
    }

    logger.end('generateMedicalChronology')
    return result
  } catch (error: any) {
    logger.error('generateMedicalChronology', error)
    throw new Error(`Failed to generate medical chronology: ${error.message}`)
  }
}

// Step 1: Extract data from documents - Now with parallelization and throttling
async function extractDataFromDocumentsBasic(
  documents: Record<string, DocumentContent[]>
): Promise<Record<string, any>> {
  logger.start('extractDataFromDocuments')

  try {
    const extractedData: Record<string, any> = {}
    const limit = pLimit(MAX_CONCURRENT_REQUESTS)
    const extractionPromises: Promise<void>[] = []

    // Setup the structure for each document type
    for (const docType of Object.keys(documents)) {
      extractedData[docType] = []
    }

    const docExtractionPrompt = await db.prompt.findFirst({
      where: {
        source: 'AI_MEDICAL_CHRONOLOGY_DOCUMENT_EXTRACTION'
      }
    })

    // Create a flat list of all extraction tasks
    for (const [docType, docs] of Object.entries(documents)) {
      for (const doc of docs) {
        extractionPromises.push(
          limit(async () => {
            logger.info(
              `Extracting data from ${docType} document: "${doc.title}" (ID: ${doc.id})`
            )

            const docExtractionPromptText = docExtractionPrompt?.prompt
              .replace('{{context}}', doc.content)
              .replace('{{docType}}', docType)
              .replace('{{docTitle}}', doc.title)

            // Call OpenAI to extract structured data from the document
            const prompt =
              docExtractionPromptText + docExtractionPrompt!.expectedOutput!

            try {
              const response = await createCompletion({
                messages: [{ role: 'user', content: prompt }],
                temperature: 0.1
              })

              // Parse the structured data from the response
              try {
                const extractedDocData = JSON.parse(response)
                extractedDocData.sourceDocumentId = doc.id
                extractedDocData.docType = docType
                extractedData[docType].push(extractedDocData)
                logger.info(
                  `Successfully extracted data from ${docType} document: "${doc.title}" (ID: ${doc.id})`
                )
              } catch (parseError) {
                logger.error(
                  `Error parsing OpenAI response for document ${doc.id}`,
                  response
                )
                // Handle gracefully by adding partial data
                extractedData[docType].push({
                  sourceDocumentId: doc.id,
                  docType,
                  error: 'Failed to parse structured data',
                  rawContent: response
                })
              }
            } catch (apiError: any) {
              logger.error(`API error for document ${doc.id}`, apiError)
              // Handle API errors gracefully
              extractedData[docType].push({
                sourceDocumentId: doc.id,
                docType,
                error: `API error: ${apiError.message}`,
                partialContent: doc.content.substring(0, 100) + '...'
              })
            }
          })
        )
      }
    }

    // Wait for all extraction tasks to complete
    await Promise.all(extractionPromises)

    logger.end('extractDataFromDocuments', {
      documentTypes: Object.keys(extractedData).length,
      totalDocuments: Object.values(extractedData).flat().length
    })

    return extractedData
  } catch (error: any) {
    logger.error('extractDataFromDocuments', error)
    throw new Error(`Failed to extract data from documents: ${error.message}`)
  }
}

async function extractDataFromDocuments(
  documents: Record<string, DocumentContent[]>,
  user?: AuthUser,
  strategyInputs?: MedicalChronologyStrategyFormData | null,
  binderId?: string
): Promise<Record<string, any>> {
  logger.start('extractDataFromDocuments')

  try {
    const extractedData: Record<string, any> = {}
    const limit = pLimit(MAX_CONCURRENT_REQUESTS)
    const extractionPromises: Promise<void>[] = []

    // Setup the structure for each document type
    for (const docType of Object.keys(documents)) {
      extractedData[docType] = []
    }

    // Create a flat list of all extraction tasks
    for (const [docType, docs] of Object.entries(documents)) {
      for (const doc of docs) {
        extractionPromises.push(
          limit(async () => {
            logger.info(
              `Extracting data from ${docType} document: "${doc.title}" (ID: ${doc.id})`
            )

            try {
              // Split document into chunks for processing
              const chunks = splitDocumentIntoChunks(doc.content)
              let allEvents: ExtractedEvent[] = []
              let latestEvents: ExtractedEvent[] = []

              // Process chunks with event extraction
              for (let i = 0; i < chunks.length; i++) {
                const chunk = chunks[i]
                const pageReference = `${i + 1}`

                try {
                  const chunkEvents = await extractEventsFromChunk(
                    chunk,
                    doc.title,
                    latestEvents,
                    strategyInputs,
                    user!
                  )

                  // Add source document info to each event
                  const eventsWithSource = chunkEvents.map((event) => ({
                    ...event,
                    sourceDocumentId: doc.id,
                    docType,
                    pageReference
                  }))

                  allEvents.push(...eventsWithSource)

                  // Keep latest events for context (limit to 5 for memory)
                  latestEvents = eventsWithSource.slice(-5)
                } catch (chunkError) {
                  logger.error(
                    `Error processing chunk ${i + 1} of document ${doc.id}`,
                    chunkError
                  )
                  // Continue with other chunks
                }
              }

              // Store events in database if binderId is provided
              if (binderId && allEvents.length > 0) {
                try {
                  for (const event of allEvents) {
                    await db.documentEvent.create({
                      data: {
                        binderId,
                        documentId: doc.id,
                        pageRange: event.pageReference || '1',
                        event: event.event,
                        eventType: event.eventType,
                        eventDescription: event.eventDescription,
                        timestamp: new Date(event.timestamp),
                        estimatedTime: event.estimatedTime,
                        rawExtractedData: event as any,
                        processed: false
                      }
                    })
                  }
                  logger.info(
                    `Stored ${allEvents.length} events in database for document ${doc.id}`
                  )
                } catch (dbError) {
                  logger.error(
                    `Error storing events in database for document ${doc.id}`,
                    dbError
                  )
                }
              }

              // Store extracted events in the format expected by downstream functions
              extractedData[docType].push({
                sourceDocumentId: doc.id,
                docType,
                title: doc.title,
                events: allEvents,
                // Legacy format for compatibility
                rawContent: doc.content.substring(0, 500) + '...',
                extractedEventsCount: allEvents.length
              })

              logger.info(
                `Successfully extracted ${allEvents.length} events from ${docType} document: "${doc.title}" (ID: ${doc.id})`
              )
            } catch (docError: any) {
              logger.error(`Error processing document ${doc.id}`, docError)
              // Handle document errors gracefully
              extractedData[docType].push({
                sourceDocumentId: doc.id,
                docType,
                title: doc.title,
                error: `Document processing error: ${docError.message}`,
                events: [],
                partialContent: doc.content.substring(0, 100) + '...'
              })
            }
          })
        )
      }
    }

    // Wait for all extraction tasks to complete
    await Promise.all(extractionPromises)

    logger.end('extractDataFromDocuments', {
      documentTypes: Object.keys(extractedData).length,
      totalDocuments: Object.values(extractedData).flat().length,
      totalEvents: Object.values(extractedData)
        .flat()
        .reduce((sum, doc: any) => sum + (doc.events?.length || 0), 0)
    })

    return extractedData
  } catch (error: any) {
    logger.error('extractDataFromDocuments', error)
    throw new Error(`Failed to extract data from documents: ${error.message}`)
  }
}

// Extract events from text chunk using LLM (simplified from pro version)
async function extractEventsFromChunk(
  chunk: string,
  documentTitle: string,
  latestEvents: ExtractedEvent[],
  strategyInputs: MedicalChronologyStrategyFormData | null | undefined,
  user: AuthUser
): Promise<ExtractedEvent[]> {
  const prompt = `Extract medical and legally relevant events from this document chunk for mass tort litigation and demand letter preparation. Focus on events that demonstrate injury causation, medical damages, pain and suffering, and liability.

IMPORTANT - EVENT CONSOLIDATION PRINCIPLE:
Consolidate multiple related activities that occur at the same time and location into a single comprehensive event. For example:
- If multiple medications are prescribed/purchased at one pharmacy visit → Single "Medication pickup" event listing all medications
- If multiple tests are performed during one medical appointment → Single "Diagnostic testing" event describing all tests
- If multiple treatments are received in one therapy session → Single "Therapy session" event covering all treatments

Document: ${documentTitle}
Content: ${chunk}

${latestEvents.length > 0 ? 'Recent events:\n' + latestEvents.map((e) => `- ${e.event} (${e.timestamp})`).join('\n') : 'No recent events.'}

${
  strategyInputs?.treatmentTimelineGuidelines?.trim()
    ? `
STRATEGY GUIDANCE - Treatment Timeline Guidelines:
${strategyInputs.treatmentTimelineGuidelines}

Follow these specific treatment timeline guidelines when extracting events.
`
    : ''
}

${
  strategyInputs?.prospectiveCareGuidelines?.trim()
    ? `
STRATEGY GUIDANCE - Prospective Care & Follow-Up Guidelines:
${strategyInputs.prospectiveCareGuidelines}

When extracting events related to future care, recommendations, or follow-up treatment, prioritize according to these prospective care guidelines.
`
    : ''
}

Return JSON array of events with this structure:
{
  "events": [
    {
      "event": "Comprehensive description of what happened",
      "eventType": "medical_treatment|emergency_visit|diagnostic_test|injury_report|police_report|medication|surgery|therapy|expert_opinion|legal_consultation|disability_assessment|work_restriction|pain_documentation",
      "eventDescription": "Detailed description including ALL activities, treatments, prescriptions, tests, or procedures",
      "timestamp": "YYYY-MM-DD HH:MM",
      "estimatedTime": true/false,
      "confidence": 0.1-1.0,
      "pageReference": "page number if available"
    }
  ]
}

Date Requirements:
- Only extract events with identifiable dates in ISO format (YYYY-MM-DD HH:MM) parsable with Date.parse()
- For estimated dates, use context clues and mark estimatedTime: true
- If exact time unknown, estimate or use 12:00 and mark estimatedTime: true

CONSOLIDATE BEFORE EXTRACTING:
Before creating your JSON response, review all potential events and consolidate those that occurred at the same time and place. Aim for fewer, more comprehensive events rather than many small, fragmented ones.`

  try {
    const response = await createAzureCompletion({
      messages: [
        {
          role: 'system',
          content: prompt
        }
      ],
      model: GPTModel.GPTo4Mini,
      json: true,
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'event-extraction'
    })

    const events: ExtractedEvent[] = response.events || []

    // Validate and filter events
    const validEvents = events.filter((event) => {
      try {
        // Validate timestamp format
        const date = new Date(event.timestamp)
        return !isNaN(date.getTime()) && event.event && event.eventDescription
      } catch {
        return false
      }
    })

    return validEvents
  } catch (error: any) {
    logger.error('extractEventsFromChunk', error)
    // Return empty array on error to prevent pipeline failure
    return []
  }
}

// Step 2: Structure metadata fields
async function structureMetadata(
  extractedData: Record<string, any>
): Promise<{ plaintiffInfo: PlaintiffInfo; incidentDetails: IncidentDetails }> {
  logger.start('structureMetadata')

  try {
    // Prepare a consolidated view of the extracted data for OpenAI
    const consolidatedData = JSON.stringify(extractedData)

    const structureMetadataPrompt = await db.prompt.findFirst({
      where: {
        source: 'AI_MEDICAL_CHRONOLOGY_STRUCTURE_METADATA'
      }
    })

    const prompt =
      structureMetadataPrompt!.prompt.replace(
        '{{consolidatedData}}',
        consolidatedData
      )! + structureMetadataPrompt!.expectedOutput!

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    })

    // Parse the structured metadata from the response
    const metadata = JSON.parse(response)

    // Ensure the returned data has the expected structure
    const plaintiffInfo: PlaintiffInfo = {
      fullName: metadata.plaintiffInfo.fullName || 'Unknown',
      dateOfBirth: metadata.plaintiffInfo.dateOfBirth || 'Unknown',
      preExistingConditions: metadata.plaintiffInfo.preExistingConditions || []
    }

    const incidentDetails: IncidentDetails = {
      date: metadata.incidentDetails.date || 'Unknown',
      description: metadata.incidentDetails.description || 'Unknown',
      primaryInjuries: metadata.incidentDetails.primaryInjuries || [],
      treatmentHistory: metadata.incidentDetails.treatmentHistory || ''
    }

    logger.end('structureMetadata', {
      plaintiff: plaintiffInfo.fullName,
      incidentDate: incidentDetails.date
    })

    return { plaintiffInfo, incidentDetails }
  } catch (error: any) {
    logger.error('structureMetadata', error)
    throw new Error(`Failed to structure metadata: ${error.message}`)
  }
}

// Step 3: Process user strategy inputs
async function processUserStrategyInputs(
  strategyInputs: MedicalChronologyStrategyFormData
): Promise<any> {
  logger.start('processUserStrategyInputs')

  try {
    const processStrategyPrompt = await db.prompt.findFirst({
      where: {
        source: 'AI_MEDICAL_CHRONOLOGY_PROCESS_STRATEGY'
      }
    })

    // Convert raw form data into structured strategy that can be used by other functions
    const prompt =
      processStrategyPrompt!.prompt
        .replace(
          '{{treatmentTimelineGuidelines}}',
          strategyInputs.treatmentTimelineGuidelines || ''
        )
        .replace(
          '{{prospectiveCareGuidelines}}',
          strategyInputs.prospectiveCareGuidelines || ''
        ) + processStrategyPrompt!.expectedOutput

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    })

    const structuredStrategy = JSON.parse(response)

    // Store the original form data for reference
    structuredStrategy.rawFormData = strategyInputs

    logger.end('processUserStrategyInputs')
    return structuredStrategy
  } catch (error: any) {
    logger.error('processUserStrategyInputs', error)
    throw new Error(`Failed to process user strategy inputs: ${error.message}`)
  }
}

async function organizeChronologicalEvents(
  extractedData: Record<string, any>
): Promise<ChronologyEvent[]> {
  logger.start('organizeChronologicalEvents')

  try {
    // Prepare a consolidated view of the extracted data for OpenAI
    const consolidatedData = JSON.stringify(extractedData)

    const organizeChronologicalEventsPrompt = await db.prompt.findFirst({
      where: {
        source: 'AI_MEDICAL_CHRONOLOGY_ORGANIZE_EVENTS'
      }
    })

    const prompt =
      organizeChronologicalEventsPrompt!.prompt.replace(
        '{{consolidatedData}}',
        consolidatedData
      )! + organizeChronologicalEventsPrompt!.expectedOutput!

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    })

    // Parse the events from the response
    try {
      const events: ChronologyEvent[] = JSON.parse(response).events || []

      if (!events.length) {
        throw new Error('No events found in the response')
      }

      // Sort events by date
      const sortedEvents = events.sort(
        (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
      )

      logger.end('organizeChronologicalEvents', {
        eventCount: sortedEvents.length
      })
      return sortedEvents
    } catch (parseError) {
      logger.error('organizeChronologicalEvents', response)
      throw new Error('Failed to parse chronological events from AI response')
    }
  } catch (error: any) {
    logger.error('organizeChronologicalEvents', error)
    throw new Error(`Failed to organize chronological events: ${error.message}`)
  }
}

// Step 4: Link source documents
async function linkSourceDocuments(
  events: ChronologyEvent[],
  documents: Record<string, DocumentContent[]>
): Promise<SourceLink[]> {
  logger.start('linkSourceDocuments')

  try {
    const sourceLinks: SourceLink[] = []

    // Flatten the documents array for easier lookup
    const allDocuments = Object.values(documents).flat()

    // Create source links for each event
    for (let i = 0; i < events.length; i++) {
      const event = events[i]

      // Find the corresponding document
      const document = allDocuments.find(
        (doc) => doc.id === event.sourceDocumentId
      )

      if (document) {
        sourceLinks.push({
          eventId: `event-${i}`, // Generate an ID for the event
          documentId: document.id,
          pageReferences: event.sourcePageReferences || 'N/A'
        })
      }
    }

    logger.end('linkSourceDocuments', { linkCount: sourceLinks.length })
    return sourceLinks
  } catch (error: any) {
    logger.error('linkSourceDocuments', error)
    throw new Error(`Failed to link source documents: ${error.message}`)
  }
}

// Step 5: Identify legal gaps and case strength
async function identifyLegalGaps(
  events: ChronologyEvent[],
  plaintiffInfo: PlaintiffInfo,
  incidentDetails: IncidentDetails,
  documents: Record<string, DocumentContent[]>
): Promise<CaseGap[]> {
  logger.start('identifyLegalGaps')

  try {
    // Prepare the data for OpenAI
    const data = {
      events,
      plaintiffInfo,
      incidentDetails,
      documentTypes: Object.keys(documents)
    }

    const caseInformation = JSON.stringify(data)

    const identifyLegalGapsPrompt = await db.prompt.findFirst({
      where: {
        source: 'AI_MEDICAL_CHRONOLOGY_CASE_GAPS'
      }
    })

    const prompt =
      identifyLegalGapsPrompt!.prompt.replace(
        '{{caseInformation}}',
        caseInformation
      ) + identifyLegalGapsPrompt!.expectedOutput!

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.2
    })

    // Parse the identified gaps from the response
    try {
      const caseGaps: CaseGap[] = JSON.parse(response).caseGaps || []

      // Count issues by severity
      const severityCounts = caseGaps.reduce(
        (counts, gap) => {
          counts[gap.severity] = (counts[gap.severity] || 0) + 1
          return counts
        },
        {} as Record<string, number>
      )

      logger.end('identifyLegalGaps', {
        gapCount: caseGaps.length,
        severityCounts
      })

      return caseGaps
    } catch (parseError) {
      logger.error('identifyLegalGaps', response)
      throw new Error('Failed to parse case gaps from AI response')
    }
  } catch (error: any) {
    logger.error('identifyLegalGaps', error)
    throw new Error(`Failed to identify legal gaps: ${error.message}`)
  }
}

// Step 6: Generate the final markdown report
async function generateMarkdownReport(
  chronology: MedicalChronologyBase,
  processedStrategy?: any | null
): Promise<string> {
  logger.start('generateMarkdownReport')
  const medChronPrompts = await db.prompt.findMany({
    where: {
      source: {
        in: [
          'AI_MEDICAL_CHRONOLOGY_CASE_INFORMATION_METADATA',
          'AI_MEDICAL_CHRONOLOGY_DIAGNOSTIC_HIGHLIGHTS',
          'AI_MEDICAL_CHRONOLOGY_TREATMENT_CALENDAR',
          'AI_MEDICAL_CHRONOLOGY_FLAGS_AND_CASE_GAPS',
          'AI_MEDICAL_CHRONOLOGY_TREATMENT_TIMELINE',
          'AI_MEDICAL_CHRONOLOGY_CASE_STRENGTH_ANALYSIS'
        ]
      }
    }
  })

  const sortedPrompts = medChronPrompts.sort((a, b) => {
    const order = [
      'AI_MEDICAL_CHRONOLOGY_CASE_INFORMATION_METADATA',
      'AI_MEDICAL_CHRONOLOGY_DIAGNOSTIC_HIGHLIGHTS',
      'AI_MEDICAL_CHRONOLOGY_TREATMENT_CALENDAR',
      'AI_MEDICAL_CHRONOLOGY_FLAGS_AND_CASE_GAPS',
      'AI_MEDICAL_CHRONOLOGY_TREATMENT_TIMELINE',
      'AI_MEDICAL_CHRONOLOGY_CASE_STRENGTH_ANALYSIS'
    ]
    return order.indexOf(a.source) - order.indexOf(b.source)
  })

  try {
    // Prepare the data for OpenAI
    const chronologyWithoutMarkdown = { ...chronology } as any
    delete chronologyWithoutMarkdown.markdownReport

    // Generate sections - some using imported utility functions, others using LLM
    const sectionResults: string[] = []

    for (const prompt of sortedPrompts) {
      try {
        let sectionContent = ''

        // Use imported utility functions for calendar and timeline sections
        if (prompt.source === 'AI_MEDICAL_CHRONOLOGY_TREATMENT_CALENDAR') {
          const documentEvents = convertChronologyEventsToDocumentEvents(
            chronology.events
          )
          sectionContent = generateTreatmentCalendar(documentEvents)
        } else if (
          prompt.source === 'AI_MEDICAL_CHRONOLOGY_TREATMENT_TIMELINE'
        ) {
          const timelineEvents = convertChronologyEventsToTimelineEvents(
            chronology.events
          )
          sectionContent = generateTimelineMarkdown(timelineEvents)
        } else {
          // Use LLM for other sections
          let sectionPrompt = prompt.prompt + prompt.expectedOutput
          let contextualPrompt = sectionPrompt.replace(
            '{{context}}',
            JSON.stringify(chronologyWithoutMarkdown)
          )

          // Special handling for treatment timeline section with strategy (not used anymore but kept for consistency)
          if (
            prompt.source === 'AI_MEDICAL_CHRONOLOGY_TREATMENT_TIMELINE' &&
            processedStrategy
          ) {
            contextualPrompt = contextualPrompt
              .replace(
                '{{partAGuidelines}}',
                JSON.stringify(processedStrategy.partAGuidelines || '')
              )
              .replace(
                '{{partBGuidelines}}',
                JSON.stringify(processedStrategy.partBGuidelines || '')
              )
          }

          // Include chronology data with the prompt
          sectionContent = await createCompletion({
            messages: [
              {
                role: 'user',
                content: contextualPrompt
              }
            ],
            temperature: 0.2,
            json: false
          })
        }

        sectionResults.push(sectionContent)
      } catch (error: any) {
        // Log error but don't fail the entire report
        logger.error(`generateMarkdownReport:${prompt.source}`, error)
        sectionResults.push('') // Add empty string for failed sections
      }
    }

    // Combine all sections in the correct order
    const combinedReport = sectionResults.join('\n\n')

    logger.end('generateMarkdownReport', {
      reportLength: combinedReport.length,
      sections: combinedReport.split('##').length - 1
    })

    return combinedReport
  } catch (error: any) {
    logger.error('generateMarkdownReport', error)
    throw new Error(`Failed to generate markdown report: ${error.message}`)
  }
}

// Adapter functions to convert ChronologyEvent to formats expected by imported utilities
function convertChronologyEventsToDocumentEvents(events: ChronologyEvent[]): {
  id: number
  binderId: string
  documentId: number | string
  pageRange: string
  event: string
  eventType: string
  eventDescription: string
  timestamp: Date
  estimatedTime: boolean
  rawExtractedData: string
  processed: boolean
  createdAt: Date
  updatedAt: Date
}[] {
  return events.map((event) => ({
    id: Math.random(), // Not used in the function
    binderId: 'temp', // Not used in the function
    documentId: event.sourceDocumentId,
    pageRange: event.sourcePageReferences || '1',
    event: event.title,
    eventType: convertCategoryToEventType(event.category),
    eventDescription: event.summary,
    timestamp: new Date(event.date),
    estimatedTime: false,
    rawExtractedData: JSON.stringify(event),
    processed: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }))
}

function convertChronologyEventsToTimelineEvents(events: ChronologyEvent[]): {
  timestamp: string
  event: string
  eventType: string
  eventDescription: string
  documentId: number | string
  pageRange: string
}[] {
  return events.map((event) => ({
    timestamp: event.date,
    event: event.title,
    eventType: convertCategoryToEventType(event.category),
    eventDescription: event.summary,
    documentId: event.sourceDocumentId,
    pageRange: event.sourcePageReferences || '1'
  }))
}

function convertCategoryToEventType(category: string): string {
  const mapping: Record<string, string> = {
    PoliceReport: 'police_report',
    AmbulanceReport: 'emergency_visit',
    EDAdmission: 'emergency_visit',
    ImagingDiagnostics: 'diagnostic_test',
    Treatment: 'medical_treatment',
    EDDischarge: 'emergency_visit',
    FollowUp: 'medical_treatment'
  }
  return mapping[category] || 'medical_treatment'
}

// Helper function to fetch deduplicated events from database
async function fetchDeduplicatedEvents(
  binderId: string,
  documents: Record<string, DocumentContent[]>
): Promise<ExtractedEvent[]> {
  const events = await db.documentEvent.findMany({
    where: { binderId },
    orderBy: { timestamp: 'asc' }
  })

  return events.map((event) => ({
    event: event.event,
    eventType: event.eventType,
    eventDescription: event.eventDescription,
    timestamp: event.timestamp.toISOString(),
    estimatedTime: event.estimatedTime,
    confidence: 0.9, // Default confidence for stored events
    pageReference: event.pageRange,
    sourceDocumentId: event.documentId,
    docType: findDocTypeForDocument(event.documentId, documents)
  }))
}

// Helper function to find document type for a document ID
function findDocTypeForDocument(
  documentId: number,
  documents: Record<string, DocumentContent[]>
): string {
  for (const [docType, docs] of Object.entries(documents)) {
    if (docs.some((doc) => doc.id === documentId)) {
      return docType
    }
  }
  return 'unknown'
}

// Helper function to clean up existing events for a binder
async function cleanupExistingEvents(
  binderId: string,
  forceCleanup: boolean = false
) {
  if (forceCleanup) {
    // Delete all existing events for this binder
    await db.documentEvent.deleteMany({
      where: { binderId }
    })
    logger.info(`Cleaned up all existing events for binder: ${binderId}`)
  } else {
    // Only delete unprocessed events to allow for re-processing
    const deletedCount = await db.documentEvent.deleteMany({
      where: { binderId, processed: false }
    })
    logger.info(
      `Cleaned up ${deletedCount.count} unprocessed events for binder: ${binderId}`
    )
  }
}
