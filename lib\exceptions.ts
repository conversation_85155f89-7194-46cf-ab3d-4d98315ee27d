export class RequiresProPlanError extends Error {
  constructor(message = 'This action requires a pro plan') {
    super(message)
  }
}

export class UnauthorizedError extends Error {
  constructor(message = 'Unauthorized') {
    super(message)
  }
}

export class DatabaseError extends Error {
  constructor(message = 'Database error') {
    super(message)
  }
}

export class OpenAIError extends Error {
  constructor(message = 'OpenAI error') {
    super(message)
  }
}
