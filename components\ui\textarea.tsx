import * as React from 'react'

import { cn } from '@/lib/utils'

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      // Auto-resize the textarea based on content
      e.target.style.height = 'auto'
      e.target.style.height = `${e.target.scrollHeight}px`
    }

    return (
      <textarea
        className={cn(
          'flex w-full rounded-md border border-slate-200 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-slate-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-800 dark:bg-slate-950 dark:ring-offset-slate-950 dark:placeholder:text-slate-400 dark:focus-visible:ring-slate-300',
          className
        )}
        ref={ref}
        rows={1}
        onInput={handleInput}
        style={{ resize: 'none', overflow: 'hidden' }} // Ensure no manual resizing and auto-growing
        {...props}
      />
    )
  }
)
Textarea.displayName = 'Textarea'

export { Textarea }
