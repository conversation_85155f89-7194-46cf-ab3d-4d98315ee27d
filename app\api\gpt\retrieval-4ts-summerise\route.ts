import { NextRequest, NextResponse } from 'next/server'
import { StreamingTextResponse } from 'ai'
import { ChatOpenAI } from '@langchain/openai'
import { PromptTemplate } from '@langchain/core/prompts'
import { LL<PERSON>hain } from 'langchain/chains'
import { formatVercelMessages } from '@/lib/retriever-utils'
import { GPTModel, ResearchStoreContent } from '@/types'
import { getCurrentUserResponse } from '@/lib/session'
import { db } from '@/lib/db'

// export const runtime = 'edge'
export const dynamic = 'force-dynamic'
export const maxDuration = 120

const GPT_MODEL = GPTModel.GPT4oMini

const ANSWER_TEMPLATE = `You are now operating under the persona of "SmartCounsel," a highly specialized legal research bot. Your responses must adhere strictly to a legal context and must be delivered professionally and informatively.


You are tasked to summarise the following case:
<context>
  {context}
</context>

Given the following conversation, use the discussion for additional context. Skip messages that are completely unrelated to the follow up question.
<chat_history>
  {chat_history}
</chat_history>`

export async function POST(req: NextRequest) {
  try {
    const url = req.nextUrl
    const queryParams = url.searchParams
    let namespace: any = queryParams.get('ns') ?? 'default'
    const body = await req.json()
    const user = await getCurrentUserResponse()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const messages = body.messages ?? []
    const previousMessages = messages.slice(0, -1)
    const userQuestion = messages[messages.length - 1].content
    const filter: Pick<ResearchStoreContent, 'court' | 'year' | 'sources'> =
      body.metadata
    const chatHistory = formatVercelMessages(previousMessages)

    const model = new ChatOpenAI({
      modelName: GPT_MODEL,
      temperature: 0.2,
      streaming: true
    })

    const caseData = await db.documentRecords.findFirst({
      where: {
        id: {
          in: filter.sources.map((source) => parseInt(source))
        }
      }
    })

    const prompt = PromptTemplate.fromTemplate(ANSWER_TEMPLATE)

    const chain = new LLMChain({ llm: model, prompt })
    const textEncoder = new TextEncoder()

    const context = `${caseData?.meta}\n\n>>>>>>>>>\n\n${caseData?.content}`

    const stream = new ReadableStream({
      async start(controller) {
        controller.enqueue(textEncoder.encode(''))

        await chain.call(
          {
            context: context,
            chat_history: chatHistory,
            question: userQuestion
          },
          [
            {
              handleLLMNewToken(token: string) {
                controller.enqueue(textEncoder.encode(token))
              }
            }
          ]
        )
        controller.close()
      }
    })

    return new StreamingTextResponse(stream, {
      headers: {
        'x-message-index': (previousMessages.length + 1).toString(),
        'x-namespace': namespace
      }
    })
  } catch (e: any) {
    console.log(e)
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}
