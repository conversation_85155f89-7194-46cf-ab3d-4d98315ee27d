import { redirect } from 'next/navigation'
import { authOptions } from '@/lib/auth'
import { getCurrentUser } from '@/lib/session'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { LegalQuery } from '@/components/elements/chat/legal-query'
import { features } from '@/config/dashboard'
import { db } from '@/lib/db'
import { RecentResearchList } from '@/components/elements/research/recent-research-list'
import { ResearchInfoCard } from '@/components/elements/custom-components/feature-info-card'
import type { ResearchStoreContent } from '@/types'
import { ResearchType } from '@prisma/client'

export const metadata = features['research']

export default async function ResearchStartPage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect(authOptions?.pages?.signIn || '/login')
  }

  const researchHistory = await db.researchStore.findMany({
    select: {
      id: true,
      createdAt: true,
      question: true
    },
    where: {
      userId: user.id,
      region: user.region,
      type: ResearchType.law
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 10
  })

  const researchProps: ResearchStoreContent = {
    model: 'brainstem',
    sources: [],
    court: ['federal_constitution'],
    year: [],
    sourcesForMessages: {}
  }

  return (
    <DashboardShell>
      <DashboardHeader heading={metadata.title} text={metadata.description} />
      <div className="grid gap-10">
        <LegalQuery
          researchProps={researchProps}
          user={user}
          researchType={ResearchType.law}
          showFilters={true}
          emptyStateComponent={<ResearchInfoCard />}
        />

        {researchHistory.length > 0 && (
          <RecentResearchList
            researchHistory={researchHistory}
            path={'/dashboard/research'}
          />
        )}
      </div>
    </DashboardShell>
  )
}
