import { FormEvent, useCallback, MutableRefObject } from 'react'
import { toast } from '@/components/ui/use-toast'

interface UseChatHandlersProps {
  messageContainerRef: MutableRefObject<HTMLDivElement | null>
  messages: any[]
  chatEndpointIsLoading: boolean
  handleSubmit: (e: FormEvent<HTMLFormElement>) => void
}

/**
 * Provides the `sendMessage` function that coordinates
 * form submission and handles some side effects (e.g. scrolling).
 */
export function useChatHandlers({
  messageContainerRef,
  messages,
  chatEndpointIsLoading,
  handleSubmit
}: UseChatHandlersProps) {
  const sendMessage = useCallback(
    async (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault()

      // Let the chat container expand (if needed)
      if (messageContainerRef.current) {
        messageContainerRef.current.classList.add('grow')
      }

      // Slight delay if this is the first message
      if (!messages.length) {
        await new Promise((resolve) => setTimeout(resolve, 300))
      }

      if (chatEndpointIsLoading) {
        return toast({
          title: 'Please wait',
          description: 'We are still processing your previous message.'
        })
      }

      handleSubmit(e)
    },
    [messageContainerRef, messages, chatEndpointIsLoading, handleSubmit]
  )

  return { sendMessage }
}
