export const generalEmailHTMLtemplate = ({
  subject,
  receiverName,
  title,
  para1,
  para2,
  cta,
  postScript
}: {
  subject: string
  receiverName: string
  title: string
  para1: string
  para2: string
  cta: {
    url: string
    text: string
  }
  postScript: {
    title: string
    text: string
  }
}) => `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
  xmlns:v="urn:schemas-microsoft-com:vml"
  lang="en"
>
  <head>
    <title>${subject}</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style type="text/css">
      /*** BMEMBF Start ***/
      /* CMS V4 Editor Test */
      [name='bmeMainBody'] {
        min-height: 1000px;
      }
      @media only screen and (max-width: 480px) {
        table.blk,
        table.tblText,
        .bmeHolder,
        .bmeHolder1,
        table.bmeMainColumn {
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeImageCard table.bmeCaptionTable td.tblCell {
          padding: 0px 20px 20px 20px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeImageCard table.bmeCaptionTable.bmeCaptionTableMobileTop td.tblCell {
          padding: 20px 20px 0 20px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        table.bmeCaptionTable td.tblCell {
          padding: 10px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        table.tblGtr {
          padding-bottom: 20px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        td.blk_container,
        .blk_parent,
        .bmeLeftColumn,
        .bmeRightColumn,
        .bmeColumn1,
        .bmeColumn2,
        .bmeColumn3,
        .bmeBody {
          display: table !important;
          max-width: 600px !important;
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        table.container-table,
        .bmeheadertext,
        .container-table {
          width: 95% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .mobile-footer,
        .mobile-footer a {
          font-size: 13px !important;
          line-height: 18px !important;
        }
        .mobile-footer {
          text-align: center !important;
        }
        table.share-tbl {
          padding-bottom: 15px;
          width: 100% !important;
        }
        table.share-tbl td {
          display: block !important;
          text-align: center !important;
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        td.bmeShareTD,
        td.bmeSocialTD {
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        td.tdBoxedTextBorder {
          width: auto !important;
        }
      }
      @media only screen and (max-width: 480px) {
        th.tdBoxedTextBorder {
          width: auto !important;
        }
      }

      @media only screen and (max-width: 480px) {
        table.blk,
        table[name='tblText'],
        .bmeHolder,
        .bmeHolder1,
        table[name='bmeMainColumn'] {
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeImageCard table.bmeCaptionTable td[name='tblCell'] {
          padding: 0px 20px 20px 20px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeImageCard table.bmeCaptionTable.bmeCaptionTableMobileTop td[name='tblCell'] {
          padding: 20px 20px 0 20px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        table.bmeCaptionTable td[name='tblCell'] {
          padding: 10px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        table[name='tblGtr'] {
          padding-bottom: 20px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        td.blk_container,
        .blk_parent,
        [name='bmeLeftColumn'],
        [name='bmeRightColumn'],
        [name='bmeColumn1'],
        [name='bmeColumn2'],
        [name='bmeColumn3'],
        [name='bmeBody'] {
          display: table !important;
          max-width: 600px !important;
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        table[class='container-table'],
        .bmeheadertext,
        .container-table {
          width: 95% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .mobile-footer,
        .mobile-footer a {
          font-size: 13px !important;
          line-height: 18px !important;
        }
        .mobile-footer {
          text-align: center !important;
        }
        table[class='share-tbl'] {
          padding-bottom: 15px;
          width: 100% !important;
        }
        table[class='share-tbl'] td {
          display: block !important;
          text-align: center !important;
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        td[name='bmeShareTD'],
        td[name='bmeSocialTD'] {
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        td[name='tdBoxedTextBorder'] {
          width: auto !important;
        }
      }
      @media only screen and (max-width: 480px) {
        th[name='tdBoxedTextBorder'] {
          width: auto !important;
        }
      }

      @media only screen and (max-width: 480px) {
        .bmeImageCard table.bmeImageTable {
          height: auto !important;
          width: 100% !important;
          padding: 20px !important;
          clear: both;
          float: left !important;
          border-collapse: separate;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblInline table.bmeImageTable {
          height: auto !important;
          width: 100% !important;
          padding: 10px !important;
          clear: both;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblInline table.bmeCaptionTable {
          width: 100% !important;
          clear: both;
        }
      }
      @media only screen and (max-width: 480px) {
        table.bmeImageTable {
          height: auto !important;
          width: 100% !important;
          padding: 10px !important;
          clear: both;
        }
      }
      @media only screen and (max-width: 480px) {
        table.bmeCaptionTable {
          width: 100% !important;
          clear: both;
        }
      }
      @media only screen and (max-width: 480px) {
        table.bmeImageContainer {
          width: 100% !important;
          clear: both;
          float: left !important;
        }
      }
      @media only screen and (max-width: 480px) {
        table.bmeImageTable td {
          padding: 0px !important;
          height: auto;
        }
      }
      @media only screen and (max-width: 480px) {
        img.mobile-img-large {
          width: 100% !important;
          height: auto !important;
        }
      }
      @media only screen and (max-width: 480px) {
        img.bmeRSSImage {
          max-width: 320px;
          height: auto !important;
        }
      }
      @media only screen and (min-width: 640px) {
        img.bmeRSSImage {
          max-width: 600px !important;
          height: auto !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .trMargin img {
          height: 10px;
        }
      }
      @media only screen and (max-width: 480px) {
        div.bmefooter,
        div.bmeheader {
          display: block !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .tdPart {
          width: 100% !important;
          clear: both;
          float: left !important;
        }
      }
      @media only screen and (max-width: 480px) {
        table.blk_parent1,
        table.tblPart {
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .tblLine {
          min-width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblCenter img {
          margin: 0 auto;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblCenter,
        .bmeMblCenter div,
        .bmeMblCenter span {
          text-align: center !important;
          text-align: -webkit-center !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeNoBr br,
        .bmeImageGutterRow,
        .bmeMblStackCenter .bmeShareItem .tdMblHide {
          display: none !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblInline table.bmeImageTable,
        .bmeMblInline table.bmeCaptionTable,
        .bmeMblInline {
          clear: none !important;
          width: 50% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblInlineHide,
        .bmeShareItem .trMargin {
          display: none !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblInline table.bmeImageTable img,
        .bmeMblShareCenter.tblContainer.mblSocialContain,
        .bmeMblFollowCenter.tblContainer.mblSocialContain {
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblStack > .bmeShareItem {
          width: 100% !important;
          clear: both !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeShareItem {
          padding-top: 10px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .tdPart.bmeMblStackCenter,
        .bmeMblStackCenter .bmeFollowItemIcon {
          padding: 0px !important;
          text-align: center !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblStackCenter > .bmeShareItem {
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        td.bmeMblCenter {
          border: 0 none transparent !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeLinkTable.tdPart td {
          padding-left: 0px !important;
          padding-right: 0px !important;
          border: 0px none transparent !important;
          padding-bottom: 15px !important;
          height: auto !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .tdMblHide {
          width: 10px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeShareItemBtn {
          display: table !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblStack td {
          text-align: left !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblStack .bmeFollowItem {
          clear: both !important;
          padding-top: 10px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblStackCenter .bmeFollowItemText {
          padding-left: 5px !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .bmeMblStackCenter .bmeFollowItem {
          clear: both !important;
          align-self: center;
          float: none !important;
          padding-top: 10px;
          margin: 0 auto;
        }
      }
      @media only screen and (max-width: 480px) {
        .tdPart > table {
          width: 100% !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .tdPart > table.bmeLinkContainer {
          width: auto !important;
        }
      }
      @media only screen and (max-width: 480px) {
        .tdPart.mblStackCenter > table.bmeLinkContainer {
          width: 100% !important;
        }
      }
      .blk_parent:first-child,
      .blk_parent {
        float: left;
      }
      .blk_parent:last-child {
        float: right;
      }
      /*** BMEMBF END ***/
    </style>
    <!--[if gte mso 9]>
      <xml>
        <o:OfficeDocumentSettings>
          <o:AllowPNG />
          <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
      </xml>
    <![endif]-->
  </head>
  <body
    topmargin="0"
    leftmargin="0"
    style="height: 100% !important; margin: 0; padding: 0; width: 100% !important; min-width: 100%"
  >
    <img
      src="https://clt1676964.benchurl.com/c/o?e=17EEAE0&c=1996A4&t=1&email=NyIeD9VsXLU%3D&relid="
      alt=""
      border="0"
      style="display: none"
      height="1"
      width="1"
    />

    <table
      width="100%"
      cellspacing="0"
      cellpadding="0"
      border="0"
      name="bmeMainBody"
      bgcolor="#ebebeb"
      style="background-color: rgb(235, 235, 235)"
    >
      <tbody>
        <tr>
          <td width="100%" valign="top" align="center">
            <table cellspacing="0" cellpadding="0" border="0" name="bmeMainColumnParentTable">
              <tbody>
                <tr>
                  <td
                    name="bmeMainColumnParent"
                    style="border: 0px none transparent; border-radius: 0px; border-collapse: separate"
                  >
                    <table
                      name="bmeMainColumn"
                      class="bmeHolder bmeMainColumn"
                      style="
                        max-width: 600px;
                        overflow: visible;
                        border-radius: 0px;
                        border-collapse: separate;
                        border-spacing: 0px;
                      "
                      cellspacing="0"
                      cellpadding="0"
                      border="0"
                      align="center"
                    >
                      <tbody>
                        <tr id="section_1" class="flexible-section" data-columns="1" data-section-type="bmePreHeader">
                          <td
                            width="100%"
                            class="blk_container bmeHolder"
                            name="bmePreHeader"
                            valign="top"
                            align="center"
                            style="color: rgb(102, 102, 102); border: 0px none transparent"
                            bgcolor=""
                          ></td>
                        </tr>
                        <tr>
                          <td
                            width="100%"
                            class="bmeHolder"
                            valign="top"
                            align="center"
                            name="bmeMainContentParent"
                            style="
                              border: 1px solid rgb(224, 224, 224);
                              border-radius: 5px;
                              border-collapse: separate;
                              border-spacing: 0px;
                              overflow: hidden;
                            "
                          >
                            <table
                              name="bmeMainContent"
                              style="
                                border-radius: 0px;
                                border-collapse: separate;
                                border-spacing: 0px;
                                border: 0px none transparent;
                              "
                              width="100%"
                              cellspacing="0"
                              cellpadding="0"
                              border="0"
                              align="center"
                            >
                              <tbody>
                                <tr id="section_2" class="flexible-section" data-columns="1">
                                  <td
                                    width="100%"
                                    class="blk_container bmeHolder"
                                    name="bmeHeader"
                                    valign="top"
                                    align="center"
                                    style="
                                      color: rgb(56, 56, 56);
                                      border-top-color: rgba(0, 0, 0, 0);
                                      border-left-color: rgba(0, 0, 0, 0);
                                      border-right-color: rgba(0, 0, 0, 0);
                                      border-bottom-color: rgb(235, 235, 235);
                                      border-bottom-style: solid;
                                      border-bottom-width: 1px;
                                      background-color: rgb(245, 245, 245);
                                    "
                                    bgcolor="#f5f5f5"
                                  >
                                    <div id="dv_1" class="blk_wrapper">
                                      <table
                                        width="600"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                        class="blk"
                                        name="blk_divider"
                                        style=""
                                      >
                                        <tbody>
                                          <tr>
                                            <td class="tblCellMain" style="padding: 0px">
                                              <table
                                                class="tblLine"
                                                cellspacing="0"
                                                cellpadding="0"
                                                border="0"
                                                width="100%"
                                                style="
                                                  border-top-width: 10px;
                                                  border-top-color: #bf9a6f;
                                                  border-top-style: solid;
                                                  min-width: 1px;
                                                "
                                              >
                                                <tbody>
                                                  <tr>
                                                    <td><span></span></td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                    <div id="dv_9" class="blk_wrapper">
                                      <table
                                        width="600"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                        class="blk"
                                        name="blk_boxtext"
                                      >
                                        <tbody>
                                          <tr>
                                            <td
                                              align="center"
                                              name="bmeBoxContainer"
                                              style="
                                                padding-left: 0px;
                                                padding-right: 0px;
                                                padding-top: 20px;
                                                padding-bottom: 20px;
                                              "
                                            >
                                              <table
                                                cellspacing="0"
                                                cellpadding="0"
                                                width="100%"
                                                name="tblText"
                                                border="0"
                                                class="tblText"
                                              >
                                                <tbody>
                                                  <tr>
                                                    <td
                                                      valign="top"
                                                      align="left"
                                                      style="
                                                        padding: 20px;
                                                        font-family: Arial, Helvetica, sans-serif;
                                                        font-weight: normal;
                                                        font-size: 14px;
                                                        color: rgb(56, 56, 56);
                                                        background-color: rgba(0, 0, 0, 0);
                                                        border-collapse: collapse;
                                                        word-break: break-word;
                                                      "
                                                      name="tblCell"
                                                      class="tblCell"
                                                    >
                                                      <div style="line-height: 170%; text-align: center">
                                                        <span
                                                          style="
                                                            font-size: 24px;
                                                            font-family: 'Arial Black', 'Arial Bold', sans-serif;
                                                            color: #434e3f;
                                                            line-height: 170%;
                                                          "
                                                          ><strong>
                                                            ${title}
                                                          </strong></span
                                                        >
                                                      </div>
                                                    </td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                  </td>
                                </tr>
                                <tr id="section_3" class="flexible-section" data-columns="1">
                                  <td
                                    width="100%"
                                    class="blk_container bmeHolder bmeBody"
                                    name="bmeBody"
                                    valign="top"
                                    align="center"
                                    style="
                                      color: rgb(56, 56, 56);
                                      border: 0px none transparent;
                                      background-color: rgb(255, 255, 255);
                                    "
                                    bgcolor="#ffffff"
                                  >
                                    <div id="dv_4" class="blk_wrapper">
                                      <table
                                        width="600"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                        class="blk"
                                        name="blk_boxtext"
                                      >
                                        <tbody>
                                          <tr>
                                            <td
                                              align="center"
                                              name="bmeBoxContainer"
                                              style="
                                                padding-left: 20px;
                                                padding-right: 20px;
                                                padding-top: 20px;
                                                padding-bottom: 20px;
                                              "
                                            >
                                              <table
                                                cellspacing="0"
                                                cellpadding="0"
                                                width="100%"
                                                name="tblText"
                                                border="0"
                                                class="tblText"
                                              >
                                                <tbody>
                                                  <tr>
                                                    <td
                                                      valign="top"
                                                      align="left"
                                                      style="
                                                        padding: 20px;
                                                        font-family: Arial, Helvetica, sans-serif;
                                                        font-weight: normal;
                                                        font-size: 14px;
                                                        color: rgb(56, 56, 56);
                                                        background-color: rgba(0, 0, 0, 0);
                                                        border-collapse: collapse;
                                                        word-break: break-word;
                                                      "
                                                      name="tblCell"
                                                      class="tblCell"
                                                    >
                                                      <div style="line-height: 170%; text-align: left">
                                                        <span
                                                          style="
                                                            font-size: 16px;
                                                            font-family: Helvetica, Arial, sans-serif;
                                                            color: black;
                                                            line-height: 170%;
                                                          "
                                                          ><strong>Hi ${receiverName},</strong></span
                                                        >
                                                        <br />
                                                        <br /><span
                                                          style="
                                                            font-size: 14px;
                                                            font-family: Helvetica, Arial, sans-serif;
                                                            color: black;
                                                            line-height: 170%;
                                                          "
                                                          >
                                                            ${para1}
                                                          </span
                                                        >
                                                      </div>
                                                    </td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                    <div id="dv_3" class="blk_wrapper">
                                      <table
                                        width="600"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                        class="blk"
                                        name="blk_button"
                                        style=""
                                      >
                                        <tbody>
                                          <tr>
                                            <td width="20"></td>
                                            <td align="center">
                                              <table
                                                class="tblContainer"
                                                cellspacing="0"
                                                cellpadding="0"
                                                border="0"
                                                width="100%"
                                              >
                                                <tbody>
                                                  <tr>
                                                    <td height="10"></td>
                                                  </tr>
                                                  <tr>
                                                    <td align="left">
                                                      <table
                                                        cellspacing="0"
                                                        cellpadding="0"
                                                        border="0"
                                                        class="bmeButton"
                                                        width="100%"
                                                        style="border-collapse: separate"
                                                      >
                                                        <tbody>
                                                          <tr>
                                                            <td
                                                              style="
                                                                border-radius: 0px;
                                                                border: 0px none transparent;
                                                                text-align: center;
                                                              "
                                                              class="bmeButtonText"
                                                            >
                                                              <a
                                                                href="${cta.url}"
                                                                style="
                                                                  color: #ffffff;
                                                                  text-decoration: none;
                                                                  cursor: pointer;
                                                                "
                                                                target="_blank"
                                                              >
                                                                <span
                                                                  style="
                                                                    font-family: Helvetica, Arial, sans-serif;
                                                                    font-size: 14px;
                                                                    color: rgb(255, 255, 255);
                                                                    padding: 20px 40px;
                                                                    font-weight: bold;
                                                                    background-color: #bf9a6f;
                                                                    word-break: break-word;
                                                                  "
                                                                  >
                                                                    ${cta.text}
                                                                  </span
                                                                >
                                                              </a>
                                                            </td>
                                                          </tr>
                                                        </tbody>
                                                      </table>
                                                    </td>
                                                  </tr>
                                                  <tr>
                                                    <td height="10"></td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                            </td>
                                            <td width="20"></td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                    <div id="dv_4" class="blk_wrapper">
                                      <table
                                        width="600"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                        class="blk"
                                        name="blk_boxtext"
                                      >
                                        <tbody>
                                          <tr>
                                            <td
                                              align="center"
                                              name="bmeBoxContainer"
                                              style="
                                                padding-left: 20px;
                                                padding-right: 20px;
                                                padding-top: 20px;
                                                padding-bottom: 20px;
                                              "
                                            >
                                              <table
                                                cellspacing="0"
                                                cellpadding="0"
                                                width="100%"
                                                name="tblText"
                                                border="0"
                                                class="tblText"
                                              >
                                                <tbody>
                                                  <tr>
                                                    <td
                                                      valign="top"
                                                      align="left"
                                                      style="
                                                        padding: 20px;
                                                        font-family: Arial, Helvetica, sans-serif;
                                                        font-weight: normal;
                                                        font-size: 14px;
                                                        color: rgb(56, 56, 56);
                                                        background-color: rgba(0, 0, 0, 0);
                                                        border-collapse: collapse;
                                                        word-break: break-word;
                                                      "
                                                      name="tblCell"
                                                      class="tblCell"
                                                    >
                                                      <div style="line-height: 170%; text-align: left">
                                                        <span
                                                          style="
                                                            font-size: 14px;
                                                            font-family: Helvetica, Arial, sans-serif;
                                                            color: black;
                                                            line-height: 170%;
                                                          "
                                                          >
                                                            ${para2}
                                                          <br /><br />
                                                          Thank you for using Smart Counsel AI.
                                                          <br /><br />
                                                          Best regards,
                                                          <br />
                                                          Smart Counsel AI Team</span
                                                        >
                                                      </div>
                                                    </td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                    <div id="dv_7" class="blk_wrapper">
                                      <table
                                        width="600"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                        class="blk"
                                        name="blk_boxtext"
                                      >
                                        <tbody>
                                          <tr>
                                            <td
                                              align="center"
                                              name="bmeBoxContainer"
                                              style="
                                                padding-left: 20px;
                                                padding-right: 20px;
                                                padding-top: 20px;
                                                padding-bottom: 20px;
                                              "
                                            >
                                              <table
                                                cellspacing="0"
                                                cellpadding="0"
                                                width="100%"
                                                name="tblText"
                                                border="0"
                                                class="tblText"
                                              >
                                                <tbody>
                                                  <tr>
                                                    <td
                                                      valign="top"
                                                      align="left"
                                                      style="
                                                        padding: 20px;
                                                        font-family: Arial, Helvetica, sans-serif;
                                                        font-weight: normal;
                                                        font-size: 14px;
                                                        color: rgb(56, 56, 56);
                                                        border-width: 1px;
                                                        border-style: solid;
                                                        border-color: rgb(224, 224, 224);
                                                        background-color: rgb(245, 245, 245);
                                                        border-collapse: collapse;
                                                        word-break: break-word;
                                                      "
                                                      name="tblCell"
                                                      class="tblCell"
                                                    >
                                                      <div style="line-height: 200%">
                                                        <span
                                                          style="
                                                            font-family: Helvetica, Arial, sans-serif;
                                                            font-size: 16px;
                                                            color: black;
                                                            line-height: 170%;
                                                          "
                                                          ><strong
                                                            >
                                                            ${postScript.title}
                                                            </strong
                                                          ></span
                                                        >
                                                        <br />
                                                        <br /><span
                                                          style="
                                                            font-size: 14px;
                                                            font-family: Helvetica, Arial, sans-serif;
                                                            color: black;
                                                            line-height: 170%;
                                                          "
                                                        >
                                                            ${postScript.text}
                                                        </span
                                                        >
                                                      </div>
                                                    </td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                    <div id="dv_6" class="blk_wrapper">
                                      <table
                                        width="600"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                        class="blk"
                                        name="blk_divider"
                                        style=""
                                      >
                                        <tbody>
                                          <tr>
                                            <td class="tblCellMain" style="padding: 30px 0px 20px">
                                              <table
                                                class="tblLine"
                                                cellspacing="0"
                                                cellpadding="0"
                                                border="0"
                                                width="100%"
                                                style="
                                                  border-top-width: 1px;
                                                  border-top-color: rgb(245, 245, 245);
                                                  border-top-style: solid;
                                                  min-width: 1px;
                                                "
                                              >
                                                <tbody>
                                                  <tr>
                                                    <td><span></span></td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                  </td>
                                </tr>
                                <tr id="section_4" class="flexible-section" data-columns="1">
                                  <td
                                    width="100%"
                                    class="blk_container bmeHolder"
                                    name="bmePreFooter"
                                    valign="top"
                                    align="center"
                                    style="
                                      color: rgb(56, 56, 56);
                                      border: 0px none transparent;
                                      background-color: rgb(255, 255, 255);
                                    "
                                    bgcolor="#ffffff"
                                  >
                                    <div id="dv_8" class="blk_wrapper">
                                      <table
                                        width="600"
                                        cellspacing="0"
                                        cellpadding="0"
                                        border="0"
                                        class="blk"
                                        name="blk_social_follow"
                                        style=""
                                      >
                                        <tbody>
                                          <tr>
                                            <td
                                              class="tblCellMain"
                                              align="center"
                                              style="
                                                padding-top: 20px;
                                                padding-bottom: 20px;
                                                padding-left: 20px;
                                                padding-right: 20px;
                                              "
                                            >
                                              <table
                                                class="tblContainer mblSocialContain"
                                                cellspacing="0"
                                                cellpadding="0"
                                                border="0"
                                                align="center"
                                                style="text-align: center"
                                              >
                                                <tbody>
                                                  <tr>
                                                    <td class="tdItemContainer">
                                                      <table
                                                        cellspacing="0"
                                                        cellpadding="0"
                                                        border="0"
                                                        class="mblSocialContain"
                                                        style="table-layout: auto"
                                                      >
                                                        <tbody>
                                                          <tr>
                                                            <td valign="top" name="bmeSocialTD" class="bmeSocialTD">
                                                              <!--[if gte mso 6]></td><td align="left" valign="top"><![endif]-->
                                                              <table
                                                                cellspacing="0"
                                                                cellpadding="0"
                                                                border="0"
                                                                class="bmeFollowItem"
                                                                type="facebook"
                                                                style="float: left; display: block"
                                                                align="left"
                                                              >
                                                                <tbody>
                                                                  <tr>
                                                                    <td
                                                                      align="left"
                                                                      class="bmeFollowItemIcon"
                                                                      gutter="20"
                                                                      width="24"
                                                                      style="padding-right: 20px; height: 20px"
                                                                    >
                                                                      <a
                                                                        href="https://clt1676964.benchurl.com/c/l?u=10FB81FD&e=17EEAE0&c=1996A4&t=1&seq=1"
                                                                        target="_blank"
                                                                        style="
                                                                          display: inline-block;
                                                                          background-color: rgb(53, 91, 161);
                                                                          border-radius: 6px;
                                                                          border-style: none;
                                                                          border-width: 0px;
                                                                          border-color: rgb(0, 0, 238);
                                                                        "
                                                                        target="_blank"
                                                                        ><img
                                                                          src="https://ui.benchmarkemail.com/images/editor/socialicons/fb_btn.png"
                                                                          alt="Facebook"
                                                                          style="display: block; max-width: 114px"
                                                                          border="0"
                                                                          width="24"
                                                                          height="24"
                                                                      /></a>
                                                                    </td>
                                                                  </tr>
                                                                </tbody>
                                                              </table>
                                                              <!--[if gte mso 6]></td><td align="left" valign="top"><![endif]-->
                                                              <table
                                                                cellspacing="0"
                                                                cellpadding="0"
                                                                border="0"
                                                                class="bmeFollowItem"
                                                                type="instagram"
                                                                style="float: left; display: block"
                                                                align="left"
                                                              >
                                                                <tbody>
                                                                  <tr>
                                                                    <td
                                                                      align="left"
                                                                      class="bmeFollowItemIcon"
                                                                      gutter="20"
                                                                      width="24"
                                                                      style="padding-right: 20px; height: 20px"
                                                                    >
                                                                      <a
                                                                        href="https://clt1676964.benchurl.com/c/l?u=10FB81FE&e=17EEAE0&c=1996A4&t=1&seq=1"
                                                                        target="_blank"
                                                                        style="
                                                                          display: inline-block;
                                                                          background-color: rgb(214, 73, 46);
                                                                          border-radius: 6px;
                                                                          border-style: none;
                                                                          border-width: 0px;
                                                                          border-color: rgb(0, 0, 238);
                                                                        "
                                                                        target="_blank"
                                                                        ><img
                                                                          src="https://ui.benchmarkemail.com/images/editor/socialicons/in_btn.png"
                                                                          alt="Instagram"
                                                                          style="display: block; max-width: 114px"
                                                                          border="0"
                                                                          width="24"
                                                                          height="24"
                                                                      /></a>
                                                                    </td>
                                                                  </tr>
                                                                </tbody>
                                                              </table>
                                                              <!--[if gte mso 6]></td><td align="left" valign="top"><![endif]-->
                                                              <table
                                                                cellspacing="0"
                                                                cellpadding="0"
                                                                border="0"
                                                                class="bmeFollowItem"
                                                                type="twitter"
                                                                style="float: left; display: block"
                                                                align="left"
                                                              >
                                                                <tbody>
                                                                  <tr>
                                                                    <td
                                                                      align="left"
                                                                      class="bmeFollowItemIcon"
                                                                      gutter="20"
                                                                      width="24"
                                                                      style="height: 20px"
                                                                    >
                                                                      <a
                                                                        href="https://clt1676964.benchurl.com/c/l?u=10FB81FF&e=17EEAE0&c=1996A4&t=1&seq=1"
                                                                        target="_blank"
                                                                        style="
                                                                          display: inline-block;
                                                                          background-color: rgb(50, 203, 255);
                                                                          border-radius: 6px;
                                                                          border-style: none;
                                                                          border-width: 0px;
                                                                          border-color: rgb(0, 0, 238);
                                                                        "
                                                                        target="_blank"
                                                                        ><img
                                                                          src="https://ui.benchmarkemail.com/images/editor/socialicons/tw_btn.png"
                                                                          alt="Twitter"
                                                                          style="display: block; max-width: 114px"
                                                                          border="0"
                                                                          width="24"
                                                                          height="24"
                                                                      /></a>
                                                                    </td>
                                                                  </tr>
                                                                </tbody>
                                                              </table>
                                                            </td>
                                                          </tr>
                                                        </tbody>
                                                      </table>
                                                    </td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
    <!-- /Test Path -->
  </body>
</html>
`
