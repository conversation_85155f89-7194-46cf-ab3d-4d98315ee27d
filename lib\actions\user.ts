'use server'

import { env } from '@/env.mjs'
import { db } from '../db'
import { sendEmailViaPostmark } from '../services/postmark-service'
import { generalEmailHTMLtemplate } from '../templates/email'
import bcrypt from 'bcryptjs'
import { Region } from '@prisma/client'

export async function validateEmailAndSendPasswordResetEmail({
  email
}: {
  email: string
}) {
  try {
    const user = await db.user.findFirst({
      where: {
        email
      }
    })

    if (!user) {
      console.log('User not found')
      throw new Error('User not found')
    }

    const createToken = await db.resetPasswordToken.create({
      data: {
        userId: user.id,
        expires: new Date(Date.now() + 1000 * 60 * 60 * 24)
      }
    })

    const template = generalEmailHTMLtemplate({
      subject: 'Reset your password for Smart Counsel AI',
      receiverName: user.name || ' there',
      title: 'Reset your password',
      para1:
        'We received a request to reset the password for your Smart Counsel AI account associated with this email address. If you did not make this request, please ignore this email. Otherwise, you can reset your password by clicking on the link below:',
      para2:
        'This password reset link will expire in 24 hours. If you need further assistance, please contact our support <NAME_EMAIL>.',
      cta: {
        text: 'Reset Password',
        url: `${env.NEXT_PUBLIC_APP_URL}/forgot/${createToken.token}`
      },
      postScript: {
        title: 'If you did not request a password reset:',
        text: `It's possible that someone else has entered your email by mistake or might be attempting to access your account. We recommend taking the following actions:
          <br>- Do not click on any links or provide any personal information.
          <br>- Change your account password if you haven't done so recently.
          <br>- Review your account details and update your security settings.
          <br>- Contact our support team immediately for further assistance and to ensure your account remains secure.
          <br>Your security is our top priority.`
      }
    })

    const postmarkResponse = await sendEmailViaPostmark({
      to: email,
      subject: 'Reset your password for Smart Counsel AI',
      htmlBody: template
    })

    console.log('postmarkResponse:', postmarkResponse)

    return postmarkResponse
  } catch (error) {
    console.error('Error in validateEmailAndSendPasswordResetEmail:', error)
    return null
  }
}

export async function updatePassword({
  userId,
  password
}: {
  userId: string
  password: string
}) {
  try {
    const salt = bcrypt.genSaltSync(10)
    const passwordHash = bcrypt.hashSync(password, salt)
    await db.user.update({
      where: {
        id: userId
      },
      data: {
        passwordHash
      }
    })

    const user = await db.user.findUnique({
      where: {
        id: userId
      }
    })

    return user?.email
  } catch (error) {
    console.error('Error in updatePassword:', error)
    return null
  }
}

export async function switchRegion({
  userId,
  region
}: {
  userId: string
  region: Region
}) {
  try {
    await db.user.update({
      where: {
        id: userId
      },
      data: {
        region
      }
    })

    return region
  } catch (error) {
    console.error('Error in switchRegion:', error)
    return null
  }
}

export async function updateUserInformation({
  userId,
  teamId,
  name,
  teamName,
  region
}: {
  userId: string
  teamId: string
  name: string
  teamName: string
  region: Region
}) {
  try {
    await db.user.update({
      where: {
        id: userId
      },
      data: {
        name,
        region
      }
    })

    await db.team.update({
      where: {
        id: teamId
      },
      data: {
        name: teamName
      }
    })

    return true
  } catch (error) {
    console.error('Error in updateUserInformation:', error)
    return null
  }
}
