import type { NextApiRequest, NextApiResponse } from 'next'
import axios from 'axios'
import * as cheerio from 'cheerio'
import { db } from '@/lib/db'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const set = [2]
    for (const i of set) {
      const url = `https://labourlawreporter.net/judgements/${i}.htm`
      // const url = `https://labourlawreporter.net/opencontent.asp?id=${i}`
      console.log('Fetching data for: ', url)

      try {
        const { data } = await axios.get(url)
        const $ = cheerio.load(data)

        // remove p with class noprint
        $('p.noprint').remove()

        // remove div with id background
        $('div#background').remove()

        let headings: any
        const firstEightChildren = $('body').children(':lt(8)')
        const blockquote = firstEightChildren.find('blockquote')
        if (blockquote.children().length > 0) {
          headings = blockquote.eq(0).find('p[align="center"]')
          if (headings.length === 0) {
            headings = blockquote.eq(0).find('p[align="justify"]')
          }
        } else {
          const mainParagraphs = $('body > p:lt(5)')
          headings = mainParagraphs.filter('.linkoverCopy')
          if (headings.length === 0) {
            headings = mainParagraphs.filter('[align="center"]')
            if (headings.length === 0) {
              headings = mainParagraphs.filter('[align="justify"]')
            }
          }
        }

        const headingA = headings.eq(0).text().split('\n')

        const headingB = headings.eq(1).text().split('\n')

        let title = headingA[0].replace(/[^ -~\n\r]|[\uFFFD]/g, '-').trim()

        if (title.length > 100) {
          title = title
            // limit to 100 charecters
            .slice(0, 100)
            // split at last space
            .split(' ')
            // remove last item
            .slice(0, -1)
            // join back with space
            .join(' ')
        }

        const meta = parseCaseData(headingA, headingB)

        // select remaining html body and convert to string with utf-8 encoding
        const htmlbody = $.html()

        if (data) {
          const validate = await db.documentRecords.findFirst({
            where: {
              source: 'labourlawreporter',
              ref: i.toString()
            }
          })

          if (!validate) {
            const store = await db.documentRecords.create({
              data: {
                source: 'labourlawreporter',
                ref: i.toString(),
                title: title,
                meta: JSON.stringify(meta),
                date: meta.date,
                url: url,
                html: htmlbody.replace(/[^ -~\n\r]|[\uFFFD]/g, '-'),
                content: htmlbody.replace(/[^ -~\n\r]|[\uFFFD]/g, '-')
              }
            })
            console.log('Data stored for: ', store.id, store.title)
          } else {
            const update = await db.documentRecords.update({
              where: {
                id: validate.id
              },
              data: {
                title: title,
                meta: JSON.stringify(meta),
                date: meta.date,
                content: htmlbody.replace(/[^ -~\n\r]|[\uFFFD]/g, '-'),
                html: htmlbody.replace(/[^ -~\n\r]|[\uFFFD]/g, '-')
              }
            })
            console.log('Data updated for: ', update.id, update.title)
          }
        }
      } catch (error) {
        // console.error(`Failed to fetch or store data for ${i}:`, error)
      }
    }

    res.status(200).json({ message: 'Data saved successfully!' })
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch and parse the website.' })
  }
}

function parseCaseData(dataSet1: string[], dataSet2?: string[]): any {
  let result: any = {
    case: '',
    court: '',
    judges: [],
    parties: [],
    citation: '',
    date: undefined,
    headnotes: ''
  }

  for (let item of dataSet1) {
    const cleanItem = item.replace(/[^ -~\n\r]|[\uFFFD]/g, '-').trim()
    if (cleanItem.includes('LLR')) {
      result.case = cleanItem
    } else if (cleanItem.includes(' COURT')) {
      result.court = cleanItem
    } else if (cleanItem.includes("Hon'ble")) {
      result.judges.push(cleanItem)
    } else if (cleanItem.includes('Dt/')) {
      result.citation = cleanItem
      let dateMatch = cleanItem.match(/\d+-\d+-\d+/)
      if (dateMatch && dateMatch.length > 0) {
        const [day, month, year] = dateMatch[0].trim().split('-').map(Number)
        result.date = new Date(year, month - 1, day)
      }
    } else if (
      !cleanItem.includes('v.') &&
      !cleanItem.includes('vs.') &&
      cleanItem !== '' &&
      !result.court
    ) {
      result.headnotes = cleanItem
    }
  }

  if (dataSet2) {
    const vIndex = dataSet2.findIndex(
      (item) => item.includes('v.') || item.includes('vs.')
    )
    if (vIndex !== -1) {
      result.parties = dataSet2
        .slice(0, vIndex)
        .map((item) => item.replace(/[^ -~\n\r]|[\uFFFD]/g, '-').trim())
    }
  }

  return result
}
