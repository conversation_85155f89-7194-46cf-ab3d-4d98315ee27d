import { env } from '@/env.mjs'

export async function sendEmailViaPostmark({
  from = '<EMAIL>',
  to,
  subject,
  htmlBody,
  cc,
  bcc,
  tag,
  textBody = '',
  replyTo = '<EMAIL>',
  trackOpens = true,
  trackLinks = 'HtmlOnly',
  messageStream = 'outbound'
}: {
  from?: string
  to: string
  subject: string
  htmlBody: string
  cc?: string
  bcc?: string
  tag?: string
  textBody?: string
  replyTo?: string
  trackOpens?: boolean
  trackLinks?: 'HtmlOnly' | 'TextOnly' | 'None'
  messageStream?: string
}): Promise<
  | {
      To: string
      SubmittedAt: string
      MessageID: string
      ErrorCode: number
      Message: string
    }
  | undefined
> {
  try {
    const url = 'https://api.postmarkapp.com/email'
    const headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'X-Postmark-Server-Token': env.POSTMARK_API_TOKEN
    }
    const body = {
      From: from,
      To: to,
      Cc: cc,
      Bcc: bcc,
      Subject: subject,
      HtmlBody: htmlBody,
      TextBody: textBody,
      ReplyTo: replyTo,
      TrackOpens: trackOpens,
      TrackLinks: trackLinks,
      MessageStream: messageStream
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(body)
    })
    const responseData = await response.json()

    return responseData
  } catch (error) {
    console.log(error)
  }
}
