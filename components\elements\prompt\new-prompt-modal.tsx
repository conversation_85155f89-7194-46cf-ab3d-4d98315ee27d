'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>lose,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { Button, buttonVariants } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'
import { useRouter } from 'next/navigation'
import { createPrompt } from '@/lib/actions/prompt'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'

export const NewPromptModal = () => {
  const [role, setRole] = useState('')
  const [source, setSource] = useState('')
  const [prompt, setPrompt] = useState('')
  const [expectedOutput, setExpectedOutput] = useState('')
  const [variables, setVariables] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleCreatePrompt = async () => {
    if (!source || !prompt) {
      toast({
        title: 'Prompt and Source are required',
        description: 'Please fill out all the necessary fields',
        variant: 'destructive'
      })
      return
    }

    setIsLoading(true)
    try {
      await createPrompt({
        role: role,
        source: source,
        prompt: prompt,
        expectedOutput: expectedOutput,
        variables: variables.split(',').map((v) => v.trim())
      })
      toast({
        title: 'Prompt created successfully',
        description: `The prompt has been created`
      })
      setRole('')
      setSource('')
      setPrompt('')
      setExpectedOutput('')
      setVariables('')
      router.refresh()
    } catch (error) {
      console.error('Error creating prompt:', error)
      toast({
        title: 'Failed to create prompt',
        description: 'An error occurred while creating the prompt',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog>
      <DialogTrigger
        className={buttonVariants({
          variant: 'default',
          size: 'sm'
        })}
      >
        New Prompt
      </DialogTrigger>
      <DialogContent className="max-h-[80vh] max-w-[90%] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Prompt</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-3">
          <Input
            type="text"
            placeholder="Role"
            value={role}
            onChange={(e) => setRole(e.target.value)}
            className="p-2"
          />
          <Input
            type="text"
            placeholder="Source"
            value={source}
            onChange={(e) => setSource(e.target.value)}
            className="p-2"
          />
          <Textarea
            placeholder="Prompt"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            className="p-2 min-h-[100px]"
          />
          <Textarea
            placeholder="Expected Output"
            value={expectedOutput}
            onChange={(e) => setExpectedOutput(e.target.value)}
            className="p-2 min-h-[100px]"
          />
          <Input
            type="text"
            placeholder="Variables (comma-separated)"
            value={variables}
            onChange={(e) => setVariables(e.target.value)}
            className="p-2"
          />
        </div>
        <DialogFooter className="sm:justify-start">
          <DialogClose asChild>
            <Button onClick={handleCreatePrompt} disabled={isLoading} size="sm">
              {isLoading ? 'Creating...' : 'Create'}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
