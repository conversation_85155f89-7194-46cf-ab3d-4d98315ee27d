'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useStoreResearch } from '@/lib/hooks/chat-window/use-research-store'
import { DocumentRecords, ResearchType } from '@prisma/client'
import { Session } from 'next-auth'

export function ChatCreateButton({
  buttonText,
  user,
  researchType,
  binderId,
  documents
}: {
  buttonText: string
  user: Session['user']
  researchType: ResearchType
  binderId: string
  documents?: Pick<DocumentRecords, 'id' | 'title'>[]
}) {
  const baseCourt = {
    [ResearchType.law]: ['federal_constitution'],
    [ResearchType.case]: ['federalcourts_ussupremecourt'],
    [ResearchType.private]: []
  }
  const { storeResearch } = useStoreResearch({
    user,
    court: baseCourt[researchType],
    year: [],
    model: 'brainstem',
    sources: documents ? documents.map((doc) => doc.id.toString()) : [],
    sourceLabels: documents?.map((doc) => ({
      id: doc.id.toString(),
      title: doc.title
    })),
    sourcesForMessages: {},
    researchType,
    binderId
  })
  return (
    <Button onClick={() => storeResearch([])} className="rounded-full">
      {buttonText}
    </Button>
  )
}
