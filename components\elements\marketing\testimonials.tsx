import * as React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Star } from 'lucide-react'

const testimonials = [
  {
    title: 'Mastering Medical Records & Handwritten Documents',
    content:
      'Deciphering complex medical records and handwritten notes used to be a nightmare—critical details were often buried in jargon, abbreviations, or illegible handwriting. SmartCounsel.AI extracts every key fact with precision, ensuring we never miss a diagnosis, causation link, or liability factor. It is like having a medical expert and legal analyst working together in real time.\nWith SmartCounsel.AI, no detail is overlooked, and no defense tactic goes unchallenged.',
    rating: 5
  },
  {
    title: 'Winning Tough Cases',
    content:
      'We had a complex injury case where causation was tough to prove. SmartCounsel.AI helped us connect the dots, cross-reference medical records, and find the exact precedent we needed to turn the case around.\nSmartCounsel.AI helped us secure justice for our client.',
    rating: 5
  },
  {
    title: 'Crushing the Defense',
    content:
      'Defense attorneys thrive on delays and document dumps. SmartCounsel.AI cuts through the noise, finds the smoking gun, and arms us with the evidence we need to dismantle their arguments—fast.\nSmartCounsel.AI gives us the firepower to take on any defense team.',
    rating: 5
  },
  {
    title: 'Mass Tort Case Management',
    content:
      'Managing thousands of claims in mass tort litigation used to be overwhelming. SmartCounsel.AI organizes evidence, identifies trends across cases, and ensures no detail is overlooked. It is like having a full-time investigator at our fingertips.\nSmartCounsel.AI makes mass tort litigation manageable and profitable.',
    rating: 5
  },
  {
    title: 'Maximizing Case Value',
    content:
      'SmartCounsel.AI helps us build stronger cases, faster. From analyzing medical records to pinpointing key liability evidence, it streamlines our workflow so we can maximize settlements and verdicts for our clients.\nWith SmartCounsel.AI, we fight smarter and win bigger.',
    rating: 5
  },
  {
    title: 'Faster Case Preparation',
    content:
      'Time is everything in personal injury cases. SmartCounsel.AI extracts critical facts from accident reports, depositions, and medical files in seconds—work that used to take days. Now, we move faster than the insurance companies.\nSmartCounsel.AI accelerates our case prep and keeps us ahead.',
    rating: 5
  }
]

export function Testimonials() {
  return (
    <>
      <div className="mx-auto max-w-[58rem] flex flex-col items-center text-center space-y-4">
        <h2 className="font-heading text-3xl leading-[1.2] sm:text-4xl md:text-5xl">
          What Our Clients Say
        </h2>
        <p className="max-w-[55ch] text-muted-foreground sm:text-lg">
          Discover how SmartCounsel.AI is transforming the way law firms handle
          personal injury and mass tort cases.
        </p>
      </div>
      <div className="mx-auto grid w-full max-w-[80rem] grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {testimonials.map((testimonial, i) => (
          <Card key={i} className="h-full">
            <CardHeader>
              <CardTitle className="text-lg font-semibold sm:text-xl">
                {testimonial.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col justify-between space-y-4">
              <p className="text-sm leading-6 sm:text-base">
                {testimonial.content}
              </p>
              <div className="flex space-x-1">
                {Array.from({ length: testimonial.rating }).map((_, index) => (
                  <Star
                    key={index}
                    className="h-5 w-5 fill-current text-yellow-500"
                    // Tip: remove "fill-current" if you prefer outline-only stars
                    // and handle coloring via Tailwind classes or style
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </>
  )
}
