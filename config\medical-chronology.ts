export const medical_chronology_prompts: {
  [key: string]: {
    prompt: string
    expected_output: string
  }
} = {
  case_information_metadata: {
    prompt:
      'Generate a **Medical Chronology** section in Markdown format using the following structured data:\n\n{{context}}\n\nThe section should include:\n- **Plaintiff Name**: [Full Name]\n- **Date of Birth**: [DOB]\n- **Incident Date & Description**: [Date | Brief Summary of Accident]\n- **Pre-Existing Conditions**: [Any Prior Medical Conditions]\n\n**Formatting Guidelines:**\n- Use **bold** headers and list formatting.\n- Ensure clarity and readability.\n- Leave space between paragraphs for readability.\n- Do not include any sections beyond those explicitly listed.\n- Do **not** include **Event Documentation** or **Case Gaps**.\n- **Do not** include any additional sections such as **Primary Injuries** or **Treatment History** or **Key Medical Events**.\n- Do not include any unnecessary text or placeholders. All citations must be provided as [doc-id:page] at the line, example: [123:2].',
    expected_output:
      '# Medical Chronology\n\n**Plaintiff Name:** <PERSON>  \n**Date of Birth:** 05/14/1985  \n**Incident Date & Description:** 10/18/2022 | Rear-end collision at intersection, sustained multiple injuries  \n**Pre-Existing Conditions:**  \n- History of Low Back Pain  '
  },
  diagnostic_highlights: {
    prompt:
      'Generate a **Diagnostic Highlights** section in Markdown format using the following structured data:\n\n{{context}}\n\nThe section should present a **table format** with:\n- **ICD/CPT Codes**\n- **Diagnosis**\n- **First Diagnosed Date**\n- **Reference Document**\n\n**Formatting Guidelines:**\n- Use a Markdown table for structured data.\n- Ensure each diagnosis is clearly documented. All citations must be provided as [doc-id:page] at the line, example: [123:2].\n\n**Hyperlinking Instructions:**\n- All referenced documents should be hyperlinked within the downloadable PDF version.\n- Clicking a hyperlink in the PDF should open or navigate to the corresponding source document at **https://smartcounsel.ai/document/document-id** (example: [6324862](https://smartcounsel.ai/document/6324862)).\n- Use the format [Reference Document](https://smartcounsel.ai/document/<document-id>) to hyperlink within the Markdown.\n- Ensure the hyperlinks are functional when the document is exported to PDF.',
    expected_output:
      '# Diagnostic Highlights (ICD, CPT, etc.)\n\n| ICD/CPT Codes | Diagnosis                         | First Diagnosed | Reference Document       |\n|--------------|----------------------------------|----------------|--------------------------|\n| M24.811      | Joint Derangement (Right Shoulder) | 10/18/2022     | [Anduin Medical](https://smartcounsel.ai/document/6324862) |\n| S43.101A     | Dislocation of Right AC Joint     | 10/20/2022     | [Happy Health](https://smartcounsel.ai/document/6324863)   |\n| M54.50       | Low Back Pain                     | 10/20/2022     | [Happy Health](https://smartcounsel.ai/document/6324864)   |\n| M75.51       | Bursitis of Right Shoulder        | 01/03/2023     | [Rock Surgery Center](https://smartcounsel.ai/document/6324865) |'
  },
  treatment_calendar: {
    prompt:
      'Generate a **Treatment Calendar** in Markdown format using the following structured data:\n\n{{context (excluding entries where **Department** is **Law Enforcement**)}}\n\nThe section should summarize the treatment timeline in a **table format** with the following columns:\n- **Medical Provider**\n- **Department**\n- **Treatment Period**\n- **Number of Visits**\n- **Reference Document**\n\n**Formatting Guidelines:**\n- Use a Markdown table with proper column alignment.\n- Color-code departments using appropriate emojis:\n  - 🔵 **Law Enforcement**\n  - 🟢 **EMS**\n  - 🟩 **Emergency Room**\n  - 🟦 **Imaging**\n  - 🟨 **Medical Treatment**\n  - 🟪 **Rehabilitation**\n- Ensure clarity and readability. All citations must be provided as [doc-id:page] at the line, example: [123:2].\n\n**Hyperlinking Instructions:**\n- All referenced exhibits should be hyperlinked within the downloadable PDF version of the chronology.\n- Clicking a hyperlink in the PDF should open or navigate to the corresponding source document at **https://smartcounsel.ai/document/document-id** (example: [6324862](https://smartcounsel.ai/document/6324862)).\n- Use the format [Reference Document](https://smartcounsel.ai/document/<document-id>) to hyperlink within the Markdown.\n- Ensure the hyperlinks are functional when the document is exported to PDF.',
    expected_output:
      '# Treatment Calendar (Summarized View)\n\n| Medical Provider          | Department             | Treatment Period | Number of Visits | Reference Document          |\n|---------------------------|------------------------|------------------|------------------|-----------------------------|\n| Canyon Lake Fire EMS      | 🟢 EMS                 | 10/18/2022       | 1                | [EMS Report](https://smartcounsel.ai/document/6324862)  |\n| Anduin Medical            | 🟩 Emergency Room      | 10/18/2022       | 1                | [Anduin Medical Report](https://smartcounsel.ai/document/6324863) |\n| Happy Health              | 🟨 Medical Treatment   | 10/20/2022       | 1                | [Happy Health Medical Notes](https://smartcounsel.ai/document/6324864) |\n| Benefit Plus MRI          | 🟦 Imaging             | 11/22/2022       | 1                | [Benefit Plus MRI Report](https://smartcounsel.ai/document/6324865) |\n| Rock Surgery Center       | 🟨 Surgery             | 01/03/2023       | 1                | [Rock Surgery Surgery Report](https://smartcounsel.ai/document/6324866) |\n| Special Physical Therapy  | 🟪 Rehabilitation      | 02/15/2023       | 3                | [Physical Therapy Notes](https://smartcounsel.ai/document/6324867) |'
  },
  flags_and_case_gaps: {
    prompt:
      'Generate a **Flags & Case Gaps** section in Markdown format using the following structured data:\n\n{{context}}\n\nThe section should present **a table format** with:\n- **Date**\n- **Provider**\n- **Department**\n- **Flag Description (highlight missing records, gaps, prior injuries)**\n- **Reference Document**\n\n**Formatting Guidelines:**\n- Use a Markdown table with structured data.\n- Implement a color-coded flagging system to visually indicate the severity of gaps or issues:\n  - 🟥 **Red** – Critical Issue (e.g., missing medical records, conflicting information)\n  - 🟧 **Amber** – Moderate Issue (e.g., missing follow-up, incomplete data)\n  - 🟩 **Green** – No Issue\n- Ensure clear and structured documentation.\n- All citations must be provided as [doc-id:page] at the line, example: [123:2].\n\n**Hyperlinking Instructions:**\n- All referenced documents should be hyperlinked within the downloadable PDF version.\n- Clicking a hyperlink in the PDF should open or navigate to the corresponding source document at **https://smartcounsel.ai/document/document-id** (example: [6324862](https://smartcounsel.ai/document/6324862)).\n- Use the format [Reference Document](https://smartcounsel.ai/document/<document-id>) to hyperlink within the Markdown.\n- Ensure the hyperlinks are functional when the document is exported to PDF.',
    expected_output:
      "# Flags & Case Gaps\n\n| Date        | Provider             | Department           | Flag Description                                       | Reference Document                                                                 |\n|------------|----------------------|---------------------|-------------------------------------------------------|------------------------------------------------------------------------------------|\n| 10/18/2022 | Anduin Medical         | 🟩 Emergency Room    | 🟥 History of Low Back Pain (May Affect Causation)      | [Medical History](https://smartcounsel.ai/document/6324862)                         |\n| 10/20/2022 | Happy Health           | 🟨 Medical Treatment | 🟥 No Record of Orthopedic Referral Follow-Up            | [Doctor's Notes](https://smartcounsel.ai/document/6324863)                           |\n| 01/03/2023 | Maple Anesthesia        | 🟥 Missing Records   | 🟧 Only Bills Available, No Medical Documentation        | [Billing Records](https://smartcounsel.ai/document/6324864)                          |"
  },
  treatment_timeline: {
    prompt: `Generate a **Treatment Timeline** section in Markdown format using the following structured data:

{{context}}

(excluding entries where **Treatment Type** is "Police Report" or "Legal Advice")

**User Strategy Guidelines:**
Part A Guidelines: {{partAGuidelines}}
Part B Guidelines: {{partBGuidelines}}

Use these guidelines to focus the treatment timeline generation according to user preferences. Pay special attention to:
- Highlighted providers, date ranges, treatment types, and diagnoses specified in Part A guidelines
- Prospective care priorities and focus areas specified in Part B guidelines

## Part A – Chronological Treatment Timeline
The section should list **all medical and legal events in chronological order**, structured with:
- **Date**
- **Provider**
- **Department**
- **Treatment Type**
- **Key Findings & Summary**
- **Source Document**

**Formatting Guidelines:**
- Use a structured Markdown table.
- Color-code departments using appropriate emojis:
 - 🔵 **Law Enforcement**
 - 🟢 **EMS**
 - 🟩 **Emergency Room**
 - 🟨 **Medical Treatment**
 - 🟦 **Imaging**
 - 🟨 **Surgery**
 - 🟪 **Rehabilitation**
- Ensure clear readability.
- All citations must be provided as [doc-id:page] at the line, example: [123:2].

## Part B – Prospective Care & Follow-Up Recommendations
After rendering the final timeline row in Part A, insert the heading
### Prospective Care & Follow-Up Recommendations
and populate a Markdown table that lists only forward-looking recommendations that are not already represented as completed, in-progress, or scheduled events in Part A.

**Inclusion logic**

1. Scan the entire record set for forward-looking advice (future surgery, imaging, PT, medication changes, referrals, follow-ups, etc.).
2. Build a set of identifiers from Part A (date + provider + treatment type or an obviously matching description).
3. Omit any recommendation whose procedure, imaging, therapy, or follow-up is already captured in Part A as:
4. Completed (performed on or before the same date), or
5. In-progress / Scheduled (explicitly listed in Part A with a future date or "pending").
6. If no unique forward-looking items remain, output the single line _No prospective treatment or follow-up documented._ and stop.

**Table columns (in order)**
| Date | Provider & Specialty | Dept. (emoji) | Future Care Ordered | Timing / Frequency / Duration | Rationale | Source Document |

**Formatting guidelines**

- Use a well-spaced Markdown table, one row per recommendation.
- Color-code Dept. with these emojis:

- 🔵**Law Enforcement**
- 🟢**EMS**
- 🟩**Emergency Room**
- 🟨**Medical Treatment**
- 🟦**Imaging**
- 🟨**Surgery** (reuse 🟨 for surgical items)
- 🟪**Rehabilitation**

- Cite every row using [doc-id:page] in the Citation column (e.g., [123:2]).
- Keep wording concise and align all pipes (|) for readability.`,
    expected_output: `## Part A – Chronological Treatment Timeline

| Date | Provider | Dept. | Treatment Type | Key Findings & Summary | Source Document |
|------|----------|-------|----------------|------------------------|-----------------|
| 10/18/2022 | Officer Smith | 🔵 Law Enforcement | Incident Report | Rear-end collision, plaintiff conscious and responsive | [123:1] |
| 10/18/2022 | Metro EMS | 🟢 EMS | Emergency Transport | Transported to hospital, vital signs stable | [124:1] |
| 10/18/2022 | Dr. Johnson | 🟩 Emergency Room | Initial Assessment | Lower back pain, neck stiffness, no neurological deficits | [125:2] |

### Prospective Care & Follow-Up Recommendations

| Date | Provider & Specialty | Dept. | Future Care Ordered | Timing / Frequency / Duration | Rationale | Source Document |
|------|---------------------|-------|-------------------|------------------------------|-----------|-----------------|
| 10/25/2022 | Dr. Anderson, Orthopedist | 🟨 Medical Treatment | MRI Lumbar Spine | Within 2 weeks | Evaluate extent of spinal injury | [126:3] |
| 11/01/2022 | PT Clinic | 🟪 Rehabilitation | Physical Therapy | 3x/week for 8 weeks | Restore mobility and strength | [127:1] |`
  },
  case_strength_analysis: {
    prompt:
      'Generate a **Final Case Strength Analysis** section in Markdown format using the following structured data:\n\n{{context}}\n\nThe section should highlight:\n- **Strong Evidence** ✅\n- **Potential Weaknesses** ⚠\n\n**Formatting Guidelines:**\n- Use bullet points and emphasis where needed.\n- All citations must be provided as [doc-id:page] at the line, example: [123:2].\n\n**Hyperlinking Instructions:**\n- All referenced documents should be hyperlinked within the downloadable PDF version.\n- Clicking a hyperlink in the PDF should open or navigate to the corresponding source document at **https://smartcounsel.ai/document/document-id** (example: [6324862](https://smartcounsel.ai/document/6324862)).\n- Use the format [Reference Document](https://smartcounsel.ai/document/<document-id>) to hyperlink within the Markdown.\n- Ensure the hyperlinks are functional when the document is exported to PDF.',
    expected_output:
      '# Final Case Strength Analysis\n\n📌 **Causation Evidence:**\n✅ Strong medical documentation linking injuries to the accident ([EMS Report](https://smartcounsel.ai/document/6324862)).  \n✅ Consistent diagnostic evidence (X-rays, MRI, Surgery) ([Anduin Medical Report](https://smartcounsel.ai/document/6324863)).  \n\n📌 **Potential Weaknesses:**\n⚠ Missing follow-up orthopedic referral records ([Happy Health Medical Notes](https://smartcounsel.ai/document/6324864)).  \n⚠ History of low back pain could be disputed ([Benefit Plus MRI Report](https://smartcounsel.ai/document/6324865)).'
  }
} as const
