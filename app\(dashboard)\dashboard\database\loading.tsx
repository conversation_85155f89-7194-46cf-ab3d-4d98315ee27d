import { CardSkeleton } from '@/components/elements/custom-components/card-skeleton'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { features } from '@/config/dashboard'

export default function DashboardSettingsLoading() {
  const featureContext = features['searchDatabase']

  return (
    <DashboardShell>
      <DashboardHeader
        heading={featureContext.title}
        text={featureContext.description}
      />
      <div className="grid gap-10">
        <CardSkeleton />
      </div>
    </DashboardShell>
  )
}
