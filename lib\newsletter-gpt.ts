import { env } from '@/env.mjs'
import { GPTModel, NewsletterConfig, TextDavinciResponse } from '@/types'
import { OpenAIError } from './exceptions'
import { isJsonString } from './utils'

export async function generateNewsletterEJSWithOpenAI({
  newsletterConfig
}: {
  newsletterConfig: NewsletterConfig
}) {
  try {
    const editorialTone = [
      {
        value: 'formal',
        label: 'Formal: Use formal/professional language and tone.'
      },
      {
        value: 'casual',
        label: 'Casual: Use casual/playful language and tone.'
      }
    ].find((type) => type.value === newsletterConfig.editorialTone)

    const prompt = `Act as an editor for a prestigious legal publication. Create a comprehensive legal newsletter based on the following provided content:

    Newsletter Title: ${newsletterConfig.title}
    Content Theme: ${newsletterConfig.theme}
    Key Legal Updates: ${newsletterConfig.keyLegalUpdates.join(', ')}
    Target Audience: ${newsletterConfig.targetAudience || 'Legal Professionals'}
    Editorial Tone: ${editorialTone?.label}
    
    ----------------------
    Content: ${newsletterConfig.content}
    ----------------------

    Each section should be presented with clear headings, bullet points, and paragraphs. The content should be detailed, aiming for at least 2000 words. The output should be formatted in the universal EditorJS JSON output format, ensuring it's structured for easy editing and publication.
    
    Include an ending note that encourages engagement from the readers, and add the date at the end.
    
    This newsletter will be catering specifically to professionals in the legal field, ensuring they are well-informed of the latest trends and case laws.
    `

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: GPTModel.GPT4Turbo,
        messages: [{ role: 'user', content: JSON.stringify(prompt) }],
        max_tokens: 3800,
        temperature: 0.5,
        response_format: { type: 'json_object' }
      })
    })

    const completion = await response.json()
    if (
      (completion satisfies TextDavinciResponse) &&
      completion?.choices &&
      isJsonString(completion.choices[0].message.content)
    ) {
      return JSON.parse(completion.choices[0].message.content)
    } else {
      console.log(JSON.stringify(completion, null, 2))
      throw new OpenAIError()
    }
  } catch (error) {
    if (error instanceof OpenAIError) {
      console.error('OpenAIError', error)
      throw new Error('OpenAIError')
    } else {
      console.error('UnknownError', error)
      throw new Error('UnknownError')
    }
  }
}
