import Link from 'next/link'
import { TeamDocument } from '@prisma/client'

import { formatDate } from '@/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'

interface DocumentItemProps {
  document: Pick<TeamDocument, 'id' | 'title' | 'source' | 'createdAt'>
}

export function DocumentItem({ document }: DocumentItemProps) {
  return (
    <div className="flex items-center justify-between p-4">
      <div className="grid gap-1">
        <Link
          href={`/dashboard/document-review/${document.id}`}
          className="font-semibold hover:underline"
        >
          {document.title}
        </Link>
        <div>
          <p className="text-sm text-muted-foreground">
            {formatDate(document.createdAt?.toDateString())}
          </p>
        </div>
      </div>
    </div>
  )
}

DocumentItem.Skeleton = function DocumentItemSkeleton() {
  return (
    <div className="p-4">
      <div className="space-y-3">
        <Skeleton className="h-5 w-2/5" />
        <Skeleton className="h-4 w-4/5" />
      </div>
    </div>
  )
}
