import { Type } from '@google/genai'

export const extractEventsSchema = {
  responseMimeType: 'application/json',
  responseSchema: {
    type: Type.OBJECT,
    properties: {
      events: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            event: {
              type: Type.STRING,
              description: 'Brief description of what happened'
            },
            eventType: {
              type: Type.STRING,
              enum: [
                'medical_treatment',
                'emergency_visit',
                'diagnostic_test',
                'injury_report',
                'police_report',
                'medication',
                'surgery',
                'therapy',
                'expert_opinion',
                'legal_consultation',
                'disability_assessment',
                'work_restriction',
                'pain_documentation'
              ]
            },
            eventDescription: {
              type: Type.STRING,
              description: 'Detailed description with legal relevance'
            },
            timestamp: {
              type: Type.STRING,
              description: 'ISO format date YYYY-MM-DD HH:MM'
            },
            estimatedTime: {
              type: Type.BOOLEAN
            },
            confidence: {
              type: Type.NUMBER,
              description: 'Confidence score between 0.1 and 1.0'
            },
            pageReference: {
              type: Type.STRING,
              nullable: true
            }
          },
          required: [
            'event',
            'eventType',
            'eventDescription',
            'timestamp',
            'estimatedTime',
            'confidence'
          ],
          propertyOrdering: [
            'event',
            'eventType',
            'eventDescription',
            'timestamp',
            'estimatedTime',
            'confidence',
            'pageReference'
          ]
        }
      }
    },
    required: ['events']
  }
}

export const deduplicateEventsSchema = {
  responseMimeType: 'application/json',
  responseSchema: {
    type: Type.OBJECT,
    properties: {
      duplicateGroups: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            keepEventId: {
              type: Type.STRING,
              description: 'ID of event to keep'
            },
            mergeEventIds: {
              type: Type.ARRAY,
              items: {
                type: Type.STRING
              },
              description: 'IDs of duplicate events to merge'
            },
            mergedEvent: {
              type: Type.STRING,
              description: 'Combined description emphasizing legal significance'
            },
            mergedDescription: {
              type: Type.STRING,
              description: 'Comprehensive description highlighting damages'
            }
          },
          required: [
            'keepEventId',
            'mergeEventIds',
            'mergedEvent',
            'mergedDescription'
          ],
          propertyOrdering: [
            'keepEventId',
            'mergeEventIds',
            'mergedEvent',
            'mergedDescription'
          ]
        }
      },
      uniqueEventIds: {
        type: Type.ARRAY,
        items: {
          type: Type.STRING
        },
        description: 'IDs of events that are unique'
      }
    },
    required: ['duplicateGroups', 'uniqueEventIds']
  }
}

export const plaintiffInfoSchema = {
  responseMimeType: 'application/json',
  responseSchema: {
    type: Type.OBJECT,
    properties: {
      fullName: {
        type: Type.STRING,
        description: 'Patient full name'
      },
      dateOfBirth: {
        type: Type.STRING,
        description: "YYYY-MM-DD format or 'Unknown'"
      },
      age: {
        type: Type.NUMBER,
        nullable: true
      },
      gender: {
        type: Type.STRING,
        enum: ['M', 'F', 'Unknown']
      },
      occupation: {
        type: Type.STRING,
        description: 'Specific occupation and work status'
      },
      preExistingConditions: {
        type: Type.ARRAY,
        items: {
          type: Type.STRING
        },
        description: 'Conditions with severity assessment'
      },
      primaryInjuries: {
        type: Type.ARRAY,
        items: {
          type: Type.STRING
        },
        description: 'Injuries with legal significance and permanency'
      },
      incidentDate: {
        type: Type.STRING,
        description: 'YYYY-MM-DD of main incident'
      },
      incidentDescription: {
        type: Type.STRING,
        description: 'Detailed incident description for liability'
      }
    },
    required: [
      'fullName',
      'dateOfBirth',
      'preExistingConditions',
      'primaryInjuries',
      'incidentDate',
      'incidentDescription'
    ],
    propertyOrdering: [
      'fullName',
      'dateOfBirth',
      'age',
      'gender',
      'occupation',
      'preExistingConditions',
      'primaryInjuries',
      'incidentDate',
      'incidentDescription'
    ]
  }
}
