{"name": "lex<PERSON>n", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3011", "redev": "rm -rf .next && next dev -p 3011", "pm2": "rm -rf .next && next dev", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "prettier --config \".prettierrc\"  \"./**/*.tsx\"  \"./**/*.ts\" --write", "update-db": "npx prisma db push && npx prisma generate", "migrate-pg": "npx prisma migrate dev --schema=prisma/schema.postgres.prisma", "generate-pg": "npx prisma generate --schema=prisma/schema.postgres.prisma"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@aws-sdk/client-s3": "^3.635.0", "@aws-sdk/s3-request-presigner": "^3.635.0", "@azure/openai": "^2.0.0", "@dqbd/tiktoken": "^1.0.18", "@editorjs/code": "^2.8.0", "@editorjs/editorjs": "^2.27.2", "@editorjs/embed": "^2.5.3", "@editorjs/header": "^2.7.0", "@editorjs/inline-code": "^1.4.0", "@editorjs/link": "^2.5.0", "@editorjs/list": "^1.8.0", "@editorjs/table": "^2.2.2", "@google/genai": "^0.13.0", "@hookform/resolvers": "^3.9.0", "@langchain/pinecone": "^0.0.8", "@next-auth/prisma-adapter": "^1.0.7", "@pinecone-database/pinecone": "^3.0.0", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.0.4", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.38.0", "@t3-oss/env-nextjs": "^0.6.0", "@tanstack/react-table": "^8.20.5", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-horizontal-rule": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-paragraph": "^2.11.5", "@tiptap/extension-subscript": "^2.11.5", "@tiptap/extension-superscript": "^2.11.5", "@tiptap/extension-table": "^2.11.5", "@tiptap/extension-table-cell": "^2.11.5", "@tiptap/extension-table-header": "^2.11.5", "@tiptap/extension-table-row": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-typography": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/node": "20.4.5", "@types/react": "18.2.17", "@types/react-dom": "18.2.7", "ai": "^2.2.14", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0-rc.12", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.0.0", "eslint-config-next": "13.4.12", "googleapis": "^144.0.0", "html-docx-js": "^0.3.1", "jotai": "^2.9.3", "json5": "^2.2.3", "katex": "^0.16.11", "langchain": "^0.2.16", "llamaindex": "^0.10.6", "lucide-react": "^0.507.0", "mammoth": "^1.8.0", "marked": "^12.0.2", "next": "^14.2.29", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "nodemailer": "^6.9.4", "openai": "^4.67.1", "pdf-parse": "^1.1.1", "postcss": "8.4.27", "postmark": "^3.0.19", "react": "18.2.0", "react-calendly": "^4.3.1", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-tag-input": "^6.10.3", "react-textarea-autosize": "^8.5.2", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.6", "typescript": "5.1.6", "xlsx": "^0.18.5", "zod": "^3.21.4"}, "devDependencies": {"@prisma/nextjs-monorepo-workaround-plugin": "^5.21.1", "@tailwindcss/typography": "^0.5.10", "@types/bcryptjs": "^2.4.6", "@types/editorjs__header": "^2.6.0", "@types/html-docx-js": "^0.3.4", "@types/katex": "^0.16.7", "@types/pdf-parse": "^1.1.4", "autoprefixer": "10.4.14", "encoding": "^0.1.13", "eslint": "^8.46.0", "eslint-plugin-spellcheck": "^0.0.20", "prettier": "^3.0.0", "prisma": "^6.5.0", "react-hook-form": "^7.45.2", "tailwindcss": "3.3.3"}}