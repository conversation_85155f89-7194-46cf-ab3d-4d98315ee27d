import { db } from '@/lib/db'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { LinkWithInformationMini } from '@/components/elements/card-infotag'

export const metadata = {
  title: 'Document Source View Page'
}

export default async function DocumentViewPagePage() {
  const documentCountRaw = await db.documentRecords.groupBy({
    by: ['source'],
    _count: {
      _all: true
    },
    orderBy: {
      _count: {
        source: 'desc'
      }
    }
  })

  // remove documentCountRaw source starts with cl or cm
  const documentCount = documentCountRaw.filter(
    (record) =>
      !record.source.startsWith('cl') && !record.source.startsWith('cm')
  )

  return (
    <DashboardShell>
      <DashboardHeader
        heading="SmartCounsel Reference Documents"
        text="View and select all the document sources in the system"
      ></DashboardHeader>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {documentCount.map((record, i) => (
          <LinkWithInformationMini
            key={i}
            title={record.source}
            mainValue={record._count._all.toLocaleString()}
            path={`/admin/documents/${record.source}`}
          />
        ))}
      </div>
    </DashboardShell>
  )
}
