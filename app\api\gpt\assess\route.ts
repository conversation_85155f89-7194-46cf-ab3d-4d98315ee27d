import { NextRequest, NextResponse } from 'next/server'
import { GPTModel, ResearchStoreContent, type VercelChatMessage } from '@/types'
import { getCurrentUserResponse } from '@/lib/session'
import { refineQuestion } from '@/lib/cerebrum/gpt-assisted-processes'
import { developer } from '@/lib/utils'
import { ResearchType } from '@prisma/client'

export const dynamic = 'force-dynamic'
export const maxDuration = 120

const GPT_MODEL = GPTModel.GPT4o

interface ChatRequestBody {
  researchType: ResearchType
  namespace?: string
  summarise?: boolean
  metadata: Pick<ResearchStoreContent, 'court' | 'year' | 'sources'>
  messages?: VercelChatMessage[]
  question: string
}

export async function POST(req: NextRequest) {
  try {
    // Validate if user is logged in
    const user = await getCurrentUserResponse()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      namespace = undefined,
      summarise = false,
      metadata: { sources = [], court = [], year = [] },
      messages = [],
      researchType,
      question: userQuestion
    } = (await req.json()) as ChatRequestBody

    const previousMessages = messages.slice(0, -1)

    const isPrivateResearch = user.teamId === namespace

    try {
      developer.log(['metadata', { sources, court, year }])
      developer.log(['userQuestion', userQuestion])

      const assessment = await refineQuestion({
        messages: previousMessages,
        question: userQuestion,
        researchType,
        region: user.region,
        namespace: namespace,
        model: GPT_MODEL,
        dummy: summarise,
        promptType: isPrivateResearch ? 'general' : 'regional'
      })

      return NextResponse.json(assessment)
    } catch (error: any) {
      developer.error(error)
      const userQuestion = messages[messages.length - 1].content
      return NextResponse.json({
        relevance: 'no',
        independent: 'no',
        specific_case: 'no',
        rewritten_question_set: [
          {
            question: userQuestion,
            intent: 'general'
          }
        ]
      })
    }
  } catch (e: any) {
    console.log(e)
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}
