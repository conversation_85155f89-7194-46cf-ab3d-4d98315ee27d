import { env } from '@/env.mjs'
import { Prisma, PrismaClient } from '@prisma/client'

const globalForPrisma = global as unknown as { prisma: PrismaClient }

export const db =
  globalForPrisma.prisma ||
  new PrismaClient({
    errorFormat: 'pretty',
    log: [
      {
        emit: 'event',
        level: 'query'
      },
      {
        emit: 'stdout',
        level: 'error'
      },
      {
        emit: 'stdout',
        level: 'info'
      },
      {
        emit: 'stdout',
        level: 'warn'
      }
    ]
  })

// prisma.$on('query', (e) => {
//   console.info('Query: ' + e.query)
//   console.info('Params: ' + e.params)
//   console.info('Duration: ' + e.duration + 'ms')
// })

if (env.NODE_ENV !== 'production') globalForPrisma.prisma = db

export function handlePrismaError(error: unknown) {
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    console.error({
      type: 'PrismaClientKnownRequestError',
      code: error.code,
      meta: error.meta,
      message: error.message.trim()
    })
  } else if (error instanceof Prisma.PrismaClientUnknownRequestError) {
    console.error({
      type: 'PrismaClientUnknownRequestError',
      message: error.message.trim()
    })
  } else if (error instanceof Prisma.PrismaClientRustPanicError) {
    console.error({
      type: 'PrismaClientRustPanicError',
      message: error.message.trim()
    })
  } else {
    console.error(error)
  }
}
