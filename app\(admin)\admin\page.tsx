import { db } from '@/lib/db'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { CardWithInformation } from '@/components/elements/card-infotag'

export const metadata = {
  title: 'Supers Dashboard'
}

export default async function DashboardPage() {
  const teams = await db.team.count()
  const documents = await db.teamDocument.count()

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Usage Records"
        text="Track SmartCounsel usage stats"
      ></DashboardHeader>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <CardWithInformation title="Teams" mainValue={teams.toString()} />
        <CardWithInformation
          title="Documents"
          mainValue={documents.toString()}
        />
      </div>
    </DashboardShell>
  )
}
