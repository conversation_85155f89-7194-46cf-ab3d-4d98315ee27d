import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import {
  MultiSelector,
  MultiSelectorContent,
  MultiSelectorInput,
  MultiSelectorItem,
  MultiSelectorList,
  MultiSelectorTrigger
} from '@/components/elements/research/multi-select'
import { IndianLawSource, StoredNamespace, USLawSource } from '@/types'
import { Region } from '@prisma/client'

const constants = {
  year: {
    placeholder: (type: string) => `Select ${type} year if required`,
    label: (type: string) => `${type} Year`,
    options: (start: number, end: number) =>
      Array.from({ length: end - start }, (_, i) => end - i).map(String)
  }
}

export function SelectYearFilter({
  type,
  startYear,
  endYear,
  value,
  setValue
}: {
  type?: string
  startYear: number
  endYear: number
  value: string | undefined
  setValue: (value: string) => void
}) {
  const { placeholder, label, options } = constants['year']

  return (
    <Select
      value={value}
      defaultValue={value}
      onValueChange={(value) => setValue(value)}
    >
      <SelectTrigger>
        {type && <span>{type === 'start' ? 'from' : 'to'}:</span>}
        <SelectValue placeholder={placeholder(type || '')} />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel className="capitalize">{label(type || '')}</SelectLabel>
          {options(startYear, endYear).map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

export function SelectNamespaceFilter({
  value,
  setValue,
  region
}: {
  value: string | undefined
  setValue: (value: StoredNamespace[Region]) => void
  region: Region
}) {
  return (
    <Select onValueChange={setValue} defaultValue={value}>
      <SelectTrigger>
        <SelectValue placeholder="Select Case Type" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Database</SelectLabel>
          {region === Region.IN ? (
            <>
              <SelectItem value={IndianLawSource.LabourLaw}>
                Labour Law
              </SelectItem>
              <SelectItem value={IndianLawSource.IndiaGST}>GST</SelectItem>
            </>
          ) : (
            <>
              <SelectItem value={USLawSource.FederalSupremeCourt}>
                Federal Supreme Court
              </SelectItem>
            </>
          )}
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

export function ModelSelector({
  model,
  setModel
}: {
  model: string | undefined
  setModel: (model: string) => void
}) {
  return (
    <Select onValueChange={setModel} defaultValue={model}>
      <SelectTrigger>
        <SelectValue placeholder="Select intelligence level" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectLabel>Intelligence Level</SelectLabel>
          <SelectItem value="brainstem-gemini">Gemini</SelectItem>
          <SelectItem value="brainstem-o1">Ultra</SelectItem>
          <SelectItem value="brainstem">Basic</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

export function SelectDocumentsFilter({
  options,
  values,
  setValues,
  maxVisible
}: {
  options: {
    value: string
    label: string
  }[]
  values: string[]
  setValues: (values: string[]) => void
  maxVisible: number
}) {
  return (
    <MultiSelector values={values} onValuesChange={setValues}>
      <MultiSelectorTrigger maxVisible={maxVisible} selectorProps={options}>
        <MultiSelectorInput
          className="text-sm"
          placeholder="Select documents..."
        />
      </MultiSelectorTrigger>
      <MultiSelectorContent>
        <MultiSelectorList>
          {options.map((option) => (
            <MultiSelectorItem key={option.value} value={option.value}>
              {option.label}
            </MultiSelectorItem>
          ))}
        </MultiSelectorList>
      </MultiSelectorContent>
    </MultiSelector>
  )
}
