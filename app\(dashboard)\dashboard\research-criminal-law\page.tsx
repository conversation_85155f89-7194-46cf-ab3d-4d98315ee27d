import { redirect } from 'next/navigation'
import { authOptions } from '@/lib/auth'
import { getCurrentUser } from '@/lib/session'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { LegalQuery } from '@/components/elements/chat/legal-query'
import { features } from '@/config/dashboard'
import type { ResearchStoreContent } from '@/types'
import { db } from '@/lib/db'
import { RecentResearchList } from '@/components/elements/research/recent-research-list'
import { ResearchInfoCard } from '@/components/elements/custom-components/feature-info-card'
import { ResearchType } from '@prisma/client'

// TODO: This is marked to be hidden
export const metadata = (features as any)['researchCriminalLaw']

export default async function ResearchStartPage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect(authOptions?.pages?.signIn || '/login')
  }

  const researchHistory = await db.researchStore.findMany({
    select: {
      id: true,
      createdAt: true,
      question: true
    },
    where: {
      userId: user.id
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 5
  })

  const researchProps: ResearchStoreContent = {
    model: 'retrieval-4ts-comp',
    sources: [],
    court: [],
    year: [],
    sourcesForMessages: {}
  }

  return (
    <DashboardShell>
      <DashboardHeader heading={metadata.title} text={metadata.description} />
      <div className="grid gap-10">
        <LegalQuery
          researchProps={researchProps}
          user={user}
          researchType={ResearchType.law}
          namespace="new-criminal-law<>old-criminal-law"
          showFilters={false}
          emptyStateComponent={<ResearchInfoCard />}
        />

        {researchHistory.length > 0 && (
          <RecentResearchList
            researchHistory={researchHistory}
            path={'/dashboard/research-criminal-law'}
          />
        )}
      </div>
    </DashboardShell>
  )
}
