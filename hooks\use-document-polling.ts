'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { fetchDocuments } from '@/lib/actions/case/fetch-documents'
import { DocumentTitle } from '@/types/case'

export function useDocumentPolling(
  binderId: string,
  initialDocuments: DocumentTitle[]
) {
  const [documents, setDocuments] = useState<DocumentTitle[]>(initialDocuments)
  const [isPolling, setIsPolling] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const hasUnindexedDocuments = useCallback((docs: DocumentTitle[]) => {
    return docs.some((doc) => !doc.indexed)
  }, [])

  const updateDocuments = useCallback(async () => {
    try {
      const freshDocuments = await fetchDocuments(binderId)
      setDocuments(freshDocuments)
      return freshDocuments
    } catch (error) {
      console.error('Error fetching documents:', error)
      return []
    }
  }, [binderId])

  const startPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    setIsPolling(true)
    intervalRef.current = setInterval(async () => {
      const freshDocuments = await updateDocuments()

      // Stop polling if all documents are indexed
      if (!hasUnindexedDocuments(freshDocuments)) {
        setIsPolling(false)
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
          intervalRef.current = null
        }
      }
    }, 5000) // Poll every 5 seconds
  }, [updateDocuments, hasUnindexedDocuments])

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    setIsPolling(false)
  }, [])

  // Effect to handle initial documents and detect changes
  useEffect(() => {
    setDocuments(initialDocuments)
  }, [initialDocuments])

  // Effect to start/stop polling based on document state
  useEffect(() => {
    const hasUnindexed = hasUnindexedDocuments(documents)

    if (hasUnindexed && !isPolling) {
      startPolling()
    } else if (!hasUnindexed && isPolling) {
      stopPolling()
    }
  }, [documents, hasUnindexedDocuments, isPolling, startPolling, stopPolling])

  useEffect(() => {
    return () => {
      stopPolling()
    }
  }, [stopPolling])

  // Manual refresh function for external triggers
  const refreshDocuments = useCallback(async () => {
    const freshDocuments = await updateDocuments()

    // If we found new unindexed documents and we're not already polling, start polling
    if (hasUnindexedDocuments(freshDocuments) && !isPolling) {
      startPolling()
    }

    return freshDocuments
  }, [updateDocuments, hasUnindexedDocuments, isPolling, startPolling])

  return {
    documents,
    isPolling,
    updateDocuments: refreshDocuments
  }
}
