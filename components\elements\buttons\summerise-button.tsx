export function SummariseCaseButton({
  sendMessage,
  input,
  handleInputChange
}: {
  sendMessage: (e: React.FormEvent<HTMLFormElement>) => void
  input: string
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void
}) {
  return (
    <form
      onSubmit={sendMessage}
      className="mt-2 text-xs text-center border-2 p-5 hover:bg-ring hover:dark:bg-slate-700 cursor-pointer duration-200 rounded-lg"
    >
      <input
        id="summarise-query"
        className="invisible w-0"
        value="summarise this case"
        readOnly
      />
      <button type="submit">Summarize</button>
    </form>
  )
}
