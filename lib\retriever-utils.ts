import { db } from '@/lib/db'
import { Document } from 'langchain/document'
import { cleanUpString, isJsonString } from '@/lib/utils'
import { env } from '@/env.mjs'
import { DocumentRecords, Region } from '@prisma/client'
import {
  type VercelChatMessage,
  type TextDavinciResponse,
  GPTModel,
  USLawSource,
  IndianLawSource,
  QueryAssessmentResponse,
  QueryClarityCheckResponse,
  YesNo
} from '@/types'
import { Pinecone } from '@pinecone-database/pinecone'
import { OpenAIEmbeddings } from '@langchain/openai'
import { PineconeStore } from '@langchain/pinecone'
import { CaseData } from '@/types/document'

export async function fetchFullDocuments(
  ids: number[]
): Promise<DocumentRecords[]> {
  const newDocuments = await db.documentRecords.findMany({
    where: {
      id: {
        in: ids
      }
    }
  })

  return newDocuments
}

export async function fetchDocumentMetadata(
  ids: number[]
): Promise<Pick<DocumentRecords, 'id' | 'meta'>[]> {
  const newDocuments = await db.documentRecords.findMany({
    select: {
      id: true,
      meta: true
    },
    where: {
      id: {
        in: ids
      }
    }
  })

  return newDocuments
}

export function processSerialisedSources({
  documents,
  titleSet
}: {
  documents: Document[]
  titleSet: { title: string; id: number }[]
}) {
  const groupedByDocId = documents.reduce((acc: any, doc) => {
    const docId = doc.metadata.documentRecordsId
    doc.pageContent = cleanUpString(doc.pageContent)

    if (acc[docId]) {
      acc[docId].push(doc)
    } else {
      acc[docId] = [doc]
    }
    return acc
  }, {})

  const result = Object.entries(groupedByDocId).map(
    ([docId, docs]: [docsId: string, docs: any]) => {
      return {
        docId: docId,
        title: titleSet.find((title) => title.id === parseInt(docId))?.title,
        refId: docs[0].metadata.refId,
        year: docs[0].metadata.year || 1000,
        data: docs
      }
    }
  )

  result.sort((a, b) => {
    if (a.year > b.year) {
      return -1
    } else if (a.year < b.year) {
      return 1
    } else {
      return 0
    }
  })

  const serializedSources = Buffer.from(JSON.stringify(result)).toString(
    'base64'
  )

  return serializedSources
}

export function formatVercelMessages(
  chatHistory: VercelChatMessage[],
  compress?: boolean
) {
  const formattedDialogueTurns = chatHistory.map((message) => {
    if (message.role === 'user') {
      return `Human: ${message.content}`
    } else if (message.role === 'assistant' && compress) {
      return `Assistant: ${message.content.slice(0, 100)}...`
    } else if (message.role === 'assistant') {
      return `Assistant: ${message.content}`
    } else {
      return `${message.role}: ${message.content}`
    }
  })
  return formattedDialogueTurns.join('\n')
}

export function extractHumanMessages(chatHistory: VercelChatMessage[]) {
  const humanMessages = chatHistory
    .filter((message) => message.role === 'user')
    .map((message) => message.content)
    .join('. ')
  return humanMessages
}

export async function fetchVectors(searchContent: string) {
  try {
    const pinecone = new Pinecone()
    const pineconeIndex = pinecone.Index(env.PINECONE_INDEX)

    const vectorStore = await PineconeStore.fromExistingIndex(
      new OpenAIEmbeddings(),
      { pineconeIndex, namespace: 'taxindiaonline' }
    )

    const documents = await vectorStore.similaritySearch(searchContent, 3)
    const documentCollection = await fetchFullDocuments(
      documents.map((doc) => doc.metadata.documentRecordsId)
    )

    return {
      documentSpecificContent: documents.map((doc) => doc.pageContent),
      documentCollection
    }
  } catch (error) {
    console.log(error)
    return {
      documentSpecificContent: [],
      documentCollection: []
    }
  }
}

export function generateContextArray({
  documents,
  documentCollection
}: {
  documents: Document[]
  documentCollection: DocumentRecords[]
}) {
  console.log('select documents >', documents.length)

  if (!documents.length) return null

  const groupedByDocId = documents.reduce(
    (
      acc: {
        [key: string]: string[]
      },
      doc
    ) => {
      const docId = doc.metadata.documentRecordsId as number
      doc.pageContent = doc.pageContent
        .replace(/\n/g, ' ')
        .replace(/  /g, ' ')
        .trim()
      if (acc[docId]) {
        acc[docId].push(doc.pageContent)
      } else {
        acc[docId] = [doc.pageContent]
      }

      return acc
    },
    {}
  )

  const contextArray: {
    documentRecordsId: number
    documentMetadata?: CaseData
    caseContent: string
    pageContentCollection: string[]
  }[] = []

  for (const [docId, pageContentCollection] of Object.entries(groupedByDocId)) {
    const doc = documentCollection.find((doc) => doc.id === parseInt(docId))
    if (doc) {
      contextArray.push({
        documentRecordsId: doc.id,
        documentMetadata: doc.meta ? JSON.parse(doc.meta) : undefined,
        caseContent: doc.content,
        pageContentCollection
      })
    }
  }

  const numberOfCases = contextArray.length

  const context = contextArray.reduce((acc: string, contextItem) => {
    let metadataString = `Citation ID: ${contextItem.documentRecordsId}\n`

    if (
      contextItem.documentMetadata?.title &&
      contextItem.documentMetadata.title.length > 2
    )
      metadataString += `Title: ${contextItem.documentMetadata.title}\n`
    if (
      contextItem.documentMetadata?.court &&
      contextItem.documentMetadata.court.length > 2
    )
      metadataString += `Court: ${contextItem.documentMetadata.court}\n`
    if (
      contextItem.documentMetadata?.judges &&
      contextItem.documentMetadata.judges.length > 2
    )
      metadataString += `Judges: ${contextItem.documentMetadata.judges.join(', ')}\n`
    if (
      contextItem.documentMetadata?.parties &&
      contextItem.documentMetadata.parties.length > 2
    )
      metadataString += `Parties: ${contextItem.documentMetadata.parties.join(', ')}\n`
    if (
      contextItem.documentMetadata?.citation &&
      contextItem.documentMetadata.citation.length > 2
    )
      metadataString += `Citation: ${contextItem.documentMetadata.citation}\n`
    if (contextItem.documentMetadata?.date)
      metadataString += `Date: ${contextItem.documentMetadata.date}\n`
    if (contextItem.documentMetadata?.year)
      metadataString += `Year: ${contextItem.documentMetadata.year}\n`
    if (
      contextItem.documentMetadata?.summary &&
      contextItem.documentMetadata.summary.length > 2
    )
      metadataString += `Summary: ${contextItem.documentMetadata.summary}\n`

    if (metadataString.length > 2) {
      metadataString += `
CASE INFORMATION:
=======================
${metadataString}`
    }

    if (numberOfCases > 1) {
      acc += `${metadataString}

RELEVANT POINTS FROM CASE:
=======================
${cleanUpString(contextItem.pageContentCollection.join('\n>>>>>>>\n'))}`
    } else {
      acc += `${metadataString}

CASE CONTENT:
=======================
${cleanUpString(contextItem.caseContent).slice(0, 30000)}
`
    }

    return acc
  }, '')

  return context
}

export function namespaceIdentifier(
  sector: string,
  region: Region
): IndianLawSource | USLawSource {
  let namespace = sector.toLowerCase()
  if (region === Region.IN) {
    switch (namespace) {
      case 'labour law':
        namespace = IndianLawSource.LabourLaw
        break

      case 'indian gst':
        namespace = IndianLawSource.IndiaGST
        break

      case 'indian income tax':
        namespace = IndianLawSource.IndiaIncomeTax
        break

      default:
        namespace = IndianLawSource.LabourLaw
        break
    }
  } else {
    switch (namespace) {
      case 'us-law':
        namespace = USLawSource.FederalSupremeCourt
        break

      default:
        namespace = USLawSource.FederalSupremeCourt
        break
    }
  }

  return namespace as USLawSource | IndianLawSource
}

export function cleanQueryAssessmentResponse({
  rawAssessment,
  question,
  region,
  namespace
}: {
  rawAssessment: any
  question: string
  region: Region
  namespace?: string
}): QueryAssessmentResponse {
  return {
    relevance: rawAssessment?.relevance ?? 'yes',
    sector:
      namespace ?? namespaceIdentifier(rawAssessment?.sector ?? '', region),
    independent: rawAssessment?.independent ?? 'no',
    specific_case: rawAssessment?.specific_case ?? 'no',
    rewritten_question:
      rawAssessment?.rewritten_question &&
      rawAssessment.rewritten_question.length > 10
        ? rawAssessment.rewritten_question
        : question,
    related_searches: rawAssessment?.related_searches ?? []
  }
}

export function cleanQueryClarityCheckResponse({
  rawAssessment,
  question
}: {
  rawAssessment: any
  question: string
}): QueryClarityCheckResponse {
  return {
    relevance: rawAssessment?.relevance ?? 'yes',
    independent: rawAssessment?.independent ?? 'no',
    specific_case: rawAssessment?.specific_case ?? 'no',
    rewritten_question_set: rawAssessment?.rewritten_question_set ?? [
      {
        question,
        intent: 'general'
      }
    ]
  }
}

export function dummyAssessmentResponse({
  question,
  namespace = 'dummy'
}: {
  question: string
  namespace?: string
}): QueryAssessmentResponse {
  return {
    relevance: YesNo.YES,
    sector: namespace,
    independent: YesNo.NO,
    specific_case: YesNo.NO,
    rewritten_question: question
  }
}
