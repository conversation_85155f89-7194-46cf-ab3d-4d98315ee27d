import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { NewPromptModal } from '@/components/elements/prompt/new-prompt-modal'
import { PromptManagerWithFeatures } from '@/components/elements/prompt/prompt-manager'

export const metadata = {
  title: 'Prompts',
  description: 'Manage prompts.'
}

export default async function PromptsPage() {
  return (
    <DashboardShell>
      <DashboardHeader heading="Prompts" text="Manage prompts by feature.">
        <NewPromptModal />
      </DashboardHeader>
      <PromptManagerWithFeatures />
    </DashboardShell>
  )
}
