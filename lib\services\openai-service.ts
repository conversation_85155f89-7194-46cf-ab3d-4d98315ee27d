import { env } from '@/env.mjs'
import { GPTModel } from '@/types'
import { trackTokenUsage } from './llm-token-usage'
import OpenAI from 'openai'
import { ChatCompletionMessageParam } from 'openai/resources/chat/completions.mjs'
import { LLMProvider } from '@prisma/client'

export async function* createCompletionStream({
  model,
  messages,
  temperature = 0.3
}: {
  model: GPTModel
  messages: ChatCompletionMessageParam[]
  temperature?: number
}): AsyncIterableIterator<string> {
  const openai = new OpenAI({ apiKey: env.OPENAI_API_KEY })

  const params: OpenAI.Chat.ChatCompletionCreateParams = {
    model,
    messages,
    temperature,
    stream: true
  }

  const stream = await openai.chat.completions.create(params)

  for await (const part of stream) {
    yield part.choices[0].delta.content ?? ''
  }
}

export async function* createReasonedCompletionStream({
  model,
  messages
}: {
  model: GPTModel
  messages: ChatCompletionMessageParam[]
}): AsyncIterableIterator<string> {
  const openai = new OpenAI({ apiKey: env.OPENAI_API_KEY })

  const params: OpenAI.Chat.ChatCompletionCreateParams = {
    model,
    messages
  }

  const response = await openai.chat.completions.create(params)
  const content = response.choices[0].message.content
  for (const word of (content || '').split(/\s+/)) {
    yield word + ' '
  }
}

export async function createCompletion({
  messages,
  model = GPTModel.GPT4x1,
  temperature = 0.3,
  max_tokens,
  frequency_penalty = 0,
  presence_penalty = 0,
  top_p = 1,
  json = true,
  teamId,
  purpose = 'completion',
  activity = 'chat'
}: {
  messages: ChatCompletionMessageParam[]
  model?: GPTModel
  temperature?: number
  max_tokens?: number
  frequency_penalty?: number
  presence_penalty?: number
  top_p?: number
  json?: boolean
  teamId?: string
  purpose?: string
  activity?: string
}): Promise<string> {
  const openai = new OpenAI({ apiKey: env.OPENAI_API_KEY })

  const params: OpenAI.Chat.ChatCompletionCreateParams = {
    model,
    messages,
    temperature,
    max_tokens,
    frequency_penalty,
    presence_penalty,
    top_p,
    response_format: json
      ? {
          type: 'json_object'
        }
      : undefined
  }

  const response = await openai.chat.completions.create(params)

  // Track token usage if teamId is provided
  if (teamId && response.usage) {
    await trackTokenUsage({
      teamId,
      provider: LLMProvider.OPENAI,
      model: response.model,
      purpose,
      activity,
      requestId: response.id,
      finishReason: response.choices[0]?.finish_reason || undefined,
      usage: response.usage
    }).catch((error) => {
      console.error('Failed to track OpenAI token usage:', error)
    })
  }

  return response.choices[0].message.content ?? ''
}
