'use client'

import { cn } from '@/lib/utils'
import { buttonVariants } from '../../ui/button'

interface DownloadExcelButtonProps {
  customName: string
  dataFunction: () => Promise<any>
}

const DownloadExcelButton: React.FC<DownloadExcelButtonProps> = ({
  customName,
  dataFunction
}) => {
  const downloadExcel = async () => {
    try {
      const dataProp = await dataFunction()

      if (!dataProp) throw new Error('No data to download')

      const today = new Date()
      const date =
        today.getDate() +
        '-' +
        (today.getMonth() + 1) +
        '-' +
        today.getFullYear()

      const XLSX = await import('xlsx')
      const ws = XLSX.utils.json_to_sheet(dataProp)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
      XLSX.writeFile(wb, `${customName}_${date}.xlsx`)
    } catch (error) {
      const errorLog = error as Error
      console.error(errorLog.message)
    }
  }

  return (
    <button className={cn(buttonVariants())} onClick={downloadExcel}>
      Download Data
    </button>
  )
}

export default DownloadExcelButton
