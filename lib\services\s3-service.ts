// lib/services/s3-service.ts

import {
  GetObjectCommand,
  PutObjectCommand,
  DeleteObjectCommand,
  S3Client
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { env } from '@/env.mjs'

export enum AWS_BUCKET {
  secure = 'sc-protected',
  public = 'smart-counsel-ai'
}

// Helper function for handling S3 errors
const handleS3Error = (error: any, operation: string, key: string): never => {
  console.error(`S3 ${operation} error for key ${key}:`, error)
  throw new Error(
    `Failed to ${operation} file from S3: ${error.message || 'Unknown error'}`
  )
}

// Create S3 client with credentials
const createS3Client = () => {
  return new S3Client({
    region: env.AWS_REGION,
    credentials: {
      accessKeyId: env.AWS_KEYID,
      secretAccessKey: env.AWS_SECRET
    }
  })
}

export async function s3UrlPut(
  key: string,
  fileType: string,
  bucket = AWS_BUCKET.public
) {
  const s3 = createS3Client()

  const params = {
    Bucket: bucket,
    Key: key,
    ContentType: fileType.startsWith('application/')
      ? fileType
      : `application/${fileType}`,
    ResponseHeaders: {
      'Access-Control-Allow-Origin': process.env.NEXT_PUBLIC_APP_URL || '*',
      'Access-Control-Allow-Methods': 'PUT',
      'Access-Control-Allow-Headers': '*'
    }
  }
  const command = new PutObjectCommand(params)
  const uploadURL = await getSignedUrl(s3, command, { expiresIn: 600 })

  return uploadURL
}

export async function s3UrlGet({
  key,
  bucket = AWS_BUCKET.public,
  expiresIn = 300 // 5 mins default
}: {
  key: string
  bucket?: AWS_BUCKET
  expiresIn?: number
}) {
  const s3 = createS3Client()

  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: key
  })

  const signedUrl = await getSignedUrl(s3, command, { expiresIn })
  return signedUrl
}

export async function s3UrlDelete(file: string) {
  try {
    const s3 = new S3Client({
      region: env.AWS_REGION,
      credentials: {
        accessKeyId: env.AWS_KEYID,
        secretAccessKey: env.AWS_SECRET
      }
    })

    const params = {
      Bucket: AWS_BUCKET.public,
      Key: file
    }

    const command = new DeleteObjectCommand(params)
    const response = await s3.send(command)

    console.log('Delete successful from s3:', response)
    return response
  } catch (error) {
    console.error('Error deleting document from s3:', error)
  }
}

/**
 * Uploads a file buffer directly to S3
 * @param buffer The file buffer to upload
 * @param key S3 key (path)
 * @param fileType MIME type of the file
 * @returns The URL to access the file
 */
export async function s3UploadFile({
  buffer,
  key,
  fileType,
  bucket = AWS_BUCKET.public
}: {
  buffer: Buffer
  key: string
  fileType: string
  bucket: AWS_BUCKET
}): Promise<string> {
  try {
    const s3 = createS3Client()

    const params = {
      Bucket: bucket,
      Key: key,
      Body: buffer,
      ContentType: fileType
    }

    // Upload the file directly
    await s3.send(new PutObjectCommand(params))

    // Return a base URL without the signature for storage
    return getS3Url({ key, bucket })
  } catch (error: any) {
    return handleS3Error(error, 'upload', key)
  }
}

/**
 * Downloads a file from S3 and returns it as a buffer
 * @param key S3 key (path)
 * @param bucket S3 bucket to download from
 * @returns Buffer containing the file contents
 */
export async function s3DownloadFile({
  key,
  bucket = AWS_BUCKET.public
}: {
  key: string
  bucket: AWS_BUCKET
}): Promise<Buffer> {
  try {
    const s3 = createS3Client()

    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: key
    })

    const s3Object = await s3.send(command)

    if (!s3Object.Body) {
      throw new Error('Empty response from S3')
    }

    const arrayBuffer = await s3Object.Body.transformToByteArray()
    return Buffer.from(arrayBuffer)
  } catch (error: any) {
    return handleS3Error(error, 'download', key)
  }
}

/**
 * Generates a consistent S3 URL for a file
 * @param key S3 key (path)
 * @param bucket S3 bucket containing the file
 * @returns S3 URL
 */
export function getS3Url({
  key,
  bucket = AWS_BUCKET.public
}: {
  key: string
  bucket: AWS_BUCKET
}): string {
  return `https://${bucket}.s3.${env.AWS_REGION}.amazonaws.com/${key}`
}
