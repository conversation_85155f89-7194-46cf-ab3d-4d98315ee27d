'use client'

import { getDocumentRecordById } from '@/lib/actions/admin'
import type { CaseData } from '@/types/document'
import { DocumentRecords } from '@prisma/client'
import { useEffect, useState } from 'react'
import { toast } from '../../ui/use-toast'
import { cn } from '@/lib/utils'
import { MetadataForm } from '../forms/admin-metadata-form'

export function DocumentShowcasePanel({
  documents,
  skipCount
}: {
  documents: Pick<DocumentRecords, 'id' | 'title' | 'createdAt' | 'updatedAt'>[]
  skipCount: number
}) {
  const [currentDocument, setCurrentDocument] = useState<number>(
    documents?.[0]?.id || 0
  )

  const [documentRecord, setDocumentRecord] = useState<{
    documentRecord: DocumentRecords
    metadata: CaseData | null
    htmlbody: string
  } | null>(null)

  useEffect(() => {
    async function fetchDocumentRecord() {
      const response = await getDocumentRecordById(currentDocument)
      if (typeof response === 'string') {
        return toast({
          title: 'Something went wrong.',
          description: response,
          variant: 'destructive'
        })
      }
      if (response) {
        const metadata = response.meta
          ? (JSON.parse(response.meta) as CaseData)
          : null

        let htmlbody = response.html
        if (htmlbody.split('<body class="mid1">')[1]) {
          htmlbody = htmlbody
            .split('<body class="mid1">')[1]
            .split('</body>')[0]
        }
        setDocumentRecord({
          documentRecord: response,
          metadata,
          htmlbody
        })
      }
    }

    fetchDocumentRecord()
  }, [currentDocument])

  return (
    <div className="grid grid-cols-8 gap-6">
      <DocumentList
        documents={documents}
        currentDocument={currentDocument}
        setCurrentDocument={setCurrentDocument}
        skipCount={skipCount}
      />
      <div className="col-span-5">
        {documentRecord?.metadata && (
          <MetadataForm
            documentId={currentDocument}
            metadata={documentRecord.metadata}
          />
        )}
        <div className="border-2 rounded-lg p-5">
          {documentRecord?.htmlbody && (
            <div
              dangerouslySetInnerHTML={{ __html: documentRecord.htmlbody }}
            />
          )}
        </div>
      </div>
    </div>
  )
}

export function DocumentList({
  documents,
  currentDocument,
  setCurrentDocument,
  skipCount
}: {
  documents: Pick<DocumentRecords, 'id' | 'title' | 'createdAt' | 'updatedAt'>[]
  currentDocument: number
  setCurrentDocument: (id: number) => void
  skipCount: number
}) {
  return (
    <div className="flex flex-col gap-2 p-2 border-2 rounded-lg col-span-3 max-h-screen overflow-y-scroll">
      {documents.map((document, i) => (
        <div
          key={document.id}
          className={cn(
            document.id === currentDocument ? 'bg-ring' : 'hover:bg-ring',
            'text-sm duration-200 p-2 rounded border-2'
          )}
          onClick={() => setCurrentDocument(document.id)}
        >
          <h3 className="font-semibold">
            {skipCount + i + 1}: {document.title}
          </h3>
          <p>ID: {document.id}</p>
          <p>Created: {new Date(document.createdAt).toLocaleDateString()}</p>
          <p>
            Last updated: {new Date(document.updatedAt).toLocaleDateString()}
          </p>
        </div>
      ))}
    </div>
  )
}
