import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { Button, buttonVariants } from '../ui/button'
import { useState } from 'react'
import { toast } from '../ui/use-toast'
import { useRouter } from 'next/navigation'
import { groupDocumentsInDataset } from '@/lib/actions/research'

const CreateDatasetDialog = ({
  selectedDocuments
}: {
  selectedDocuments: number[]
}) => {
  const router = useRouter()

  const [datasetName, setDatasetName] = useState('')

  const handleGroupDocumentsSubmit = async () => {
    try {
      const newName = datasetName.trim()

      if (newName === '') {
        toast({
          title: 'Dataset name cannot be empty',
          description: 'Please provide a name for the dataset',
          variant: 'destructive'
        })
        return
      }

      const response = await groupDocumentsInDataset(
        selectedDocuments,
        datasetName
      )

      if (response.ok) {
        toast({
          title: 'Documents grouped successfully',
          description: 'The selected documents have been grouped into a dataset'
        })
        router.refresh()
      } else {
        throw new Error('Failed to group documents')
      }
    } catch (error) {
      console.error('Error:', error)
      toast({
        title: 'Failed to group documents',
        description: 'An error occurred while grouping the documents',
        variant: 'destructive'
      })
    }
  }

  return (
    <Dialog>
      <DialogTrigger
        className={buttonVariants({
          variant: 'default',
          size: 'xs'
        })}
      >
        Create Dataset
      </DialogTrigger>
      <DialogContent className="max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>
            Group selected {selectedDocuments.length} documents to create a
            Dataset
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-3">
          <h2>Provide a dataset name</h2>
          <input
            type="text"
            placeholder="Dataset Name"
            value={datasetName}
            onChange={(e) => setDatasetName(e.target.value)}
            className="input-class p-2"
          />
        </div>
        <DialogFooter className="sm:justify-start">
          <DialogClose asChild>
            <Button onClick={handleGroupDocumentsSubmit} size="sm">
              Save
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default CreateDatasetDialog
