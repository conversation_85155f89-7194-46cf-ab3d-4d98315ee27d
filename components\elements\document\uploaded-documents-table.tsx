'use client'

import { DataTable } from '../data-table/data-table'
import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTableColumnHeader } from '../data-table/data-table-column-header'
import { Settings2 } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '../../ui/dropdown-menu'
import { useEffect, useState } from 'react'
import { Button } from '../../ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { toast } from '../../ui/use-toast'
import { deleteUploadedDocument } from '@/lib/actions/research'
import { useRouter } from 'next/navigation'
import CreateDatasetDialog from '../dataset-create-dialog'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle
} from '@/components/ui/sheet'
import { deleteFromS3Action } from '@/lib/actions/s3'
import { DocumentRecords } from '@prisma/client'

export function UploadedDocumentsTable({
  documents
}: {
  documents: Pick<DocumentRecords, 'id' | 'title' | 'createdAt' | 'indexed'>[]
}) {
  return (
    <DataTable
      columns={documentListingColumns}
      data={documents}
      bulkAction={(selectedRows) => (
        <>
          <CreateDatasetDialog selectedDocuments={selectedRows as number[]} />
          <DeleteSelectedDocumentsDialog
            selectedDocuments={selectedRows as number[]}
          />
        </>
      )}
    />
  )
}

// Document viewer sheet component
export function DocumentViewerSheet({
  documentId,
  title,
  isOpen,
  setIsOpen
}: {
  documentId: number
  title: string
  isOpen: boolean
  setIsOpen: (open: boolean) => void
}) {
  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetContent
        className="md:max-w-[80%] md:w-[80%] bg-background dark:bg-background"
        side="right"
      >
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          <SheetDescription>
            <iframe
              src={`/document-viewer/${documentId}`}
              className="w-full h-[90vh]"
            ></iframe>
          </SheetDescription>
        </SheetHeader>
      </SheetContent>
    </Sheet>
  )
}

// Document title cell component with clickable behavior
export function DocumentTitleCell({
  documentId,
  title
}: {
  documentId: number
  title: string
}) {
  const [isSheetOpen, setIsSheetOpen] = useState(false)

  return (
    <>
      <div className="flex space-x-2">
        <span
          className="max-w-[500px] truncate font-medium hover:underline cursor-pointer"
          onClick={() => setIsSheetOpen(true)}
        >
          {title}
        </span>
      </div>
      <DocumentViewerSheet
        documentId={documentId}
        title={title}
        isOpen={isSheetOpen}
        setIsOpen={setIsSheetOpen}
      />
    </>
  )
}

export function DeleteDocumentDialog({
  id,
  open,
  setOpen
}: {
  id: number
  open: boolean
  setOpen: (open: boolean) => void
}) {
  const router = useRouter()
  const handleDeleteFile = async (id: number) => {
    try {
      await deleteFromS3Action(id)
      const deleteData = await deleteUploadedDocument(id)

      if (!deleteData) {
        throw new Error('Failed to delete from DB')
      }

      toast({
        title: 'Document deleted'
      })
    } catch (error) {
      toast({
        title: 'Failed to delete document',
        description:
          error instanceof Error ? error.message : 'Something went wrong.',
        variant: 'destructive'
      })
    } finally {
      router.refresh()
      setOpen(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Document</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this document?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="destructive" onClick={() => handleDeleteFile(id)}>
            Confirm Delete
          </Button>
          <Button onClick={() => setOpen(false)} variant="secondary">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function DeleteSelectedDocumentsDialog({
  selectedDocuments
}: {
  selectedDocuments: number[]
}) {
  const router = useRouter()
  const handleDeleteFiles = async (ids: number[]) => {
    const deleteData = await Promise.all(
      ids.map((id) => deleteUploadedDocument(id))
    )
    if (deleteData) {
      toast({
        title: 'Documents deleted'
      })
    } else {
      toast({
        title: 'Failed to delete documents',
        variant: 'destructive'
      })
    }

    router.refresh()
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="destructive" size="xs">
          Delete Selected
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Documents</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete these {selectedDocuments.length}{' '}
            documents?
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button
              variant="destructive"
              onClick={() => handleDeleteFiles(selectedDocuments)}
            >
              Confirm Delete
            </Button>
          </DialogClose>
          <Button variant="secondary">Cancel</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function DocumenteTableActions({ id }: { id: number }) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  useEffect(() => {
    if (!isDeleteDialogOpen) {
      setTimeout(() => (document.body.style.pointerEvents = ''), 500)
    }
  }, [isDeleteDialogOpen])

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <Settings2 className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          <DropdownMenuItem>Edit</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => setIsDeleteDialogOpen(true)}
            className="hover:bg-red-400"
          >
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <DeleteDocumentDialog
        id={id}
        open={isDeleteDialogOpen}
        setOpen={setIsDeleteDialogOpen}
      />
    </div>
  )
}

export const documentListingColumns: ColumnDef<any>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value: any) =>
          table.toggleAllPageRowsSelected(!!value)
        }
        aria-label="Select all"
        className="translate-y-[2px] scale-110"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value: any) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px] scale-110"
      />
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'title',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      return (
        <DocumentTitleCell
          documentId={row.original.id}
          title={row.getValue('title')}
        />
      )
    }
  },
  {
    accessorKey: 'indexed',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Processed" />
    ),
    cell: ({ row }) => {
      return (
        <DocumentTitleCell
          documentId={row.original.id}
          title={row.getValue('indexed') ? 'Yes' : 'No'}
        />
      )
    }
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Uploaded" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {new Date(row.getValue('createdAt')).toLocaleDateString('de-DE', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit'
            })}
          </span>
        </div>
      )
    }
  },
  {
    id: 'actions',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Action" />
    ),
    cell: ({ row }) => <DocumenteTableActions id={row.original.id} />
  }
]
