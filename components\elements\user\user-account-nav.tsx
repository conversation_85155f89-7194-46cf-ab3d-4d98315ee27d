'use client'

import Link from 'next/link'
import { AuthUser } from 'next-auth'
import { signOut } from 'next-auth/react'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { UserAvatar } from '@/components/elements/user/user-avatar'
import { Icons } from '../icons'
import { Region } from '@prisma/client'
import { useRouter } from 'next/navigation'
import { switchRegion } from '@/lib/actions/user'
import { toast } from '../../ui/use-toast'

interface UserAccountNavProps extends React.HTMLAttributes<HTMLDivElement> {
  user: AuthUser
}

export function UserAccountNav({ user }: UserAccountNavProps) {
  const router = useRouter()

  async function handleSwitchRegion(region: Region) {
    toast({
      title: 'Updating region...'
    })
    const update = await switchRegion({ userId: user.id, region })
    if (update) {
      toast({
        title: 'Region updated successfully.'
      })
      router.refresh()
    } else {
      return toast({
        title: 'Something went wrong.',
        description: 'Please try again.',
        variant: 'destructive'
      })
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <UserAvatar
          user={{ name: user.name || null, image: user.image || null }}
          className="h-8 w-8"
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <div className="flex items-center justify-start gap-2 p-2">
          <div className="flex flex-col space-y-1 leading-none">
            {user.name && <p className="font-medium">{user.name}</p>}
            {user.email && (
              <p className="w-[200px] truncate text-sm text-muted-foreground">
                {user.email}
              </p>
            )}
          </div>
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/dashboard">Dashboard</Link>
        </DropdownMenuItem>
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <span>Region</span>
            {user.region === 'IN' ? (
              <Icons.flagIndia className="ml-2 h-4 w-4" />
            ) : (
              <Icons.flagUSA className="ml-2 h-4 w-4" />
            )}
          </DropdownMenuSubTrigger>
          <DropdownMenuPortal>
            <DropdownMenuSubContent>
              <DropdownMenuItem
                className="cursor-pointer"
                onSelect={(event) => {
                  event.preventDefault()
                  handleSwitchRegion(Region.US)
                }}
              >
                <Icons.flagUSA className="mr-2 h-4 w-4" />
                <span>US Law</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer"
                onSelect={(event) => {
                  event.preventDefault()
                  handleSwitchRegion(Region.IN)
                }}
              >
                <Icons.flagIndia className="mr-2 h-4 w-4" />
                <span>Indian Law</span>
              </DropdownMenuItem>
            </DropdownMenuSubContent>
          </DropdownMenuPortal>
        </DropdownMenuSub>
        <DropdownMenuItem asChild>
          <Link href="/dashboard/settings">Settings</Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/support">Support</Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        {user.userType === 'super' && (
          <DropdownMenuItem asChild>
            <Link href="/admin">Admin View</Link>
          </DropdownMenuItem>
        )}
        <DropdownMenuItem
          className="cursor-pointer"
          onSelect={(event) => {
            event.preventDefault()
            signOut({
              callbackUrl: `${window.location.origin}/login`
            })
          }}
        >
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
