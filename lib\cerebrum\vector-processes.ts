import { db } from '@/lib/db'
import { env } from '@/env.mjs'
import { DocumentRecords, Region, ResearchType } from '@prisma/client'
import { Pinecone } from '@pinecone-database/pinecone'
import { OpenAIEmbeddings } from '@langchain/openai'
import { PineconeStore } from '@langchain/pinecone'
import {
  fetchFullDocuments,
  generateContextArray,
  processSerialisedSources
} from '../retriever-utils'
import { assessDocumentRelevance } from './gpt-assisted-processes'
import type {
  QueryAssessmentResponse,
  ResearchStoreContent,
  StoredNamespace
} from '@/types'
import { developer } from '../utils'
import { Document } from 'langchain/document'
import { searchNearestPrivateVectors, searchNearestVectors } from '../pg-db'

export async function fetchResearchContext({
  question,
  relatedQuestions,
  researchType,
  assessment,
  namespace,
  summarise,
  filter,
  region,
  isPrivate = false,
  takeMin
}: {
  question: string
  relatedQuestions: string[]
  researchType: ResearchType
  assessment: QueryAssessmentResponse
  namespace: StoredNamespace[Region] | string
  summarise: boolean
  filter: Pick<ResearchStoreContent, 'court' | 'year' | 'sources'>
  region: Region
  isPrivate?: boolean
  takeMin?: number
}) {
  try {
    let documentCollection: DocumentRecords[] = []
    const documentRecordsIds =
      filter.sources &&
      filter.sources.length > 0 &&
      assessment.independent === 'no'
        ? filter.sources.map((source) => parseInt(source))
        : undefined

    if (summarise && documentRecordsIds && documentRecordsIds.length === 0) {
      const document = await db.documentRecords.findUnique({
        where: {
          id: documentRecordsIds[0]
        }
      })

      if (document) {
        return {
          context: document.content,
          serializedSources: ''
        }
      }
    }

    const isSpecificCase = assessment.specific_case === 'yes'
    const hasSources = filter.sources.length > 0
    const isNotIndependent = assessment.independent === 'no'

    const docsToSearch = isSpecificCase
      ? hasSources && isNotIndependent
        ? 10
        : 1
      : 6

    let documents: Document[] = []
    if (isPrivate) {
      documents = await fetchVectorsFromPrivatePg({
        documentRecordsIds,
        region,
        namespace,
        question,
        relatedQuestions,
        docsToSearch
      })
    } else if (region === Region.US) {
      documents = await fetchVectorsFromPg({
        documentRecordsIds,
        filter,
        region,
        namespace,
        question,
        relatedQuestions,
        docsToSearch
      })
    } else {
      documents = await fetchVectorsFromPinecone({
        documentRecordsIds,
        filter,
        namespace,
        question,
        relatedQuestions,
        docsToSearch
      })
    }

    const filterRelevantDocuments = await assessDocumentRelevance({
      questions: [question, ...relatedQuestions],
      documents,
      takeMin
    })

    if (filterRelevantDocuments.length === 0) {
      throw new Error('No relevant documents found')
    }

    documentCollection = await fetchFullDocuments(
      filterRelevantDocuments.map((doc) => doc.metadata.documentRecordsId)
    )

    const context = generateContextArray({
      documents: filterRelevantDocuments,
      documentCollection
    })

    const titleSet = documentCollection.map((doc) => {
      return {
        title: doc.title,
        id: doc.id
      }
    })

    const serializedSources = processSerialisedSources({
      titleSet,
      documents: filterRelevantDocuments
    })

    return {
      context,
      serializedSources
    }
  } catch (error: any) {
    console.error(error.message)
    return {
      context:
        researchType === ResearchType.case
          ? '###  NO RELEVANT CASES FOUND  ###'
          : '###  NO RELEVANT LAWS FOUND  ###',
      serializedSources: ''
    }
  }
}

async function fetchVectorsFromPinecone({
  documentRecordsIds,
  filter,
  namespace,
  question,
  relatedQuestions,
  docsToSearch = 3
}: {
  documentRecordsIds?: number[]
  filter: {
    court: string[]
    year: string[]
  }
  namespace: string

  question: string
  relatedQuestions: string[]
  docsToSearch?: number
}) {
  const filterQuery = {
    $and: []
  } as {
    $and: any[]
  }

  if (documentRecordsIds && documentRecordsIds.length > 0) {
    filterQuery.$and = [{ documentRecordsId: { $in: documentRecordsIds } }]
  }

  if (filter.court && filter.court.length > 0) {
    filterQuery.$and.push({
      court: {
        $in: filter.court
      }
    })
  }

  if (filter.year && filter.year.length > 0) {
    filterQuery.$and.push({
      year: {
        $in: filter.year.map((year) => parseInt(year))
      }
    })
  }

  developer.log([
    'vector filters',
    namespace,
    JSON.stringify(filterQuery, null, 2)
  ])

  const pinecone = new Pinecone()
  const pineconeIndex = pinecone.Index(env.PINECONE_INDEX)
  const vectorstore = await PineconeStore.fromExistingIndex(
    new OpenAIEmbeddings(),
    {
      pineconeIndex,
      filter: filterQuery.$and.length > 0 ? filterQuery : undefined,
      namespace
    }
  )

  vectorstore

  // Run the related searches in parallel
  const relatedSearches = [question, ...relatedQuestions].map(
    (relQuestion, index) =>
      vectorstore.similaritySearch(relQuestion, index === 0 ? docsToSearch : 3)
  )

  const searchResults = await Promise.all(relatedSearches)
  const documents = searchResults.flat().filter((doc, index, self) => {
    return index === self.findIndex((t) => t.pageContent === doc.pageContent)
  })

  return documents
}

async function fetchVectorsFromPg({
  documentRecordsIds,
  region,
  filter,
  namespace,
  question,
  relatedQuestions,
  docsToSearch = 3
}: {
  documentRecordsIds?: number[]
  region: Region
  filter: {
    court: string[]
    year: string[]
  }
  namespace: string

  question: string
  relatedQuestions: string[]
  docsToSearch?: number
}) {
  let documents: Document[] = []
  const documentRecords = [question, ...relatedQuestions].map(
    async (relQuestion, index) => {
      const searchResults = await searchNearestVectors({
        queryText: relQuestion,
        options: {
          region: region,
          documentRecordsIds,
          namespace,
          court: filter.court,
          year: filter.year.map((year) => parseInt(year)),
          limit: index === 0 ? docsToSearch : 3
        }
      })

      return searchResults
    }
  )

  documents = (await Promise.all(documentRecords))
    .flat()
    .filter((doc, index, self) => {
      return index === self.findIndex((t) => t.id === doc.id)
    })

  return documents
}

async function fetchVectorsFromPrivatePg({
  documentRecordsIds,
  region,
  namespace,
  question,
  relatedQuestions,
  docsToSearch = 3
}: {
  documentRecordsIds?: number[]
  region: Region
  namespace: string

  question: string
  relatedQuestions: string[]
  docsToSearch?: number
}) {
  let documents: Document[] = []
  const documentRecords = [question, ...relatedQuestions].map(
    async (relQuestion, index) => {
      const searchResults = await searchNearestPrivateVectors({
        queryText: relQuestion,
        options: {
          region,
          documentRecordsIds,
          namespace,
          limit: index === 0 ? docsToSearch : 3
        }
      })

      return searchResults
    }
  )

  documents = (await Promise.all(documentRecords))
    .flat()
    .filter((doc, index, self) => {
      return index === self.findIndex((t) => t.id === doc.id)
    })

  return documents
}
