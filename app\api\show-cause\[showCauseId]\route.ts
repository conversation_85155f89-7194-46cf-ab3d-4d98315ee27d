import * as z from 'zod'

import { NextRequest, NextResponse } from 'next/server'
import { ChatOpenAI } from '@langchain/openai'
import { fetchVectors } from '@/lib/retriever-utils'
import { getCurrentUserResponse } from '@/lib/session'
import { UnauthorizedError } from '@/lib/exceptions'
import { db } from '@/lib/db'
import { OutputData } from '@editorjs/editorjs'
import { GPTModel } from '@/types'

export const maxDuration = 800

export interface EditorJsContent extends OutputData {}

const routeContextSchema = z.object({
  params: z.object({
    showCauseId: z.string()
  })
})

export async function GET(
  req: NextRequest,
  context: z.infer<typeof routeContextSchema>
) {
  try {
    const user = await getCurrentUserResponse()
    if (!user.id) {
      throw new UnauthorizedError()
    }

    const { params } = routeContextSchema.parse(context)
    let refresh = false

    let dataRecord = await db.showCauseNotice.findUnique({
      where: {
        id: params.showCauseId
      }
    })

    if (!dataRecord) {
      return NextResponse.json({ error: 'Record not found' }, { status: 404 })
    }

    if (
      dataRecord.status === 'summerized' ||
      dataRecord.status === 'published'
    ) {
      // do nothing
    } else if (
      !dataRecord.showCauseJudgementSummary ||
      !dataRecord.caseRecords
    ) {
      const { showCauseJudgementSummary, vectorDocs } =
        await generateShowCauseJudgement({
          showCauseNoticeSummary: JSON.parse(dataRecord.showCauseNoticeSummary),
          showCauseReplySummary: JSON.parse(dataRecord.showCauseReplySummary)
        })

      dataRecord = await db.showCauseNotice.update({
        where: {
          id: params.showCauseId
        },
        data: {
          showCauseJudgementSummary: JSON.stringify(showCauseJudgementSummary),
          caseRecords: JSON.stringify(vectorDocs)
        }
      })

      refresh = true
    } else if (dataRecord.processedLength < dataRecord.orderLength) {
      let finalJudgement: EditorJsContent = await generateFinalJudgement({
        queryNumber: dataRecord.processedLength,
        totalQueries: dataRecord.orderLength,
        finalJudgementSummary: dataRecord.finalJudgementSummary || '',
        showCauseNoticeSummary: JSON.parse(dataRecord.showCauseNoticeSummary),
        showCauseReplySummary: JSON.parse(dataRecord.showCauseReplySummary),
        vectorDocs: JSON.parse(dataRecord.caseRecords)
      })

      const currentJudgementRecord: EditorJsContent | null =
        dataRecord.finalJudgementSummary
          ? JSON.parse(dataRecord.finalJudgementSummary)
          : null

      if (currentJudgementRecord) {
        finalJudgement.blocks = [
          ...currentJudgementRecord.blocks,
          ...finalJudgement.blocks
        ]
      }

      dataRecord = await db.showCauseNotice.update({
        where: {
          id: params.showCauseId
        },
        data: {
          finalJudgementSummary: JSON.stringify(finalJudgement),
          processedLength: dataRecord.processedLength + 1
        }
      })

      refresh = true
    } else {
      dataRecord = await db.showCauseNotice.update({
        where: {
          id: params.showCauseId
        },
        data: {
          status: 'summerized'
        }
      })
    }

    return NextResponse.json({
      id: dataRecord.id,
      status: dataRecord.status,
      orderLength: dataRecord.orderLength,
      processedLength: dataRecord.processedLength,
      refresh
    })
  } catch (e: any) {
    console.log(e)
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}

async function generateShowCauseJudgement({
  showCauseNoticeSummary,
  showCauseReplySummary
}: {
  showCauseNoticeSummary: string[]
  showCauseReplySummary: string[]
}) {
  const jsonModeModel = new ChatOpenAI({
    model: GPTModel.GPT4o
  }).bind({
    response_format: {
      type: 'json_object'
    }
  })

  const showCauseJudgement = await jsonModeModel.invoke([
    [
      'system',
      'The following is a summary of a show cause notice from Indian GST to a vendor and the vendor response. Review the document and take our key points that needs to be reviewed against past cases to form a final judgement. These points will be queried against a vector database to find relevant cases. Provide response in JSON array. {response: string[]}'
    ],
    [
      'human',
      `
            Show Cause Notice Summary:
            ${showCauseNoticeSummary.join('\n\n')}

            Show Cause Reply Summary:
            ${showCauseReplySummary.join('\n\n')}
          `
    ]
  ])

  let showCauseJudgementSummary = showCauseJudgement.content as any
  try {
    showCauseJudgementSummary = JSON.parse(showCauseJudgementSummary).response
  } catch (error) {}

  const vectorDocs: any = []

  try {
    for (const summary of showCauseJudgementSummary) {
      const { documentSpecificContent, documentCollection } =
        await fetchVectors(summary)
      vectorDocs.push(...documentSpecificContent)
    }
  } catch (error) {
    console.log(error)
  }

  return { showCauseJudgementSummary, vectorDocs }
}

async function generateFinalJudgement({
  queryNumber,
  totalQueries,
  finalJudgementSummary,
  showCauseNoticeSummary,
  showCauseReplySummary,
  vectorDocs
}: {
  queryNumber: number
  totalQueries: number
  finalJudgementSummary: string
  showCauseNoticeSummary: string[]
  showCauseReplySummary: string[]
  vectorDocs: string[]
}) {
  const jsonModeModel = new ChatOpenAI({
    model: GPTModel.GPT4o
  }).bind({
    response_format: {
      type: 'json_object'
    }
  })

  const finalJudgement = await jsonModeModel.invoke([
    [
      'system',
      `The following are summaries related to a show cause notice issued by Indian GST to a vendor, the vendor's response, and a collection of relevant case materials. Review these documents and formulate a very detailed case order based on the facts and the law. Provide your response in paragraphs, which can be up to 6000 words in total. This task will be completed in ${totalQueries} sections each a separate prompt, so you do not need to finalize the case in one go. Instead, build on each section progressively, ensuring continuity and coherence in your legal analysis. Provide titles and bulletpoints where needed. This is section ${queryNumber} of the final judgement. Your output should be in the universal editorjs JSON output format.

      ${
        queryNumber > 0
          ? `Continuing from the last section, which ended with:
        ${finalJudgementSummary.slice(-500)}`
          : `This is the first section to the final judgement.`
      }
      `
    ],
    [
      'human',
      `
          Show Cause Notice Summary:
          ${showCauseNoticeSummary.join('\n\n')}

          Show Cause Reply Summary:
          ${showCauseReplySummary.join('\n\n')}

          Related Case Documents:
          ${vectorDocs.join('\n\n')}
        `
    ]
  ])

  const finalJudgementJson = JSON.parse(finalJudgement.content as string)

  return finalJudgementJson
}
