import type { NextApiRequest, NextApiResponse } from 'next'
import { db } from '@/lib/db'
import fs from 'fs'
import path from 'path'
import { GPTModel } from '@/types'
import { generateMetadata, uploadFileToOpenAi } from './batch-utilts'
import OpenAI from 'openai'
import { env } from '@/env.mjs'
import { Region } from '@prisma/client'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (env.NODE_ENV !== 'development') {
      throw new Error('Unauthorized')
    }

    const source = new Date().getTime().toString()
    let lastId = req.query.lastId ? parseInt(String(req.query.lastId)) : 0
    const SET_SIZE = 8000
    const BATCH_SIZE = 100
    let totalRecords = 0
    const openai = new OpenAI()

    const teams = await db.team.findMany({
      select: {
        id: true
      }
    })

    const where = {
      id: {
        gt: lastId
      },
      source: {
        notIn: teams.map((t) => t.id)
      },
      region: Region.US,
      html_cleaned: true,
      meta_ready: false,
      // indexed: false,
      content: {
        not: ''
      }
    }

    if (lastId === 0) {
      const firstId = await db.documentRecords.findFirst({
        select: {
          id: true
        },
        where
      })
      lastId = firstId?.id || 0
    }

    setLoop: for (let i = 0; i < 1200000; i += SET_SIZE) {
      let loopEnd = false
      const uploadIDset: number[] = []
      const outputPath = `dev/gpt_results/${source}-${i}-source.txt`
      // Ensure directory exists and open a write stream
      const outputDir = path.dirname(outputPath)
      await fs.promises.mkdir(outputDir, { recursive: true })

      const outputStream = fs.createWriteStream(outputPath, {
        encoding: 'utf8',
        flags: 'a'
      }) // 'a' for append mode

      // Attach error event handler
      outputStream.on('error', (error) => {
        console.error('Error writing the TXT file:', error)
      })

      for (let j = 0; j < SET_SIZE; j += BATCH_SIZE) {
        const records = await db.documentRecords.findMany({
          select: {
            id: true,
            ref: true,
            meta: true,
            content: true
          },
          where: {
            ...where,
            id: {
              gt: lastId
            }
          },
          take: BATCH_SIZE,
          orderBy: {
            id: 'asc'
          }
        })

        if (records.length === 0) {
          loopEnd = true
        }

        uploadIDset.push(...records.map((r) => r.id))

        lastId = records[records.length - 1].id
        totalRecords += records.length
        console.log('Total records: ', records.length, ' in batch: ', j, ' last id: ', lastId) // prettier-ignore

        for (const record of records) {
          const store = await generateMetadata(record, GPTModel.GPT4oMini)
          if (store) {
            // Directly write the JSON representation to the txt file
            outputStream.write(JSON.stringify(store) + '\n')
          }
        }
      }

      // Close the write stream properly using async
      await new Promise<void>((resolve, reject) => {
        outputStream.end((err: any) => {
          if (err) return reject(err)
          resolve()
        })
      })

      // Rename the file to .jsonl after the write is complete
      const jsonlPath = outputPath.replace('.txt', '.jsonl')
      try {
        await fs.promises.rename(outputPath, jsonlPath)
        console.log(`File renamed to jsonl`)
      } catch (err) {
        console.error('Error renaming the file:', err)
      }

      // Upload the file to OpenAI
      const upload = await uploadFileToOpenAi({
        path: jsonlPath,
        openai
      })

      // Insert metadata into the database
      await db.openAiBatchProcess.create({
        data: {
          type: 'case-metadata',
          payload: JSON.stringify(uploadIDset),
          inputFileId: upload.input_file_id,
          batchId: upload.id,
          status: upload.status,
          uploadResponse: JSON.stringify(upload)
        }
      })

      // Delete the file after successful upload
      try {
        await fs.promises.unlink(jsonlPath)
        console.log(`File deleted`)
      } catch (err) {
        console.error('Error deleting the file:', err)
      }

      console.log('\n\n<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>\n')
      console.log(
        'Loop ' + i + ' ended: ' + totalRecords + ' records ending at ' + lastId
      )
      console.log('\n<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>\n')

      if (loopEnd) {
        console.log('Loop end')
        break setLoop
      }

      // wait for 5 minutes before starting the next batch
      console.log('Waiting for 5 minutes before starting the next batch...')
      await new Promise((resolve) => setTimeout(resolve, 30 * 10 * 1000))

      console.log('Starting the next batch...')
    }

    console.log('\n\n<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>\n')
    console.log('FINISHED ' + totalRecords)
    console.log('\n<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>\n')

    res
      .status(200)
      .json({ message: 'Data saved successfully!', lastId, totalRecords })
  } catch (error) {
    console.error(error)
    res.status(500).json({ error: 'Failed to index.' })
  }
}
