import { PromptStore } from '@/types/case'

export const demandLetterPrompts: PromptStore[] = [
  {
    id: 'cover_letter',
    title: 'Cover Letter',
    contextNeeded: [
      'PLAINTIFF_INFO',
      'INCIDENT_DETAILS',
      'ATTORNEY_STRATEGY_PLAN'
    ],
    prompt: `Generate a professional demand letter cover page addressed to the insurance company, following the exact format and structure seen in the sample demand letter. Include these specific elements in this order:

        1) Delivery method in all caps (e.g., 'VIA EMAIL: [email address]')
        2) Full recipient information including:
           - Insurance company name
           - Claims adjuster's full name and title
           - Complete mailing address with each component on its own line
        3) Subject line in this exact format: 'RE: BODILY INJURY DEMAND FOR POLICY LIMITS'
        4) Reference lines for:
           - 'Our Client: [PLAINTIFF'S FULL NAME]'
           - 'Your Insured: [DEFENDANT'S FULL NAME]'
           - 'Date of Incident: [Month DD, YYYY]' (use the exact date from INCIDENT_DETAILS)
           - 'Claim No.: [CLAIM NUMBER]'
        5) Formal salutation (e.g., 'Dear Mr./Ms. [LAST NAME] and All Insurance Company Decision-Makers,')
        6) Opening paragraph that:
           - States your firm represents the plaintiff (use the exact format: 'Our firm represents [PLAINTIFF NAME] ("Mr./Ms. [LAST NAME]" or "our client") in connection with...')
           - Describes the incident type (e.g., 'motor vehicle collision', 'slip and fall', etc.)
           - States the exact date of incident
           - Identifies the defendant (use format: '[DEFENDANT NAME] ("Mr./Ms. [LAST NAME]" or "your insured")')
        7) A paragraph stating this is for settlement purposes pursuant to specific state settlement statute (e.g., 'Fla. Stat. § 90.408') and that all rights are reserved
        8) A clear request to inform you of additional items needed within 10 days
        9) Request to forward the demand to all insurance company decision-makers and the insured

        The tone should be formal, professional, and assertive without being aggressive. Use legal terminology appropriate for correspondence with insurance professionals. Format the letter with proper spacing between sections and align the date, recipient info, and subject lines according to standard business letter format. Personalize with the correct names but use 'XXXX' or similar placeholder if specific names are not provided in the context.`
  },
  {
    id: 'facts_and_liability',
    title: 'Facts & Liability',
    contextNeeded: [
      'INCIDENT_DETAILS',
      'LIABILITY_ASSESSMENT',
      'PLAINTIFF_INFO'
    ],
    prompt: `Create the 'Facts & Liability' section of the demand letter with the following specific structure and content:

        1) Use the heading '1. FACTS & LIABILITY' in bold, followed by a line break
        
        2) Write a detailed narrative paragraph (2-4 paragraphs) of the incident that includes:
           - Exact date, time, and location (city, county, state) of the incident
           - Precise description of what the plaintiff was doing immediately before the incident
           - Step-by-step account of how the incident occurred, including directions of travel if applicable
           - Specific description of the impact or injury-causing event
           - Immediate result (e.g., 'The collision resulted in Mr. XXXX sustaining significant personal injuries and other damages')

        3) Reference any official investigation, using this format:
           - 'As exposed in the [OFFICIAL REPORT NAME],¹ [INVESTIGATOR NAME/TITLE] of the [AGENCY] investigated the [incident type] and determined that [DEFENDANT] was [responsibility finding] due to [specific violations or actions].'
           - Include a footnote with 'Exhibit [#] - [Report Name]'
           - If there are photographs or diagrams, mention them and reference their exhibit numbers
        
        4) Include a paragraph establishing legal liability that:
           - States 'The conditions for establishing liability are clear'
           - Outlines the legal duty of care (reference specific state statutes if available)
           - Explains how the defendant breached that duty
           - Establishes the causal link between the breach and plaintiff's injuries
        
        5) Cite specific applicable state statutes that establish liability, in this format:
           - 'Pursuant to [STATE] Stat. § [NUMBER], [summarize the legal requirement]'
           - Include 2-3 relevant statutes (e.g., traffic laws, premises liability standards, etc.)
           - For each statute, explain how the defendant's actions violated it

        6) Include a paragraph that establishes the defendant's negligence or recklessness:
           - Start with 'Here, it is clear that 100% liability rests with [DEFENDANT]'
           - Describe the defendant's actions as 'reckless,' 'willful,' or showing 'utter disregard' as appropriate
           - Specify exactly how defendant's actions violated the plaintiff's rights
        
        7) Conclude with a statement about how a jury would view the facts and the defendant's failure to prevent harm

        The tone should be factual, authoritative, and assertive. Use precise legal terminology. Format with standard paragraph spacing and proper footnote citations to exhibits. If any factual inconsistencies exist in the provided information, note them in a bracketed comment at the end of the section (e.g., '[NTD: The provided request indicates...]'). All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'injuries_and_treatments',
    title: 'Injuries & Treatments',
    contextNeeded: ['MEDICAL_CHRONOLOGY', 'PLAINTIFF_INFO', 'CASE_EVALUATION'],
    prompt: `Generate the 'Injuries & Treatments' section with these exact components and formatting:

        1) Begin with the heading '2. INJURIES & TREATMENTS' in bold, followed by a line break

        2) Create a subsection titled '2.1. Summary of Injuries' with:
           - A brief introductory sentence: 'The below tables are non-exhaustive lists/summaries of the injuries and treatments Mr./Ms. [LAST NAME] sustained as a direct and proximate result of the [DATE] [incident type].'
           - A table listing all injuries with their corresponding ICD codes with these exact columns:
             * ICD Code (e.g., M25.511)
             * Description (e.g., 'Pain in Right Shoulder')
           - Include all diagnosed conditions, organized by body region from head to toe
           - Format the table with clear borders and proper alignment

        3) Create a subsection titled '2.2. Treatment' that provides a chronological breakdown of all medical providers visited, with each provider in its own subsection containing:
           - Provider name in bold
           - A table with rows for:
             * Timeline (date range of treatment, e.g., 'February 27, 2023 - May 26, 2024')
             * No. Visits (exact number of visits)
             * Summary (detailed bullet points of treatments)
             * Documents (list of exhibits that support this provider's treatment)
           - Under 'Summary', include these specific details for each provider:
             * For initial visits: presenting complaints, examination findings, diagnostic tests ordered, diagnoses given, and treatment plan recommended
             * For follow-up visits: changes in symptoms, progression of treatment, responses to treatment
             * For surgical procedures: exact procedure performed, outcome, post-operative care
             * For diagnostic imaging: specific findings quoted directly from reports
             * Doctor's opinions on causation (use exact phrasing like 'Dr. XXXX opined that Mr. XXXX's injuries, to a reasonable degree of medical certainty, were directly related to his [incident date] [incident type]')

        4) For each provider, format the 'Summary' section as bullet points with the following pattern:
           - Start with '● On [DATE], Mr./Ms. [LAST NAME] presented to [PROVIDER NAME/TITLE] for [PURPOSE OF VISIT].'
           - Include key details of the examination, diagnosis, and treatment
           - For follow-up visits, start with '● On [DATE], Mr./Ms. [LAST NAME] returned to [PROVIDER NAME] for [PURPOSE]'

        5) For the 'Documents' section, use bullet points to list:
           - '● Exhibit [#] - [Provider Name] - Records [doc-id:page]'
           - '● Exhibit [#] - [Provider Name] - Bills [doc-id:page]'

        6) Include bracketed notes about any missing documentation or inconsistencies, formatted as:
           '[NTD: Individual date of service records were not present for visits on [DATES]. Based upon the provided billing, it appears that these were routine service visits which likely did not yield additional diagnoses, though you may wish to confirm with the provider.]'

        Ensure the entire section maintains strict chronological order by date of treatment. Use medical terminology accurately but explain complex terms if needed. Format exactly as shown in the sample demand letter, with consistent spacing and alignment. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'damages_overview',
    title: 'Damages Overview',
    contextNeeded: [
      'ECONOMIC_DAMAGES',
      'NON_ECONOMIC_DAMAGES',
      'PUNITIVE_DAMAGES',
      'DAMAGES_CALCULATION'
    ],
    prompt: `Create the 'DAMAGES' section with a comprehensive overview table that follows this exact format:

        1) Begin with the heading '3. DAMAGES' in bold, followed by a line break

        2) Create a subsection titled '3.1. Total Projected Claim Value'

        3) Create a professional-looking table with the following structure:
           - Title the table 'Elements of Damages' (centered above the table)
           - Create these main categories with their corresponding subcategories:

             A. Economic Damages (category heading, left-aligned, bold)
                - Past Medical Expenses (subcategory, indented, regular font)
                - Future Medical Expenses (subcategory, indented, regular font)
                - Loss of Income (subcategory, indented, regular font)
                - Loss of Household Services (subcategory, indented, regular font)

             B. Non-Economic Damages (category heading, left-aligned, bold)
                - Past and Future Pain and Suffering (subcategory, indented, regular font)
                - Emotional Distress (subcategory, indented, regular font)
                - Loss of Enjoyment (subcategory, indented, regular font)
                - Disfigurement (subcategory, indented, regular font)
                - From both per-diem and multiplier analysis only show the one that has the higher value.

             C. Punitive Damages (category heading, left-aligned, bold, only include if applicable based on context)
                - No subcategories for this section

             D. Total Damages (category heading, left-aligned, bold)
                - This should be the sum of all above damages

           - The right column should contain dollar amounts for each line:
             * Format all dollar amounts consistently (e.g., '$117,892.42')
             * Right-align all dollar figures
             * Ensure proper accounting format with decimal places
             * Use comma separators for thousands
             * Include dollar signs

        4) For the dollar values, use the exact figures from the DAMAGES_CALCULATION context if provided

        5) Format the table with clear borders, proper alignment, and consistent spacing

        The table should be clean, professional, and easy to read. It should occupy its own page or be positioned with adequate white space above and below. Follow the exact styling, formatting, and organization seen in the sample demand letter's damages overview section. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'past_medical_expenses',
    title: 'Past Medical Expenses',
    contextNeeded: [
      'MEDICAL_CHRONOLOGY',
      'ECONOMIC_DAMAGES',
      'EXTRACTED_FINANCIAL_DATA'
    ],
    prompt: `Generate a detailed 'Past Medical Expenses' section with the following specific components and format:

        1) Begin with the heading '3.2. Past Medical Expenses' in bold, followed by a line break

        2) Start with this opening sentence: 'To date, Mr./Ms. [LAST NAME] has incurred medical expenses as itemized below.'

        3) Create a comprehensive, properly formatted table with these exact columns:
           - Provider (name of medical provider/facility)
           - Date of Service (either specific date or date range in format 'MM/DD/YYYY' or 'MM/DD/YYYY to MM/DD/YYYY')
           - Amount Charged (dollar amount formatted with dollar sign, commas for thousands, and two decimal places)
           - Supporting Document(s) (reference to specific exhibit numbers)

        4) Include every medical provider mentioned in the MEDICAL_CHRONOLOGY, organized chronologically by first date of service

        5) For providers with multiple visits over a period, use the format:
           - Provider: Full facility/practice name
           - Date of Service: 'MM/DD/YYYY to MM/DD/YYYY'
           - Amount: Total for all services
           - Supporting Document(s) / Exibits

        6) At the bottom of the table, include a 'Total' row that correctly sums all amounts

        7) After the table, include any necessary explanatory notes about potentially missing documentation, formatted as:
           '[NTD: As noted in the medical summary for [Provider Name], we believe there is missing documentation for [description of what's missing], which may include additional medical billing.]'

        8) Conclude with this exact paragraph:
           'If you dispute any of Mr./Ms. [LAST NAME]'s medical treatment or bills as unnecessary or unreasonable, please specify the disputed items in writing. Otherwise, we will assume you agree with the necessity and reasonableness of Mr./Ms. [LAST NAME]'s medical treatments and bills.'

        9) Format the table with:
           - Clear borders around all cells
           - Column headers in bold
           - Consistent cell padding
           - Proper alignment (left-align text, right-align numbers)
           - Proper spacing between table rows

        Ensure all dollar amounts are formatted consistently and match the totals referenced elsewhere in the document. The table should be comprehensive, accurate, and professional in appearance, exactly matching the format seen in the sample demand letter. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'future_medical_expenses',
    title: 'Future Medical Expenses',
    contextNeeded: [
      'MEDICAL_CHRONOLOGY',
      'ECONOMIC_DAMAGES',
      'EXTRACTED_FINANCIAL_DATA'
    ],
    prompt: `Create the 'Future Medical Expenses' section with these specific elements and formatting:

        1) Begin with the heading '3.3. Future Medical Expenses' in bold, followed by a line break

        2) Open with this sentence: 'Mr./Ms. [LAST NAME] will require additional future treatment as identified below.'

        3) Create a detailed table with these exact columns:
           - Procedure (name of the medical procedure/treatment/appointment)
           - Years (duration for which the treatment is needed, expressed as a number)
           - Per Year (frequency of treatment needed each year, expressed as a number)
           - Cost (cost per procedure, formatted with dollar sign, commas for thousands, and two decimal places)
           - Total (calculated by multiplying Years × Per Year × Cost, formatted with dollar sign, commas, and two decimal places)

        4) Include these specific categories of future medical expenses (if applicable based on the context):
           - Follow-up appointments with specialists (orthopedic, neurology, pain management, etc.)
           - Physical therapy or rehabilitation sessions
           - Diagnostic imaging (MRIs, X-rays, CT scans)
           - Injections or pain management procedures
           - Surgical interventions
           - Medication costs
           - Assistive devices or home modifications
           - Chiropractic care or alternative treatments

        5) For each row, include a superscript footnote number at the end of the procedure name that corresponds to the source document

        6) Calculate and include a 'Total' row at the bottom that correctly sums all amounts in the Total column

        7) After the table, include this exact paragraph:
           'Healthcare and medication costs are expected to rise, and we reserve the right to update or extend our estimate to account for further care needs.'

        8) After this paragraph, include footnotes that cite the source documents for each future expense, formatted as:
           'Exhibit - [Provider Name] - [Document Type] [doc-id:page]'
           'Exhibit - [Provider Name] - [Document Type] [doc-id:page]'
           etc.

        9) Format the table with:
           - Clear borders around all cells
           - Column headers in bold and centered
           - Proper alignment (left-align text, right-align numbers)
           - Consistent cell padding and spacing

        Ensure all projected treatments are based on physician recommendations documented in the medical records. The calculations should be mathematically correct, and the formatting should exactly match the sample demand letter's future medical expenses section. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'loss_of_income',
    title: 'Loss of Income',
    contextNeeded: [
      'PLAINTIFF_INFO',
      'ECONOMIC_DAMAGES',
      'EXTRACTED_FINANCIAL_DATA'
    ],
    prompt: `Generate the 'Loss of Income' section with these specific components and formatting:
        
        1) Begin with the heading '3.4. Loss of Income' in bold, followed by a line break

        2) Write a concise paragraph (4-6 lines) that includes these specific elements:
           - Reference to an enclosed Economic Loss Report (e.g., 'Based on the enclosed Economic Loss Report estimating that Mr./Ms. [LAST NAME] will be unable to return to work until at least [DATE].')
           - State the exact total amount of lost wages (e.g., 'Mr./Ms. [LAST NAME] suffered a loss of income totaling $XX,XXX.XX')
           - Explain why the plaintiff was unable to work (e.g., 'when he/she was rendered unable to work due to the injuries he/she sustained in the [INCIDENT DATE] [incident type]')
        
        3) Format the dollar amount in bold to highlight it visually

        4) Include a footnote reference after mentioning the Economic Loss Report, formatted as a superscript number

        5) At the bottom of the page, include the footnote with the exact reference from documents:
           'Exhibit XX - Loss of Income Documentation and Economic Loss Report. [doc-id:page]'

        6) If the context provides details about:
           - The plaintiff's occupation, include it (e.g., 'as a [OCCUPATION]')
           - Pre-injury wage/salary, include it (e.g., 'where he/she earned $XX.XX per hour/week/year')
           - Specific time periods missed, include them (e.g., 'from [START DATE] to [END DATE]')
           - Partial vs. total work disability, clarify this (e.g., 'was completely unable to work' or 'was restricted to light duty resulting in reduced hours')

        Keep this section brief but precise. The tone should be factual and matter-of-fact. Format with appropriate spacing before and after the section. If specific details about income loss calculation are not provided in the context, keep the language more general but still professionally formatted, following the pattern in the sample demand letter.`
  },
  {
    id: 'loss_of_household_services',
    title: 'Loss of Household Services',
    contextNeeded: [
      'PLAINTIFF_INFO',
      'ECONOMIC_DAMAGES',
      'EXTRACTED_FINANCIAL_DATA'
    ],
    prompt: `Create the 'Loss of Household Services' section with these specific elements and formatting:

        1) Begin with the heading '3.5. Loss of Household Services' in bold, followed by a line break

        2) Start with this sentence: 'Mr./Ms. [LAST NAME] has been unable to contribute valuable household services for which he/she is entitled to the below compensation:'

        3) Include a numbered list (1-3) explaining the basis for the calculation:
           - Item 1: Establish pre-incident baseline contribution (e.g., 'Prior to [INCIDENT DATE], Mr./Ms. [LAST NAME] contributed an estimated average of [X.XX] hours of household services per day to his/her household, in line with the average contribution of similarly aged persons in [STATE].')
           - Item 2: Establish monetary value (e.g., 'The monetary value of Mr./Ms. [LAST NAME]'s labor is consistent with the average housekeeping salary of $[XX.XX] an hour in [STATE], as estimated from the [MONTH YEAR] Report on Occupational Employment and Wages by the U.S. Bureau of Labor Statistics.⁷')
           - Item 3: Detail the periods and percentages of impairment (e.g., 'Mr./Ms. [LAST NAME] suffered a [XX]% impairment from contributing to household services from [START DATE] to [END DATE], and a conservative [XX]% impairment for [X] years thereafter.')

        4) Add a paragraph summarizing the total value, using this format:
           'Assuming no further impairment of household services after [END DATE], we calculate the value of Mr./Ms. [LAST NAME]'s loss of household services between [START DATE] and [END DATE] at $[XX,XXX.XX].'

        5) Create a table titled 'Loss of Household Services Schedule' with these columns:
           - Start of Loss Date (formatted as MM/DD/YYYY)
           - End of Loss Date (formatted as MM/DD/YYYY)
           - Hourly Rate (dollar amount with dollar sign and two decimal places)
           - Hours Per Day (number with two decimal places)
           - % Impaired (percentage)
           - Net Loss (dollar amount with dollar sign, commas, and two decimal places)

        6) Include a row for each period of different impairment level

        7) Add a 'Total Loss of Household Services' row at the bottom with the sum

        8) After the table, include a paragraph citing legal precedent for household services damages in your jurisdiction, formatted as:
           'Injured plaintiffs can recover damages for loss of household services in [STATE], which encompass tasks such as [examples of household tasks]. See [doc-id:page]; [doc-id:page]; [doc-id:page].'

        9) Include a footnote after the reference to the Bureau of Labor Statistics with this exact format:
           '⁷ U.S. DEPT. OF LABOR., Div. of Occupational Emp't & Wage Statistics, Rep. on Occupational Emp't & Wages (May 2024) (accessed May 2024). BLS.gov cannot vouch for the data or analyses derived from these data after the data have been retrieved from BLS.gov.'

        Format the table with clear borders, proper alignment (left-align text, right-align numbers), and consistent spacing. The calculations should be mathematically correct, with the Net Loss being Hourly Rate × Hours Per Day × Days in Period × % Impaired. The formatting should exactly match the sample demand letter's household services section. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'pain_and_suffering',
    title: 'Past and Future Pain and Suffering',
    contextNeeded: [
      'PLAINTIFF_INFO',
      'MEDICAL_CHRONOLOGY',
      'NON_ECONOMIC_DAMAGES',
      'CASE_EVALUATION'
    ],
    prompt: `Generate the 'Past and Future Pain and Suffering' section with these specific elements and formatting:

        1) Begin with the heading '3.6. Past and Future Pain and Suffering' in bold, followed by a line break

        2) Write an opening paragraph that states:
           'The [INCIDENT DATE] [incident type] resulted in Mr./Ms. [LAST NAME] sustaining significant noneconomic damages, including considerable physical and emotional pain and suffering, for which he/she is entitled to compensation.'
        
        3) Include a comprehensive paragraph (6-8 lines) explaining the legal basis for pain and suffering damages in your jurisdiction, citing specific state law and case precedent. Use this format:
           '[STATE] law allows for an injured plaintiff to recover noneconomic damages, which includes physical pain and suffering, as a result personal injuries tortiously and proximately caused by the wrongful acts/omissions of a defendant. [doc-id:page].'

        4) Add a paragraph explaining how future pain and suffering is calculated, with relevant legal citations
        
        5) Include the statutory definition of noneconomic damages from your state law, formatted as:
           'The term "noneconomic damages" means and includes, for example, "[QUOTE DIRECTLY FROM STATUTE]" [STATUTE CITATION].'

        6) Add a paragraph explaining who determines noneconomic damages (e.g., 'The determination of an injured plaintiff's noneconomic damages, including past and future pain and suffering, is decidedly left to the sound discretion of the trier of fact.') with relevant case citations

        7) Include a paragraph about per multiplier calculation methods being accepted in your jurisdiction, with case citations

        8) Include a quote of the standard jury instruction for noneconomic damages in your jurisdiction, formatted in a text box or indented

        9) Write 2-3 detailed paragraphs describing the plaintiff's specific pain and suffering, including:
           - Physical pain experienced (specific locations, intensity, frequency)
           - Medication required for pain management
           - Impact on daily activities and independence
           - Emotional distress, anxiety, or depression resulting from injuries
           - Specific activities the plaintiff can no longer enjoy or must modify
           - Comparison of quality of life before and after the incident
           - Impact on family relationships and social life
           - Sleep disturbances or other secondary effects
           - If applicable, include a compelling photograph that illustrates the plaintiff's pre-injury lifestyle

        10) Create a 'Verdict Analysis' subsection with:
            - A heading 'Verdict Analysis' in bold
            - A statement: 'Mr./Ms. [LAST NAME] is entitled to pain and suffering damages, as supported by the following verdict rendered under similar circumstances:'
            - A table with these rows:
              * Verdict Citation (case name and citation)
              * Jurisdiction (county and state)
              * Award (dollar amount awarded)
              * Case Facts (2-3 sentence summary of the case)
              * Injuries (bullet points of similar injuries)
            - A concluding paragraph comparing the cited case to the plaintiff's situation

        11) Create a 'Noneconomic Damages - Pain and Suffering' subsection with:
            - A statement of the total value: 'As illustrated in the below multiplier method analysis, Mr./Ms. [LAST NAME]'s claim for pain and suffering is conservatively valued at $[XXX,XXX.XX].'
            - List the economic damages used as the base for calculation (e.g., '● Total Medical Expenses: $[XX,XXX.XX]', '● Lost Wages: $[XX,XXX.XX]')
            - A detailed calculation table titled 'Pain & Suffering' with these sections:
              * Pain & Suffering (with rows for Total Economic Damages, Applied Multiplier(between 1 to 5 based on severity, duration of suffering, medical documentation, and lasting limitations), Total Pain & Suffering Value)
            - A concluding statement: 'In light of the multiplier calculation and representative verdict analysis above, Mr./Ms. [LAST NAME] is entitled to $[XXX,XXX.XX] as compensation for his/her pain and suffering.'

        Format all tables with clear borders, proper alignment, and consistent spacing. Use appropriate legal terminology throughout. The explanation of the plaintiff's specific pain and suffering should be detailed and compelling without being overdramatic. All calculations should be mathematically correct. Follow the exact formatting seen in the sample demand letter's pain and suffering section. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'punitive_damages',
    title: 'Punitive Damages',
    contextNeeded: [
      'INCIDENT_DETAILS',
      'PUNITIVE_DAMAGES',
      'LIABILITY_ASSESSMENT'
    ],
    prompt: `Generate the 'Punitive Damages' section with these specific elements and formatting:

        1) Begin with the heading '3.7. Punitive Damages' in bold, followed by a line break

        2) Start with a direct quote of your state's punitive damages statute, formatted as:
           '"A defendant may be held liable for punitive damages only if the trier of fact, based on clear and convincing evidence, finds that the defendant was personally guilty of intentional misconduct or gross negligence." [STATE] Stat. Ann. § [NUMBER].'

        3) Provide a specific example of when punitive damages are warranted in your jurisdiction, with a direct quote from a relevant case, formatted as:
           'For example, "[DIRECT QUOTE FROM CASE LAW ABOUT WHEN PUNITIVE DAMAGES ARE APPROPRIATE]" [doc-id:page].'

        4) Include the definition of 'culpable negligence' or similar standard from your jurisdiction's case law, formatted as:
           'The term "culpable negligence" has been defined as a basis for punitive damages by the [STATE] Supreme Court: "[DIRECT QUOTE FROM CASE LAW DEFINING THE STANDARD]" [doc-id:page].'

        5) Include a quote of the standard jury instruction for punitive damages in your jurisdiction, formatted in a text box or indented block quote with intro text:
           '[STATE] courts may instruct a jury, in pertinent part, as follows:'

        6) Write a paragraph applying these legal standards to the defendant's conduct, including:
           - Specific date of the incident
           - Specific egregious conduct (e.g., intoxication, excessive speed, texting while driving)
           - Language describing the conduct as 'reckless,' 'willful,' 'wanton,' etc.
           - Statement that the conduct showed 'utter disregard for the safety and well-being' of the plaintiff

        7) Conclude with a statement of the punitive damages amount requested:
           'Therefore, punitive damages in the amount of $[XXX,XXX.XX] represent what we believe to be a reasonable minimum threshold for what a similarly situated jury would award Mr./Ms. [LAST NAME] today on the issue of punitive damages alone.'

        The tone should be authoritative and firmly grounded in legal precedent. Each legal standard must be supported by specific statutory or case law citations. Only include this section if the facts warrant punitive damages (e.g., drunk driving, texting while driving, excessive speeding, or other particularly egregious conduct). Format exactly as shown in the sample demand letter's punitive damages section. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'settlement_demand',
    title: 'Demand to Settle',
    contextNeeded: [
      'CASE_EVALUATION',
      'DAMAGES_CALCULATION',
      'ATTORNEY_STRATEGY_PLAN'
    ],
    prompt: `Create the 'Demand to Settle' section with these specific elements and formatting:
        
        1) Begin with the heading '4. DEMAND TO SETTLE' in bold, followed by a line break

        2) Write a clear opening paragraph that includes:
           - The plaintiff's full name
           - The specific settlement amount demanded (typically policy limits)
           - A statement that this represents applicable policy limits
           - Use this exact format: 'Mr./Ms. [LAST NAME] offers to settle his/her claims against your insured for $[XXX,XXX.XX], which represents the applicable policy limits providing coverage for the [INCIDENT DATE] [incident type].'

        3) Include a paragraph setting the deadline for response:
           'This settlement demand will remain open until 5:00 P.M. [TIME ZONE] on the 30th day after receipt, after which it will be automatically withdrawn if not accepted.'

        4) Write a paragraph about the insurer's duty to act in good faith:
           'Your company has a legal duty to act in good faith to resolve this matter. Under [STATE] law, an insurer's statutory obligation to settle claims in good faith requires the insurer to timely evaluate and pay benefits owed on the insurance policy. [doc-id:page].'

        5) Add a paragraph about potential bad faith consequences with specific statutory references:
           'As a consequence of an insurer's failure to act in good faith, and in the third-party context, [STATE] Stat. Ann. § [NUMBER] "[DIRECT QUOTE FROM STATUTE OR CASE EXPLAINING BAD FAITH CONSEQUENCES]" [doc-id:page].'

        6) Include a reservation of rights paragraph:
           'While this correspondence is subject to [STATE] Stat. § [RELEVANT STATUTE], nothing contained herein shall be construed to limit or impair, in any way, any of our client's claims, rights, remedies, or defenses in this matter, all of which are hereby expressly reserved.'

        7) Close with a professional conclusion:
           'Please do not hesitate to contact me if you have any additional questions or concerns. Thank you for your anticipated cooperation.'

        8) End with:
           'Sincerely,'
           '[ATTORNEY NAME], Esq.'

        The tone should be firm but professional. The demand should be clear and unambiguous. All statutory references should be accurate for your jurisdiction. Format exactly as shown in the sample demand letter's settlement demand section. If the ATTORNEY_STRATEGY_PLAN indicates specific settlement strategy considerations, incorporate them appropriately while maintaining this structure. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  },
  {
    id: 'exhibit_list',
    title: 'Exhibit List',
    contextNeeded: ['MEDICAL_CHRONOLOGY', 'EXTRACTED_FINANCIAL_DATA'],
    prompt: `Create a comprehensive 'Exhibit List' with these specific elements and formatting:

        1) Create a new page with the same header as other pages (insurance company name, date, page number)

        2) Title the page 'Exhibit List' in bold
        
        3) Create a table with two columns:
           - Exhibit No. (left column)
           - Description (right column)

        4) Number exhibits sequentially starting with 'Exhibit 1'

        5) Include these specific categories of exhibits in this exact order:
           a) Official reports and documentation:
              - Florida Traffic Crash Report (or equivalent official report)
              - Incident Scene Photographs
        
           b) Medical records for each provider, in chronological order by first date of service:
              - Format as '[Provider Name] - Records - [doc-id:page]'
              - Include every medical provider mentioned in the MEDICAL_CHRONOLOGY
        
           c) Medical bills for each provider, in the same order as the records:
              - Format as '[Provider Name] - Bills - [doc-id:page]'
              - Include a separate line for each provider
        
           d) Additional documentation:
              - Loss of Income Documentation and Economic Loss Report
              - Any other supporting documentation mentioned in the demand letter
        
        6) Format the table with:
           - Clear borders around all cells
           - Column headers in bold
           - Consistent cell padding
           - Left-aligned text in both columns
           - Sequential numbering in the first column
           - Professionally formatted descriptions in the second column
        
        7) Ensure every document referenced in the demand letter appears in this list
        
        8) The exhibit numbers should match any footnote references throughout the document
        
        This page serves as a comprehensive index of all supporting documentation. It should be professionally formatted and match exactly the format seen in the sample demand letter's exhibit list page. The numbering system should be consistent throughout the entire demand package. All citations or exibits must be provided as [doc-id:page] at the line, example: [123:2].`
  }
]
