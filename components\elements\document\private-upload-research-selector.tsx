'use client'

import { use<PERSON>tom } from 'jotai'
import { useState } from 'react'
import { selectedDocumentsAtom } from '@/lib/hooks/atom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import CreateDatasetDialog from '../dataset-create-dialog'
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '../../ui/tabs'

export function PrivateDocumentSelector({
  allDocuments,
  groupedDocuments
}: {
  allDocuments: {
    id: string
    title: string
  }[]
  groupedDocuments: {
    id: number
    name: string
    DocumentRecords: {
      id: number
      title: string
    }[]
  }[]
}) {
  const [selectedDocuments, setSelectedDocuments] = useAtom(
    selectedDocumentsAtom
  )
  const [searchQuery, setSearchQuery] = useState('')
  const [visibleCount, setVisibleCount] = useState(10)

  const isDocumentSelected = (docId: string | number): boolean =>
    selectedDocuments.includes(docId.toString())

  const handleCheckboxChange = (docId: string | number): void => {
    const updatedSelectedDocs = isDocumentSelected(docId)
      ? selectedDocuments.filter((id) => id !== docId)
      : [...selectedDocuments, docId.toString()]

    setSelectedDocuments(updatedSelectedDocs)
  }

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value)
  }

  // Filter documents based on search query
  const filteredDocuments = allDocuments.filter((document) =>
    document.title.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Only show the first `visibleCount` number of documents
  const visibleDocuments = filteredDocuments.slice(0, visibleCount)
  const visibleDatasets = groupedDocuments.slice(0, visibleCount)

  const handleShowMore = () => {
    setVisibleCount((prevCount) => prevCount + 10)
  }

  const handleShowAll = () => {
    setVisibleCount(filteredDocuments.length)
  }

  // Handle dataset selection (select all documents within a dataset)
  const handleDatasetSelect = (datasetDocs: { id: number }[]) => {
    const allDocsSelected = datasetDocs.every((doc) =>
      isDocumentSelected(doc.id)
    )

    if (allDocsSelected) {
      const newSelection = selectedDocuments.filter(
        (id) => !datasetDocs.some((doc) => doc.id.toString() === id)
      )
      setSelectedDocuments(newSelection)
    } else {
      const newSelection = [
        ...selectedDocuments,
        ...datasetDocs
          .filter((doc) => !isDocumentSelected(doc.id))
          .map((doc) => doc.id.toString())
      ]
      setSelectedDocuments(newSelection)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Select Uploaded Documents</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Search input */}
        <Input
          type="text"
          value={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search documents..."
          className="mb-4" // Add spacing between search input and the checkboxes
        />
        <Tabs defaultValue="ungrouped-docs">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="ungrouped-docs" className="text-center">
              Documents
            </TabsTrigger>
            <TabsTrigger
              value="grouped-docs"
              className="text-center"
              disabled={groupedDocuments.length === 0}
            >
              Datasets
              {groupedDocuments.length === 0 && (
                <span className="text-xs ml-1"> (None)</span>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="ungrouped-docs"
            className="max-h-[50vh] overflow-y-auto p-2"
          >
            <div className="grid gap-5 grid-cols-1 md:grid-cols-2 h-[20vh] items-start overflow-y-auto">
              {visibleDocuments.map((document) => (
                <li
                  key={document.id}
                  className="flex items-center space-x-2 my-1.5"
                >
                  <Checkbox
                    id={document.id}
                    checked={isDocumentSelected(document.id)}
                    onCheckedChange={() => handleCheckboxChange(document.id)}
                  />
                  <label htmlFor={document.id} className="text-sm font-medium">
                    {document.title}
                  </label>
                </li>
              ))}
            </div>

            {selectedDocuments.length > 1 && (
              <CreateDatasetDialog
                selectedDocuments={selectedDocuments.map((id) => parseInt(id))}
              />
            )}

            <div className="mt-4 space-y-2 space-x-2">
              {visibleDocuments.length === 0 && (
                <p className="text-sm text-gray-500">No documents found.</p>
              )}
              {visibleCount < filteredDocuments.length && (
                <Button
                  onClick={handleShowMore}
                  size="sm"
                  className="rounded-full"
                >
                  Show More
                </Button>
              )}
              {visibleCount < filteredDocuments.length && (
                <Button
                  onClick={handleShowAll}
                  size="sm"
                  className="rounded-full"
                >
                  Show All
                </Button>
              )}
            </div>
          </TabsContent>

          <TabsContent
            value="grouped-docs"
            className="max-h-[50vh] overflow-y-auto p-2"
          >
            <div className="grid gap-5 grid-cols-1 md:grid-cols-2 h-[20vh] items-start">
              {visibleDatasets.map((dataset) => (
                <div key={dataset.id}>
                  <div className="flex items-center space-x-2 pt-3">
                    <Checkbox
                      id={dataset.id.toString()}
                      checked={
                        dataset.DocumentRecords.length > 0 &&
                        dataset.DocumentRecords.every((doc) =>
                          isDocumentSelected(doc.id)
                        )
                      }
                      onCheckedChange={() =>
                        handleDatasetSelect(dataset.DocumentRecords)
                      }
                    />
                    <h4 className="font-semibold">{dataset.name}</h4>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
