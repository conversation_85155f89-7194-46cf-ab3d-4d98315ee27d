'use client'

import { Card } from '@/components/ui/card'
import { ChatWindow } from '@/components/elements/chat/chat-window'
import { ResearchStore, ResearchType } from '@prisma/client'
import { useEffect, useState } from 'react'
import { Session } from 'next-auth'
import { ResearchStoreContent } from '@/types'
import { fetchResearchProps } from '@/lib/actions/research'
import { Skeleton } from '@/components/ui/skeleton'

export function BinderResearch({
  researchId,
  researchType
}: {
  researchId: string
  researchType: ResearchType
}) {
  const [data, setData] = useState<{
    user: Session['user']
    researchProps: ResearchStoreContent
    stats?: {
      available: number
      used: number
    }
    research: ResearchStore
  } | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchData() {
      setLoading(true)
      try {
        const researchData = await fetchResearchProps({ researchId })
        if (researchData) {
          setData(researchData)
        }
      } catch (error) {
        console.error(error)
      }
      setLoading(false)
    }
    fetchData()
  }, [researchId])

  return (
    <Card>
      {loading && (
        <div className="space-y-4">
          <div className="flex items-start space-x-2">
            <div className="flex flex-col space-y-1">
              <Skeleton className="h-4 w-3/4 rounded-md" />
              <Skeleton className="h-4 w-1/2 rounded-md" />
            </div>
          </div>

          <div className="flex items-start space-x-2 justify-end">
            <div className="flex flex-col items-end space-y-1">
              <Skeleton className="h-4 w-2/3 rounded-md" />
              <Skeleton className="h-4 w-1/3 rounded-md" />
            </div>
          </div>

          <div className="flex items-start space-x-2">
            <div className="flex flex-col space-y-1">
              <Skeleton className="h-4 w-3/4 rounded-md" />
              <Skeleton className="h-4 w-1/2 rounded-md" />
            </div>
          </div>
        </div>
      )}

      {!loading &&
        (data ? (
          <ChatWindow
            user={data.user}
            stats={data.stats}
            researchType={researchType}
            researchProps={data.researchProps}
            showIngestForm={true}
            placeholder="Ask a question..."
            showFilters={researchType === ResearchType.private ? false : true}
            namespace={data.research?.source}
          />
        ) : (
          <p>Failed to load research data</p>
        ))}
    </Card>
  )
}
