'use client'

import { useState } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { DataTableColumnHeader } from '../data-table/data-table-column-header'
import { SimpleDataTable } from './simple-data-table'
import { Prompt } from '@prisma/client'
import { EditPromptDialog } from './edit-prompt-modal'
import { Button } from '@/components/ui/button'

export function PromptList({ prompts }: { prompts: Prompt[] }) {
  // Manage the currently-editing prompt at the component level
  const [editingPromptId, setEditingPromptId] = useState<string | null>(null)

  // Create columns with access to the state setter
  const columns = getPromptListingColumns(setEditingPromptId)

  // Find the currently editing prompt
  const editingPrompt = prompts.find((prompt) => prompt.id === editingPromptId)

  return (
    <>
      <SimpleDataTable columns={columns} data={prompts} />

      {/* Single dialog instance outside the table */}
      {editingPrompt && (
        <EditPromptDialog
          id={editingPrompt.id}
          prompt={editingPrompt}
          open={editingPromptId !== null}
          setOpen={(open) => {
            if (!open) setEditingPromptId(null)
          }}
        />
      )}
    </>
  )
}

// Create a function that returns the columns with access to the state setter
export const getPromptListingColumns = (
  setEditingPromptId: (id: string | null) => void
): ColumnDef<any>[] => [
  {
    accessorKey: 'source',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Source" />
    ),
    cell: ({ row }) => (
      <div>
        <div className="font-semibold text-amber-600">
          {row.original.role} PROMPT
        </div>
        <div className="font-medium text-xs">{row.getValue('source')}</div>
      </div>
    )
  },
  {
    accessorKey: 'prompt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Prompt" />
    ),
    cell: ({ row }) => {
      const promptText = row.getValue('prompt') as string
      return (
        <div>
          <div className="font-semibold text-amber-600">Base Prompt:</div>
          <div className="font-mono text-sm p-2 rounded border">
            {`${promptText.substring(0, 400)}...`}
          </div>
        </div>
      )
    }
  },
  {
    id: 'actions',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Actions" />
    ),
    cell: ({ row }) => {
      return (
        <Button
          variant="outline"
          className="mr-2"
          onClick={() => {
            // Set the ID of the prompt being edited
            setEditingPromptId(row.original.id)
          }}
        >
          Edit
        </Button>
      )
    }
  }
]
