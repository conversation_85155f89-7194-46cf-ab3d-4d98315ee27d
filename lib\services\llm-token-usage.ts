import { db } from '@/lib/db'
import type { CompletionUsage } from 'openai/resources/completions'
import type { GenerateContentResponseUsageMetadata } from '@google/genai'
import { LLMProvider } from '@prisma/client'

// Use the official types from both libraries
export type OpenAIUsage = CompletionUsage
export type GeminiUsageMetadata = GenerateContentResponseUsageMetadata

export interface LLMTokenUsageInput {
  teamId: string
  provider: LLMProvider
  model: string
  purpose: string
  activity: string
  requestId?: string
  modelVersion?: string
  finishReason?: string
  usage: OpenAIUsage | GeminiUsageMetadata
}

export interface ParsedTokenUsage {
  promptTokens?: number
  completionTokens?: number
  totalTokens?: number
  cachedTokens?: number
  audioTokens?: number
  reasoningTokens?: number
  candidatesTokenCount?: number
  cachedContentTokens?: number
  toolUsePromptTokens?: number
  thoughtsTokenCount?: number
  rawUsageData: any
}

export function parseTokenUsage(
  provider: LLMProvider,
  usage: OpenAIUsage | GeminiUsageMetadata
): ParsedTokenUsage {
  const result: ParsedTokenUsage = {
    rawUsageData: usage
  }

  switch (provider) {
    case LLMProvider.OPENAI:
      const openaiUsage = usage as OpenAIUsage
      result.promptTokens = openaiUsage.prompt_tokens
      result.completionTokens = openaiUsage.completion_tokens
      result.totalTokens = openaiUsage.total_tokens
      result.cachedTokens = openaiUsage.prompt_tokens_details?.cached_tokens
      result.audioTokens =
        openaiUsage.prompt_tokens_details?.audio_tokens ||
        openaiUsage.completion_tokens_details?.audio_tokens
      result.reasoningTokens =
        openaiUsage.completion_tokens_details?.reasoning_tokens
      break

    case LLMProvider.GEMINI:
      const geminiUsage = usage as GeminiUsageMetadata
      result.promptTokens = geminiUsage.promptTokenCount || 0
      result.completionTokens = geminiUsage.candidatesTokenCount || 0
      result.totalTokens = geminiUsage.totalTokenCount || 0
      result.candidatesTokenCount = geminiUsage.candidatesTokenCount
      result.cachedContentTokens = geminiUsage.cachedContentTokenCount
      result.toolUsePromptTokens = geminiUsage.toolUsePromptTokenCount
      result.thoughtsTokenCount = geminiUsage.thoughtsTokenCount
      break

    default:
      // For other providers, use any type to access unknown properties
      const unknownUsage = usage as any
      result.totalTokens = unknownUsage.totalTokens || unknownUsage.total_tokens
      result.promptTokens =
        unknownUsage.promptTokens || unknownUsage.prompt_tokens
      result.completionTokens =
        unknownUsage.completionTokens || unknownUsage.completion_tokens
      break
  }

  return result
}

export async function trackTokenUsage(
  input: LLMTokenUsageInput
): Promise<void> {
  try {
    const parsedUsage = parseTokenUsage(input.provider, input.usage)

    await db.lLMTokenUsage.create({
      data: {
        teamId: input.teamId,
        provider: input.provider,
        model: input.model,
        purpose: input.purpose,
        activity: input.activity,
        requestId: input.requestId,
        modelVersion: input.modelVersion,
        finishReason: input.finishReason,
        ...parsedUsage
      }
    })
  } catch (error) {
    console.error('Failed to track token usage:', error)
    // Don't throw error to avoid breaking the main flow
  }
}

export async function getTeamTokenUsage(
  teamId: string,
  options?: {
    provider?: LLMProvider
    purpose?: string
    activity?: string
    startDate?: Date
    endDate?: Date
  }
) {
  const where: any = { teamId }

  if (options?.provider) where.provider = options.provider
  if (options?.purpose) where.purpose = options.purpose
  if (options?.activity) where.activity = options.activity
  if (options?.startDate || options?.endDate) {
    where.createdAt = {}
    if (options.startDate) where.createdAt.gte = options.startDate
    if (options.endDate) where.createdAt.lte = options.endDate
  }

  const usage = await db.lLMTokenUsage.findMany({
    where,
    orderBy: { createdAt: 'desc' }
  })

  // Aggregate the results
  const summary = usage.reduce(
    (acc, record) => {
      const provider = record.provider
      if (!acc[provider]) {
        acc[provider] = {
          totalTokens: 0,
          promptTokens: 0,
          completionTokens: 0,
          requests: 0
        }
      }

      acc[provider].totalTokens += record.totalTokens || 0
      acc[provider].promptTokens += record.promptTokens || 0
      acc[provider].completionTokens += record.completionTokens || 0
      acc[provider].requests += 1

      return acc
    },
    {} as Record<string, any>
  )

  return {
    records: usage,
    summary
  }
}
