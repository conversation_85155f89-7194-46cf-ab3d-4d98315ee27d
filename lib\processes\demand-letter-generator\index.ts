// lib/processes/demand-letter-generator.ts

import { db } from '@/lib/db'
import { demandLetterPrompts } from './demand-letter-prompts'
import { CaseFileType } from '@prisma/client'
import { generateSectionsInBatches, logger } from '../utils-llm'
import { ContextData, DemandLetter } from '@/types/case'

// Maximum number of continuations per section
const MAX_CONTINUATIONS = 3

/**
 * Main function to generate a complete demand letter
 * @param caseId The case ID to fetch context for
 * @returns The complete demand letter
 */
export async function generateDemandLetter(
  caseId: string
): Promise<DemandLetter> {
  logger.start('generateDemandLetter', { caseId })
  const startTime = Date.now()
  let tokenCount = 0

  try {
    // Step 1: Fetch all necessary context data
    const contextData = await fetchContextData(caseId)

    // Define sections that are likely to need extension due to complexity
    const sectionsNeedingExtension = [
      'facts_and_liability',
      'injuries_and_treatments',
      'pain_and_suffering',
      'punitive_damages'
    ]

    // Fetch all documents related to the case
    const caseDocuments = await db.dataset.findMany({
      where: {
        OR: [
          {
            createdBy: caseId
          },
          {
            binderId: caseId
          }
        ]
      },
      select: {
        DocumentRecordDatasetMap: {
          select: {
            DocumentRecords: {
              select: {
                id: true,
                title: true,
                indexed: true
              }
            }
          }
        }
      }
    })

    const documents = caseDocuments
      .map((dataSet) =>
        dataSet.DocumentRecordDatasetMap.map((doc) => {
          return {
            id: doc.DocumentRecords.id,
            title: doc.DocumentRecords.title,
            indexed: doc.DocumentRecords.indexed
          }
        })
      )
      .flat()

    // Step 2: Generate all sections in parallel batches
    const sectionContents = await generateSectionsInBatches({
      contextData,
      prompts: demandLetterPrompts,
      maxContinuations: MAX_CONTINUATIONS,
      sectionsNeedingExtension,
      documentList: documents
    })

    // Step 3: Format sections into structured objects
    const sections = demandLetterPrompts
      .filter((section) => sectionContents[section.id])
      .map((section) => ({
        id: section.id,
        title: section.title,
        content: sectionContents[section.id]
      }))

    // Step 4: Combine all sections into a final demand letter
    const combinedContent = combineAllSections(sectionContents)

    // Create the final demand letter object
    const demandLetter: DemandLetter = {
      caseId,
      sections,
      combinedContent,
      metadata: {
        generatedAt: new Date().toISOString(),
        tokenCount,
        generationTimeMs: Date.now() - startTime
      }
    }

    logger.end('generateDemandLetter', {
      sectionCount: sections.length,
      totalLength: combinedContent.length,
      generationTimeMs: demandLetter.metadata.generationTimeMs
    })

    return demandLetter
  } catch (error: any) {
    logger.error('generateDemandLetter', error)
    throw new Error(`Failed to generate demand letter: ${error.message}`)
  }
}

/**
 * Fetch all context data needed for generating the demand letter
 * @param caseId The case ID to fetch context for
 * @returns Object containing all context data
 */
async function fetchContextData(caseId: string): Promise<ContextData> {
  logger.start('fetchContextData', { caseId })

  try {
    // Define all the context types we need to fetch
    const contextTypes = [
      CaseFileType.PLAINTIFF_INFO,
      CaseFileType.MEDICAL_CHRONOLOGY,
      CaseFileType.INCIDENT_DETAILS,
      CaseFileType.CASE_EVALUATION,
      CaseFileType.LIABILITY_ASSESSMENT,
      CaseFileType.ECONOMIC_DAMAGES,
      CaseFileType.NON_ECONOMIC_DAMAGES,
      CaseFileType.PUNITIVE_DAMAGES,
      CaseFileType.DAMAGES_CALCULATION,
      CaseFileType.EXTRACTED_FINANCIAL_DATA
    ]

    // Create an object to store all context data
    const contextData: ContextData = {}

    const data = await db.caseFile.findMany({
      where: {
        binderId: caseId,
        fileType: {
          in: contextTypes
        }
      }
    })

    // Iterate over the fetched data and populate contextData
    for (const item of data) {
      const { fileType, content } = item
      if (fileType && content) {
        contextData[fileType] = content
      }
    }

    logger.end('fetchContextData', {
      contextTypes: Object.keys(contextData).length,
      availableTypes: Object.keys(contextData).join(', ')
    })

    return contextData
  } catch (error: any) {
    logger.error('fetchContextData', error)
    throw new Error(`Failed to fetch context data: ${error.message}`)
  }
}

/**
 * Combine all sections into a complete demand letter
 * @param sections Object mapping section IDs to their content
 * @returns The complete demand letter text
 */
function combineAllSections(sections: Record<string, string>): string {
  // Define the order of sections in the final document
  const sectionOrder = [
    'cover_letter',
    'facts_and_liability',
    'injuries_and_treatments',
    'damages_overview',
    'past_medical_expenses',
    'future_medical_expenses',
    'loss_of_income',
    'loss_of_household_services',
    'pain_and_suffering',
    'punitive_damages',
    'settlement_demand',
    'exhibit_list'
  ]

  // Combine sections in the defined order
  return sectionOrder
    .filter((id) => sections[id]) // Only include sections that were generated
    .map((id) => {
      const sectionInfo = demandLetterPrompts.find((s) => s.id === id)
      const content = sections[id]

      // If the content doesn't already include the section title, add it
      if (sectionInfo && !content.includes(sectionInfo.title)) {
        return `# ${sectionInfo.title}\n\n${content}`
      }

      return content
    })
    .join('\n\n')
}
