'use client'

import { useRouter, usePathname } from 'next/navigation'
import { storeResearchContent } from '@/lib/actions/research'
import { Session } from 'next-auth'
import { ResearchType } from '@prisma/client'
import { SmartCouncelChatMessage } from '@/types'

export function useStoreResearch({
  user,
  researchId,
  court,
  year,
  model,
  sources,
  sourcesForMessages,
  researchType,
  sourceLabels,
  namespace,
  masquerade = false,
  binderId
}: {
  user: Session['user']
  researchId?: string
  court: string[]
  year: string[]
  model: string
  sources: string[]
  sourcesForMessages: Record<string, any>
  researchType: ResearchType
  sourceLabels?: {
    id: string
    title: string
  }[]
  namespace?: string
  masquerade?: boolean
  binderId?: string
}) {
  const router = useRouter()
  const currentPath = usePathname()

  async function storeResearch(messages: SmartCouncelChatMessage[]) {
    const store = await storeResearchContent({
      user,
      content: {
        researchId,
        court,
        year,
        model,
        sources,
        sourcesForMessages,
        sourceLabels,
        messages
      },
      namespace: namespace,
      masquerade: masquerade || false,
      researchType: researchType,
      binderId
    })

    if (store?.type === 'create' && currentPath?.includes('/dashboard/case')) {
      switch (researchType) {
        case ResearchType.law:
          router.push(`/dashboard/research/${store.data.id}`)
          break

        case ResearchType.case:
          router.push(`/dashboard/research-case/${store.data.id}`)
          break

        case ResearchType.private:
          router.push(`/dashboard/research-private/${store.data.id}`)
          break
      }
    } else if (store?.type === 'create') {
      switch (currentPath) {
        case '/dashboard/research':
          router.push(`/dashboard/research/${store.data.id}`)
          break
        case '/dashboard/research-case':
          router.push(`/dashboard/research-case/${store.data.id}`)
          break
        case '/dashboard/research-gst':
          router.push(`/dashboard/research-gst/${store.data.id}`)
          break
        case '/dashboard/research-criminal-law':
          router.push(`/dashboard/research-criminal-law/${store.data.id}`)
          break
        case '/dashboard/research-private':
          router.push(`/dashboard/research-private/${store.data.id}`)
          break
        default:
          router.refresh()
      }
    } else {
      router.refresh()
    }
  }

  return { storeResearch }
}
