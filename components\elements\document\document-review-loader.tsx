'use client'

import { EmptyPlaceholder } from '../custom-components/empty-placeholder'
import { Progress } from '../../ui/progress'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

export function ReviewDisplayCardCollectionLoader({
  documentId,
  progress
}: {
  documentId: string
  progress: number
}) {
  const router = useRouter()
  const [timeStamp, setTimeStamp] = useState<number>(new Date().getTime())

  useEffect(() => {
    async function getTeamDocumentChunkReviews(documentId: string) {
      try {
        const response = await fetch(`/api/gpt/review/${documentId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: '{}'
        })
        if (response.ok) {
          const data = await response.json()
          if (!data.completion) {
            setTimeStamp(new Date().getTime())
            router.refresh()
          }
        }
      } catch (error) {
        console.error(error)
      }
    }

    getTeamDocumentChunkReviews(documentId)
  }, [timeStamp, documentId, router])

  return (
    <EmptyPlaceholder>
      <EmptyPlaceholder.Title>
        Document is being reviewed
      </EmptyPlaceholder.Title>
      <EmptyPlaceholder.Description>
        Stay on this page while we review your document.
      </EmptyPlaceholder.Description>
      <Progress value={progress} />
    </EmptyPlaceholder>
  )
}
