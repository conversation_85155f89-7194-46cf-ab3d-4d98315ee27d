import { notFound, redirect } from 'next/navigation'

import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { getCurrentUser } from '@/lib/session'
import Head from 'next/head'
import '@/styles/document-viewer.css'
import { CaseData } from '@/types/document'

interface EditorPageProps {
  params: { documentId: string }
}

export default async function EditorPage({ params }: EditorPageProps) {
  const user = await getCurrentUser()

  if (!user) {
    redirect(authOptions?.pages?.signIn || '/login')
  }

  const document = await db.documentRecords.findFirst({
    where: {
      id: parseInt(params.documentId)
    }
  })

  if (!document) {
    return notFound()
  }

  const isTeamDoc = await db.team.findUnique({
    where: {
      id: document.source
    }
  })

  if (isTeamDoc && user.teamId !== document.source) {
    return notFound()
  }

  const metadata = document.meta
    ? (JSON.parse(document.meta) as CaseData)
    : null

  let htmlbody = document.html
  if (htmlbody.split('<body class="mid1">')[1]) {
    htmlbody = htmlbody.split('<body class="mid1">')[1].split('</body>')[0]
  }

  htmlbody = htmlbody.replace(/class="pdf-iframe"/g, 'class="hidden"')
  htmlbody = htmlbody.replace(/class="disclaimer"/g, 'class="hidden"')

  return (
    <>
      <Head>
        <title>{document.title}</title>
        <meta
          name="description"
          content={`Case of ${document.title} dated ${document.date}`}
        />
        <meta
          property="og:description"
          content={`Case of ${document.title} dated ${document.date}`}
        />
        {/* <meta
          property="og:image"
          content=""
        />
        <meta content="1200" property="og:image:width" />
        <meta content="630" property="og:image:height" /> */}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content="noindex,nofollow" />
      </Head>
      <div dangerouslySetInnerHTML={{ __html: htmlbody }} />
    </>
  )
}
