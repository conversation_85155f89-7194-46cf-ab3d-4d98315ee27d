'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Copy, FileDown, Loader2 } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { marked } from 'marked'
import { useCallback } from 'react'
import { parseMarkdown, prepareMarkdown, processCitations } from '@/lib/utils'
import { DocumentTitle } from '@/types/case'

// Copy Button Component
export function CopyButton({ content }: { content: string | null }) {
  const { toast } = useToast()
  const [isCopying, setIsCopying] = useState(false)

  const handleCopy = useCallback(async () => {
    if (!content) return

    setIsCopying(true)
    try {
      // Convert markdown to HTML to preserve formatting
      const html = await marked(content)

      // Create a blob with HTML content
      const blob = new Blob([html], { type: 'text/html' })

      // Create clipboard items
      const item = new ClipboardItem({
        'text/html': blob,
        'text/plain': new Blob([content], { type: 'text/plain' })
      })

      // Copy to clipboard
      await navigator.clipboard.write([item])

      toast({
        title: 'Copied to clipboard',
        description: 'Content copied with formatting preserved'
      })
    } catch (error) {
      console.error('Copy failed:', error)

      // Fallback to plain text copy
      try {
        await navigator.clipboard.writeText(content)
        toast({
          title: 'Copied as plain text',
          description: 'Formatting could not be preserved'
        })
      } catch (fallbackError) {
        toast({
          title: 'Copy failed',
          description: 'Could not copy content to clipboard',
          variant: 'destructive'
        })
      }
    } finally {
      setIsCopying(false)
    }
  }, [content, toast])

  return (
    <Button
      variant="outline"
      onClick={handleCopy}
      disabled={!content || isCopying}
      className="flex items-center gap-2"
    >
      {isCopying ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Copy className="h-4 w-4" />
      )}
      Copy Content
    </Button>
  )
}

// Download Button Component
export function DownloadButton({
  content,
  filename,
  documents = []
}: {
  content: string | null
  filename: string
  documents?: DocumentTitle[]
}) {
  const [isDownloading, setIsDownloading] = useState(false)

  const handleDownload = () => {
    if (!content) return

    // Process content with citations
    const { processedContent, citations } = processCitations(content, documents)

    // Add citations section at the bottom if there are any citations
    let finalContent = processedContent
    if (citations.length > 0) {
      finalContent += '\n\n---\n\n## References\n\n' + citations.join('\n\n')
    }

    const html = parseMarkdown(finalContent)
      // add text-3xl to h1, text-2xl to h2, text-xl to h3
      .replace(/<h1/g, '<h1 class="text-3xl font-bold my-3"')
      .replace(/<h2/g, '<h2 class="text-2xl font-bold my-3"')
      .replace(/<h3/g, '<h3 class="text-xl my-2"')
      .replace(/<h4/g, '<h4 class="text-lg my-2"')
    setIsDownloading(true)

    // Create a new window for printing
    const printWindow = window.open('', '_blank')

    // Add styling including page breaks and proper margins
    printWindow!.document.write(`
      <html>
        <head>
          <title>${filename}</title>
          <script src="https://cdn.tailwindcss.com"></script>
        </head>
        <body class="font-sans mx-16 my-8">
          ${html}
        </body>
      </html>
    `)

    printWindow!.document.close()

    printWindow!.onload = function () {
      printWindow!.focus()
      printWindow!.print()
      // setTimeout(() => printWindow!.close(), 500)
    }

    setIsDownloading(false)
  }

  return (
    <Button
      onClick={handleDownload}
      disabled={!content || isDownloading}
      className="flex items-center gap-2"
    >
      {isDownloading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <FileDown className="h-4 w-4" />
      )}
      Download as Word
    </Button>
  )
}
