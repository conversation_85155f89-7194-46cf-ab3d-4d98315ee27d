'use client'

import { DataTable } from './data-table/data-table'
import { ColumnDef } from '@tanstack/react-table'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTableColumnHeader } from './data-table/data-table-column-header'
import { Button } from '../ui/button'
import { toast } from '../ui/use-toast'
import { injestFileFromGDrive } from '@/lib/actions/google-drive'
import { useState } from 'react'
import { useRouter } from 'next/navigation'

export function PrivateGDriveTable({
  documents
}: {
  documents: {
    id: string
    title: string
  }[]
}) {
  return (
    <DataTable
      columns={documentListingColumns}
      data={documents}
      limitHeight={true}
      bulkAction={(selectedRows) => (
        <>
          <InjestGDriveFiles ids={selectedRows as string[]} />
        </>
      )}
    />
  )
}

const InjestGDriveFiles = ({ ids }: { ids: string[] }) => {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  async function handleClick() {
    setLoading(true)
    toast({
      title: 'Uploading',
      description: 'This may take a few minutes'
    })
    for (const id of ids) {
      try {
        await injestFileFromGDrive(id)
      } catch (error) {
        console.log(error)
        toast({
          title: 'Error',
          description: 'Error syncing file'
        })
      }
    }
    toast({
      title: 'Success',
      description: 'Files have been synced'
    })
    setLoading(false)
    router.refresh()
  }

  return (
    <Button variant="default" disabled={loading} onClick={handleClick}>
      Sync Selected Files
    </Button>
  )
}

export const documentListingColumns: ColumnDef<any>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value: any) =>
          table.toggleAllPageRowsSelected(!!value)
        }
        aria-label="Select all"
        className="translate-y-[2px] scale-110"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value: any) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-[2px] scale-110"
      />
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'title',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('title')}
          </span>
        </div>
      )
    }
  }
]
