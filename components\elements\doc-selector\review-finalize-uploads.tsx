'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { DocumentTitle } from '@/types/case'
import Link from 'next/link'

interface ReviewAndFinalizeUploadsProps {
  baseFiles?: Record<string, string[]>
  selectedDocumentsByType: Record<string, string[]>
  allDocuments: DocumentTitle[]
  setSelectedDocumentsByType: React.Dispatch<
    React.SetStateAction<Record<string, string[]>>
  >
}

export default function ReviewAndFinalizeUploads({
  baseFiles,
  selectedDocumentsByType,
  allDocuments,
  setSelectedDocumentsByType
}: ReviewAndFinalizeUploadsProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Reviewing &amp; Finalizing Uploads</CardTitle>
        <CardDescription>
          Below is a summary of all selected documents, categorized by type. You
          can review them and confirm the upload.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="overflow-x-auto rounded-lg border">
          <table className="min-w-full h-[40vh]">
            <thead>
              <tr className="border-b">
                <th className="px-4 py-2 text-left">Document Type</th>
                <th className="px-4 py-2 text-left">Uploaded File</th>
                <th className="px-4 py-2 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {baseFiles &&
                Object.entries(baseFiles).map(([type, docs]) => {
                  return docs.map((docLink, idx) => {
                    return (
                      <tr key={idx} className="border-b">
                        {idx === 0 ? (
                          <td
                            rowSpan={docs.length}
                            className="px-4 py-2 align-top font-medium"
                          >
                            {type}
                          </td>
                        ) : null}
                        <td className="px-4 py-2">
                          <Link
                            href={docLink}
                            passHref
                            prefetch={false}
                            replace={false}
                            className="hover:underline"
                          >
                            View {type}
                          </Link>
                        </td>
                        <td className="px-4 py-2 space-x-2"></td>
                      </tr>
                    )
                  })
                })}

              {Object.entries(selectedDocumentsByType).map(([type, docIds]) => {
                if (docIds.length === 0) {
                  return (
                    <tr key={type} className="border-b">
                      <td className="px-4 py-2 align-top font-medium">
                        {type}
                      </td>
                      <td
                        colSpan={3}
                        className="px-4 py-2 text-sm font-semibold text-red-400"
                      >
                        No documents selected!
                      </td>
                    </tr>
                  )
                }

                return docIds.map((docId, idx) => {
                  const doc = allDocuments.find(
                    (d) => String(d.id) === String(docId)
                  )
                  return (
                    <tr key={`${type}-${docId}`} className="border-b">
                      {idx === 0 ? (
                        <td
                          rowSpan={docIds.length}
                          className="px-4 py-2 align-top font-medium"
                        >
                          {type}
                        </td>
                      ) : null}
                      <td className="px-4 py-2">
                        {doc ? doc.title : 'Untitled Document'}
                      </td>
                      <td className="px-4 py-2 space-x-2">
                        <Button
                          variant="destructive"
                          size={'sm'}
                          onClick={() => {
                            const updatedDocs = selectedDocumentsByType[
                              type
                            ].filter((id) => id !== docId)
                            setSelectedDocumentsByType((prev) => ({
                              ...prev,
                              [type]: updatedDocs
                            }))
                          }}
                        >
                          Remove
                        </Button>
                      </td>
                    </tr>
                  )
                })
              })}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )
}
