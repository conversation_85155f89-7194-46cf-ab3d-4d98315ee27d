import { cn } from '@/lib/utils'
import Image from 'next/image'
import { ChangeEvent, FC, useState } from 'react'

interface Props {
  file: File | null
  setFile: React.Dispatch<React.SetStateAction<File | null>>
  allowedFileTypes: string[]
  maxFileSize: number
  storedFilePath?: string
  className?: string
}

const DropZone: FC<Props> = ({
  file,
  setFile,
  allowedFileTypes,
  maxFileSize,
  storedFilePath,
  className
}) => {
  const [isImage, setIsImage] = useState<boolean>(false)
  const [message, setMessage] = useState<string>(
    'Drag & drop an file, or click to select one.'
  )

  const validateFile = (file: File): string | null => {
    if (file.type.startsWith('image/')) {
      setIsImage(true)
    } else {
      setIsImage(false)
    }
    if (allowedFileTypes.indexOf(file.type) === -1) {
      return 'Unsupported file type.'
    }

    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size should be less than ${maxFileSize}MB.`
    }
    return null
  }

  const handleFiles = (files: FileList | null) => {
    if (!files) return

    const selectedFile = files[0]
    const validationError = validateFile(selectedFile)

    if (validationError) {
      setMessage(validationError)
    } else {
      setMessage('Ready to review ' + selectedFile.name)
      setFile(selectedFile)
    }
  }

  return (
    <div
      className={cn(
        className +
          ' border-2 border-dashed p-6 min-h-[200px] rounded-md relative flex items-center justify-center'
      )}
    >
      <input
        type="file"
        className="absolute top-0 left-0 w-full h-full opacity-0 cursor-pointer"
        id="file-input"
        onChange={(e: ChangeEvent<HTMLInputElement>) =>
          handleFiles(e.target.files)
        }
      />
      {file && isImage && (
        <Image
          src={URL.createObjectURL(file)}
          alt="Uploaded Preview"
          className="absolute top-0 left-0 w-full h-full object-cover"
          width={300}
          height={300}
        />
      )}

      {!file && isImage && storedFilePath && (
        <Image
          src={storedFilePath}
          alt="Uploaded Preview"
          width={300}
          height={300}
          className="absolute top-0 left-0 w-full h-full object-cover"
        />
      )}
      <label
        htmlFor="file-input"
        className="cursor-pointer z-10 absolute top-0 left-0 w-full h-full flex items-center justify-center hover:bg-opacity-40 hover:bg-black duration-200"
      >
        <p className="text-center">
          {file ? message : storedFilePath ? '' : message}
        </p>
      </label>
    </div>
  )
}

export default DropZone
