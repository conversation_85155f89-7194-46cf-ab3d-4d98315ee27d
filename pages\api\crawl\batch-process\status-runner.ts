import type { NextApiRequest, NextApiResponse } from 'next'
import { env } from '@/env.mjs'
import OpenAI from 'openai'
import {
  createOpenAiBatchRequest,
  deleteFileFromOpenAi,
  retrieveBatchStatus
} from './batch-utilts'
import { db } from '@/lib/db'
import { OpenAiBatchProcess } from '@prisma/client'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (env.NODE_ENV !== 'development') {
      throw new Error('Unauthorized')
    }

    const take = req.query.take ? parseInt(req.query.take as string) : 100

    const processList = await db.openAiBatchProcess.findMany({
      where: {
        batchId: {
          not: null
        },
        status: {
          notIn: ['completed', 'maximum_input_file_size_exceeded', 'failed_bak']
        }
      },
      take: take
    })

    const openai = new OpenAI()
    for (const process of processList) {
      await processBatchProgress({ process, openai })
    }

    res.status(200).json({ message: 'Data saved successfully!' })
  } catch (error) {
    console.error(error)
    res.status(500).json({ error: 'Failed to index.' })
  }
}

async function retryBatchCreation({
  process,
  openai
}: {
  process: OpenAiBatchProcess
  openai: OpenAI
}) {
  const upload = await createOpenAiBatchRequest({
    input_file_id: process.inputFileId,
    openai
  })

  const update = await await db.openAiBatchProcess.update({
    where: {
      id: process.id
    },
    data: {
      batchId: upload.id,
      status: upload.status,
      uploadResponse: JSON.stringify(upload)
    }
  })

  console.log(`Updated process ${process.id}: ${update.status}`)
}

async function processBatchProgress({
  process,
  openai
}: {
  process: OpenAiBatchProcess
  openai: OpenAI
}) {
  const response = await retrieveBatchStatus({
    batchId: process.batchId!,
    openai
  })
  if (response.jsonArray) {
    const batchSize = 200

    for (let i = 0; i < response.jsonArray.length; i += batchSize) {
      const batch = response.jsonArray.slice(i, i + batchSize)

      await Promise.all(
        batch.map(async (caseDoc: any) => {
          const docId = parseInt(caseDoc.custom_id)

          const currentRecord = await db.documentRecords.findUnique({
            select: { meta: true },
            where: { id: docId }
          })

          if (!currentRecord) {
            console.log('Document not found:', docId)
            return
          }

          const extendedMeta = {
            ...caseDoc.meta,
            ...JSON.parse(currentRecord.meta)
          }

          await db.documentRecords.update({
            where: { id: docId },
            data: { meta: JSON.stringify(extendedMeta), meta_ready: true }
          })

          console.log('Document metadata updated:', docId)
        })
      )
    }

    await db.openAiBatchProcess.update({
      where: {
        id: process.id
      },
      data: {
        status: 'completed',
        batchStatusResponse: JSON.stringify(response.status)
      }
    })

    console.log('Deleting input file')
    await deleteFileFromOpenAi({
      fileId: process.inputFileId,
      openai
    })

    console.log('Deleting output file')
    await deleteFileFromOpenAi({
      fileId: process.outputFileId!,
      openai
    })

    console.log('Batch process completed:', process.id)
  } else {
    const error = response.status.errors?.data?.[0]?.code
    const update = await db.openAiBatchProcess.update({
      where: {
        id: process.id
      },
      data: {
        status: error || response.status.status,
        batchStatusResponse: JSON.stringify(response.status)
      }
    })
    console.log(
      `Updated process ${process.id}: ${update.status}`,
      response.status.errors?.data
    )
    if (update.status === 'failed') {
      await retryBatchCreation({ process, openai })
    }
  }
}
