import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { caseBinderFeatures } from '@/config/dashboard'
import { db } from '@/lib/db'
import { PrivateUploadButton } from '@/components/elements/buttons/private-upload-button'
import { CaseFile, CaseFileType, CreditType } from '@prisma/client'
import CaseEvaluationSteps, {
  SelectedDocuments
} from '@/components/elements/doc-selector/case-evaluation-steps'
import { AttorneyInsightsFormData } from '@/components/elements/doc-selector/attorney-strategy-form'
import { redirect } from 'next/navigation'
import {
  getFeatureUsageStats,
  getMasqueradeUserNonNullable
} from '@/lib/session'
import { DocumentTitle } from '@/types/case'

export const metadata = caseBinderFeatures['caseEvaluation']

interface CaseEvaluationMasqueradePageProps {
  params: {
    userId: string
    binderId: string
  }
}

export default async function CaseEvaluationMasqueradePage({
  params
}: CaseEvaluationMasqueradePageProps) {
  const user = await getMasqueradeUserNonNullable(params.userId)

  const usageStats = await getFeatureUsageStats({
    feature: CreditType.case,
    user
  })

  const documentsPromise = db.documentRecords.findMany({
    where: {
      DocumentRecordDatasetMap: {
        some: {
          Dataset: {
            OR: [{ createdBy: params.binderId }, { binderId: params.binderId }]
          }
        }
      }
    },
    select: {
      id: true,
      title: true,
      indexed: true
    },
    orderBy: {
      title: 'asc'
    }
  })

  const binderPromise = db.binder.findUnique({
    where: {
      id: params.binderId,
      teamId: user.teamId
    }
  })

  const caseFilesPromise = db.caseFile.findMany({
    where: {
      binderId: params.binderId,
      fileType: {
        in: [
          CaseFileType.MEDICAL_CHRONOLOGY,
          CaseFileType.ATTORNEY_STRATEGY_PLAN,
          CaseFileType.CASE_EVALUATION
        ]
      }
    }
  })

  const [documents, binder, caseFiles] = await Promise.all([
    documentsPromise,
    binderPromise,
    caseFilesPromise
  ])

  if (!binder) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Binder not found"
          text="This binder does not exist or you don't have access to it."
        />
      </DashboardShell>
    )
  }

  const filesByType = caseFiles.reduce((acc: Record<string, any>, file) => {
    acc[file.fileType] = file
    return acc
  }, {})

  const medicalChronology: CaseFile =
    filesByType[CaseFileType.MEDICAL_CHRONOLOGY]
  const attorneyStrategyPlanData: CaseFile =
    filesByType[CaseFileType.ATTORNEY_STRATEGY_PLAN]
  const attorneyStrategyPlan = attorneyStrategyPlanData?.content
    ? (JSON.parse(attorneyStrategyPlanData.content) as AttorneyInsightsFormData)
    : null
  const caseEvaluation: CaseFile = filesByType[CaseFileType.CASE_EVALUATION]

  // Remove the redirect - now case evaluation is accessible without medical chronology
  // The lite version works without medical chronology, full version will be disabled

  const allDocuments: DocumentTitle[] = documents.map((doc) => ({
    id: doc.id,
    title: doc.title,
    indexed: doc.indexed
  }))

  return (
    <DashboardShell>
      <DashboardHeader
        heading={metadata.title + ': Masquerade ' + user.name}
        text={metadata.description}
      >
        <PrivateUploadButton className="flex" binderId={params.binderId} />
      </DashboardHeader>
      <div className="grid gap-10">
        <CaseEvaluationSteps
          usageStats={usageStats}
          binder={binder}
          allDocuments={allDocuments}
          caseEvaluation={caseEvaluation}
          attorneyStrategyPlan={attorneyStrategyPlan}
          medicalChronologyExists={!!medicalChronology}
          selectedDocumentsByTypeFromStore={
            (caseEvaluation?.selectedDocumentsByType ||
              medicalChronology?.selectedDocumentsByType ||
              {}) as SelectedDocuments
          }
        />
      </div>
    </DashboardShell>
  )
}
