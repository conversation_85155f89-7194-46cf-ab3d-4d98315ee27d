import { Skeleton } from '@/components/ui/skeleton'

export default function Loading() {
  return (
    <div className="grid w-full gap-6 p-12">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-10 w-32" />
      </div>

      <Skeleton className="h-12 w-full" />

      <div className="space-y-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div
            key={index}
            className="flex items-center justify-between space-x-4"
          >
            <Skeleton className="h-8 w-1/3" />
            <Skeleton className="h-8 w-1/6" />
            <Skeleton className="h-8 w-1/6" />
            <Skeleton className="h-8 w-1/6" />
            <Skeleton className="h-8 w-1/6" />
          </div>
        ))}
      </div>
    </div>
  )
}
