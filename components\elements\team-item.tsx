import {
  User,
  Team,
  TeamDocument,
  TeamPeriodicCredit,
  TeamCreditUsed
} from '@prisma/client'

import { formatDate } from '@/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'
import { TeamCreditManagerModal } from './team-credit-modal'
import { DeleteTeamButton } from './delete-team-button'

interface TeamExtended extends Team {
  index: number
  User: User[]
  TeamDocument: TeamDocument[]
  TeamPeriodicCredit: TeamPeriodicCredit[]
  TeamCreditUsed: TeamCreditUsed[]
}

interface TeamItemProps {
  team: TeamExtended
}

export function TeamItem({ team }: TeamItemProps) {
  let minDate = new Date()
  let maxExpiresAt = new Date()
  let totalCreditAvailable = 0
  let totalCreditUsed = 0
  const creditAvailable = team.TeamPeriodicCredit.reduce((acc, item) => {
    totalCreditAvailable += item.creditAvailable
    if (new Date(item.expiresAt) > new Date()) {
      if (new Date(item.createdAt) < minDate) {
        minDate = new Date(item.createdAt)
      }
      if (new Date(item.expiresAt) > maxExpiresAt) {
        maxExpiresAt = new Date(item.expiresAt)
      }
      return acc + item.creditAvailable
    }
    return acc
    // return acc + item.creditAvailable
  }, 0)

  const creaditUsed = team.TeamCreditUsed.reduce((acc, item) => {
    totalCreditUsed += item.creditUsed
    if (new Date(item.createdAt) > minDate) {
      return acc + item.creditUsed
    }
    return acc
  }, 0)

  return (
    <div className="flex items-center justify-between p-4">
      <div className="grid gap-1">
        <p className="font-semibold">
          <span className="text-sm text-muted-foreground">{team.index}. </span>
          {team.name || '_unknown'}
          <span className="text-sm text-muted-foreground">
            {' '}
            by {team.User[0]?.name || '_unknown'}
          </span>
        </p>
        <p className="text-sm text-muted-foreground">
          Started on {formatDate(team.createdAt?.toDateString())}
        </p>

        {creditAvailable > 0 ? (
          <p className="text-sm text-muted-foreground">
            Credit Usage: {creaditUsed} / {creditAvailable} expires on{' '}
            {formatDate(maxExpiresAt.toDateString())}
          </p>
        ) : (
          <p className="text-sm text-red-800">
            Credits expired on {formatDate(maxExpiresAt.toDateString())}
          </p>
        )}
        <p className="text-sm text-muted-foreground">
          Total Usage: {totalCreditUsed} / {totalCreditAvailable}
        </p>
      </div>
      <div className="flex flex-col gap-2 w-32">
        <TeamCreditManagerModal teamId={team.id} />
        <DeleteTeamButton teamId={team.id} teamName={team.name || '_unknown'} />
      </div>
    </div>
  )
}

TeamItem.Skeleton = function TeamItemSkeleton() {
  return (
    <div className="p-4">
      <div className="space-y-3">
        <Skeleton className="h-5 w-2/5" />
        <Skeleton className="h-4 w-4/5" />
      </div>
    </div>
  )
}
