'use server'

import {
  downloadFileContent,
  getAuthorizedDriveClient,
  getAuthUrl
} from '../services/google-drive-service'
import { redirect } from 'next/navigation'
import { getCurrentUser } from '../session'
import { injest } from './injest'

export async function redirectToAuthUrl(message: string) {
  console.log('redirectToAuthUrl', message)
  const authUrl = getAuthUrl()
  return redirect(authUrl)
}

export async function getGoogleDrivePDFfiles() {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return null
    }
    const drive = await getAuthorizedDriveClient(user.id)

    if (!drive) {
      const authUrl = getAuthUrl()
      return {
        revalidate: authUrl
      }
    }

    const response = await drive.files.list({
      q: "mimeType='application/pdf' or mimeType='application/vnd.openxmlformats-officedocument.wordprocessingml.document'",
      fields: 'files(id, name)'
    })

    return response.data.files
  } catch (error: any) {
    console.log(error)
    throw new Error('An error occurred while fetching files')
  }
}

export async function injestFileFromGDrive(fileId: string) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return null
    }
    const file = await downloadFileContent(fileId, user.id)

    await injest({
      ...file,
      teamId: user.teamId!,
      region: user.region
    })

    return true
  } catch (error: any) {
    console.log(error.message)
  }
}
