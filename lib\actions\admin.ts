'use server'

import { DocumentRecords } from '@prisma/client'
import { db } from '../db'

export async function getTeamsReportList() {
  try {
    const teams = await db.team.findMany({
      include: {
        User: true,
        TeamDocument: true,
        TeamPeriodicCredit: true,
        TeamCreditUsed: true
      }
    })

    const processedData = teams.map((team, index) => ({
      index: index + 1,
      teamId: team.id,
      teamName: team.name || '_unknown',
      owner: team.User[0]?.name || '_unknown',
      ownerEmail: team.User[0]?.email || '_unknown',
      createdAt: team.createdAt ?? '_unknown',
      creditUsage: team.TeamCreditUsed.reduce(
        (acc, item) => acc + item.creditUsed,
        0
      ),
      creditAvailable: team.TeamPeriodicCredit.reduce(
        (acc, item) => acc + item.creditAvailable,
        0
      ),
      lastUsed:
        team.TeamCreditUsed[team.TeamCreditUsed.length - 1]?.createdAt ??
        '_unknown'
    }))

    return processedData
  } catch (error: any) {
    return error.message || 'An error occurred while fetching teams'
  }
}

export async function getDocumentRecordById(
  id: number
): Promise<DocumentRecords | null | string> {
  try {
    const document = await db.documentRecords.findUnique({
      where: {
        id
      }
    })

    return document
  } catch (error: any) {
    return error.message || 'An error occurred while fetching document'
  }
}

export async function updateDocumentRecordMeta(
  id: number,
  meta: string
): Promise<DocumentRecords | string> {
  try {
    const document = await db.documentRecords.update({
      where: {
        id
      },
      data: {
        meta
      }
    })

    return document
  } catch (error: any) {
    return error.message || 'An error occurred while updating document'
  }
}
