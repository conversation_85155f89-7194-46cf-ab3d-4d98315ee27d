import { redirect } from 'next/navigation'
import { authOptions } from '@/lib/auth'
import { getCurrentUser } from '@/lib/session'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { LegalQuery } from '@/components/elements/chat/legal-query'
import { features } from '@/config/dashboard'
import type { ResearchStoreContent } from '@/types'
import { db } from '@/lib/db'
import { PrivateUploadInstructor } from '@/components/elements/private-upload-instructor'
import { RecentResearchList } from '@/components/elements/research/recent-research-list'
import { PrivateDocumentSelector } from '@/components/elements/document/private-upload-research-selector'
import Link from 'next/link'
import { buttonVariants } from '@/components/ui/button'
import { ResearchType } from '@prisma/client'

export const metadata = features['researchPrivate']

export default async function ResearchStartPage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect(authOptions?.pages?.signIn || '/login')
  }

  const researchHistory = await db.researchStore.findMany({
    select: {
      id: true,
      createdAt: true,
      question: true
    },
    where: {
      userId: user.id,
      type: ResearchType.private,
      region: user.region
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 5
  })

  const documents = await db.documentRecords.findMany({
    select: {
      id: true,
      title: true
    },
    where: {
      source: user.teamId,
      region: user.region
    },
    orderBy: {
      title: 'asc'
    }
    // take: 200
  })

  const groupedDocumentsFinder = await db.dataset.findMany({
    where: {
      createdBy: user.teamId
    },
    select: {
      id: true,
      name: true,
      DocumentRecordDatasetMap: {
        select: {
          DocumentRecords: {
            select: {
              id: true,
              title: true
            }
          }
        }
      }
    }
  })

  const groupedDocuments = groupedDocumentsFinder
    .map((group) => ({
      id: group.id,
      name: group.name,
      DocumentRecords: group.DocumentRecordDatasetMap.map((doc) => ({
        id: doc.DocumentRecords.id,
        title: doc.DocumentRecords.title
      }))
    }))
    .filter((group) => group.DocumentRecords.length > 0)

  const allDocuments = documents.map((doc) => ({
    id: doc.id.toString(),
    title: doc.title
  }))

  const researchProps: ResearchStoreContent = {
    model: 'brainstem',
    sources: documents.map((doc) => doc.id.toString()),
    court: [],
    year: [],
    sourcesForMessages: {},
    sourceLabels: documents.map((doc) => ({
      id: doc.id.toString(),
      title: doc.title
    }))
  }

  return (
    <DashboardShell>
      <DashboardHeader heading={metadata.title} text={metadata.description}>
        <Link
          className={buttonVariants()}
          href="/dashboard/research-private/documents"
        >
          Manage Documents
        </Link>
      </DashboardHeader>
      <div className="grid gap-10">
        {documents.length > 0 ? (
          <LegalQuery
            researchProps={researchProps}
            user={user}
            researchType={ResearchType.private}
            namespace={user.teamId}
            showFilters={false}
            emptyStateComponent={
              <PrivateDocumentSelector
                allDocuments={allDocuments}
                groupedDocuments={groupedDocuments}
              />
            }
          />
        ) : (
          <PrivateUploadInstructor user={user} />
        )}

        {researchHistory.length > 0 && (
          <RecentResearchList
            researchHistory={researchHistory}
            path={'/dashboard/research-private'}
          />
        )}
      </div>
    </DashboardShell>
  )
}
