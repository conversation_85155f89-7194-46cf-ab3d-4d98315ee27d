import { DashboardConfig, FeatureList } from '@/types'

export const dashboardConfig: DashboardConfig = {
  rootPath: '/dashboard',
  mainNav: [
    {
      title: 'Support',
      href: '/support',
      disabled: false
    },
    {
      title: 'FAQ',
      href: '/faq',
      disabled: false
    }
    // {
    //   title: 'Settings',
    //   href: '/dashboard/settings',
    //   disabled: false
    // }
  ],
  sidebarNav: [
    {
      title: 'Cases',
      href: '/dashboard/case',
      icon: 'case'
    },
    {
      title: 'Research Law',
      href: '/dashboard/research',
      icon: 'library'
    },
    {
      title: 'Research Case',
      href: '/dashboard/research-case',
      icon: 'briefcase'
    },
    {
      title: 'Internal Research',
      href: '/dashboard/research-private',
      icon: 'textSearch'
    },
    // {
    //   title: 'Conversations',
    //   href: '/dashboard/converse',
    //   icon: 'chat'
    // },
    // {
    //   title: 'Document Review',
    //   href: '/dashboard/document-review',
    //   icon: 'post'
    // },
    // {
    //   title: 'Search Database',
    //   href: '/dashboard/database',
    //   icon: 'database'
    // },
    {
      title: 'Documents',
      href: '/dashboard/research-private/documents',
      icon: 'storage'
    },
    {
      title: 'Settings',
      href: '/dashboard/settings',
      icon: 'settings'
    }
  ]
}

export const adminConfig: DashboardConfig = {
  rootPath: '/admin',
  mainNav: [],
  sidebarNav: [
    {
      title: 'Users',
      href: '/admin/users',
      icon: 'user'
    },
    {
      title: 'Teams',
      href: '/admin/teams',
      icon: 'user'
    },
    {
      title: 'Newsletters',
      href: '/admin/newsletter',
      icon: 'post'
    },
    {
      title: 'Documents',
      href: '/admin/documents',
      icon: 'post'
    },
    {
      title: 'Prompts',
      href: '/admin/prompts',
      icon: 'post'
    }
  ]
}

export const features: FeatureList = {
  case: {
    title: 'Cases',
    description:
      'Manage your cases efficiently, including creating, editing, and organizing case details for streamlined legal practice.',
    link: '/dashboard/case'
  },
  research: {
    title: 'Research Law',
    description:
      'Explore detailed analyses of federal and state laws, including articles and sections, to gain comprehensive legal insights tailored to your jurisdiction and case specifics.',
    link: '/dashboard/research'
  },
  researchCase: {
    title: 'Research Case',
    description:
      'Delve into case-specific discussions, focusing on depositions, proceedings, and judgments, to prepare effectively for your legal strategy.',
    link: '/dashboard/research-case'
  },
  researchPrivate: {
    title: 'Internal Research',
    description:
      'Do research over your documents, and get insights on your case.',
    link: '/dashboard/research-private'
  },
  redactionTool: {
    title: 'Redaction Tool',
    description:
      'Upload documents and redact sensitive information to ensure privacy and confidentiality.',
    link: '/dashboard/redaction'
  },
  // researchCriminalLaw: {
  //   title: 'Research New Criminal Law - BNS',
  //   description:
  //     'Research about the new criminal law Bharatiya Nyaya Sanhita (BNS).',
  //   link: '/dashboard/research-criminal-law'
  // },
  // {
  //   title: 'Conversations',
  //   description:
  //     'Engage in smart conversations with SmartCounsel AI to prepare for depositions.',
  //   link: '/dashboard/converse'
  // },
  searchDatabase: {
    title: 'Search Database',
    description:
      'Query the database to find relevant documents and information for your legal needs.'
    // link: '/dashboard/database'
  },
  documentReview: {
    title: 'Document Review',
    description:
      'Upload documents and receive recommendations for revisions and conflict resolutions.'
    // link: '/dashboard/document-review'
  },
  contractRevision: {
    title: 'Contract Revision',
    description:
      'Upload contracts, specify compliance policies, and receive recommendations for revisions and conflict resolutions.'
  }
}

export const caseBinderFeatures = {
  medicalChronology: {
    title: 'Medical Chronology',
    description:
      'Upload medical records and generate a structured chronological summary to streamline case preparation and analysis.',
    link: '/dashboard/case/{{binderId}}/medical-chronology'
  },
  plaintiffFactSheet: {
    title: 'Plaintiff Fact Sheet (PFS)',
    description:
      'Collect and organize structured plaintiff information efficiently for legal documentation and case management.',
    link: undefined
  },
  caseEvaluation: {
    title: 'Case Evaluation',
    description:
      'Leverage AI-driven analysis to assess case strength, identify key legal strategies, and optimize case outcomes.',
    link: '/dashboard/case/{{binderId}}/case-evaluation'
  },
  demandLetterGeneration: {
    title: 'Demand Letter Generation',
    description:
      'Automate the drafting of demand letters to enhance settlement negotiations and streamline legal communications.',
    link: '/dashboard/case/{{binderId}}/demand-letter'
  }
}

export type BinderFeature = keyof typeof caseBinderFeatures

export const CASE_FEATURE_GENERATION_TEXT: {
  [key in BinderFeature]: string[]
} = {
  medicalChronology: [
    'Gathering all medical records for processing… Ensuring no files are missed.',
    'Scanning all medical records… Extracting key dates, diagnoses, and treatments.',
    'Sorting records by facility, physician, and date to establish a clear timeline.',
    'Identifying hospital visits, doctor consultations, and procedures performed.',
    'Recognizing critical injuries, medications prescribed, and recovery progress.',
    'Extracting key medical terminologies and conditions relevant to the case.',
    'Cross-referencing reports, imaging studies, and lab results for accuracy.',
    'Detecting duplicate entries and inconsistencies in medical documentation.',
    'Cross-checking records to ensure consistency and accuracy in the timeline.',
    'Structuring medical events in chronological order for a clear and concise report.',
    'Highlighting gaps, missing records, and inconsistencies that may impact the case.',
    'Assessing the impact of medical findings on the overall case evaluation.',
    'Summarizing key observations and legal implications for easy case review.',
    'Finalizing a structured summary of medical events—ready for review.',
    'Preparing a downloadable and shareable report for legal and medical teams.'
  ],
  plaintiffFactSheet: [],
  caseEvaluation: [
    'Collecting case documents and attorney strategic insights for comprehensive evaluation...',
    'Extracting financial data from documents to quantify economic damages...',
    'Processing attorney insights to incorporate jurisdiction-specific considerations...',
    'Assessing liability distribution between plaintiff, defendant, and other parties...',
    'Analyzing evidence strength and identifying potential legal defenses...',
    'Calculating economic damages including medical expenses, lost wages, and property losses...',
    'Determining appropriate non-economic damages for pain, suffering, and emotional distress...',
    'Evaluating eligibility and justification for potential punitive damages...',
    'Identifying case strengths, weaknesses, and performing comprehensive risk assessment...',
    'Developing optimal litigation strategy with settlement targets and trial approach...',
    'Generating initial case evaluation report with detailed findings and recommendations...',
    'Analyzing case from defense perspective to anticipate counter-arguments...',
    'Identifying potential vulnerabilities and counter-strategies defense might employ...',
    'Revising case evaluation to address defense perspective considerations...',
    'Finalizing comprehensive analysis with actionable recommendations and settlement guidance...'
  ],
  demandLetterGeneration: [
    'Initializing demand letter generation process for the specified case ID...',
    'Setting up performance tracking metrics for generation time and token usage...',
    'Fetching plaintiff information, medical records, and incident details from case database...',
    'Retrieving case evaluation data and liability assessment to inform demand strategy...',
    'Collecting economic, non-economic, and punitive damages calculations from case files...',
    'Organizing all context data into structured format for AI processing...',
    'Creating batches of letter sections to optimize generation throughput...',
    'Generating cover letter section with appropriate tone and recipient information...',
    'Developing comprehensive facts and liability section with compelling narrative...',
    'Creating detailed injuries and treatments section with supporting medical evidence...',
    'Analyzing medical chronology to establish clear causation between incident and injuries...',
    'Generating economic damages section with detailed calculations of past expenses...',
    'Projecting future medical expenses based on treatment plans and expert opinions...',
    'Calculating lost income and diminished earning capacity with supporting documentation...',
    'Developing pain and suffering section with proper justification and comparable cases...',
    'Creating punitive damages argument when defendant conduct justifies additional penalties...',
    'Extending complex sections that exceed single-generation capacity...',
    'Performing quality checks to ensure factual accuracy and persuasive presentation...',
    'Formulating specific settlement demand with clear monetary requirements...',
    'Generating comprehensive exhibit list to document all supporting evidence...',
    'Assembling all sections in proper sequential order for logical presentation...',
    'Finalizing cohesive demand letter with consistent formatting and professional tone...'
  ]
}
