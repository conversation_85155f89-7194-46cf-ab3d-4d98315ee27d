import * as z from 'zod'

export const dbSearchSchema = z.object({
  query: z.string(),
  filter: z
    .object({
      court: z.string().optional(),
      year: z.string().optional(),
      documentRecordsId: z
        .object({
          $nin: z.array(z.number()).optional()
        })
        .optional()
    })
    .optional()
})

export const caseDataMetadataSchema = z.object({
  title: z.string(),
  case: z.string(),
  court: z.string(),
  judges: z.string().optional(),
  parties: z.string().optional(),
  citation: z.string().optional(),
  date: z.string().optional(),
  year: z.number().optional(),
  headnotes: z.string().optional(),
  summary: z.string().optional()
})
