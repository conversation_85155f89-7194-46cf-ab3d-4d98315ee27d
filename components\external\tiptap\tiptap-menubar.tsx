import { Editor } from '@tiptap/react'

import { TipTapMenuButton } from './tiptap-menubutton'
import {
  TipTapColorPicker,
  TipTapTextFormatPicker
} from './tiptap-menu-selectors'

export const TipTapMenuBar = ({ editor }: { editor: Editor | null }) => {
  if (!editor) {
    return null
  }

  return (
    <div className="flex space-x-2 bg-slate-100 p-2 px-4 rounded-md mt-3 w-fit">
      <div className="flex items-center space-x-7">
        {/* Heading Select */}
        <TipTapTextFormatPicker editor={editor} />
        <TipTapColorPicker editor={editor} />

        {/* Styling Buttons */}
        <div className="flex space-x-2 bg-slate-200 p-1 rounded-md">
          <TipTapMenuButton format="bold" editor={editor} />
          <TipTapMenuButton format="italic" editor={editor} />
          <TipTapMenuButton format="underline" editor={editor} />
          <TipTapMenuButton format="strike" editor={editor} />
        </div>

        {/* Alignment Buttons */}
        <div className="flex space-x-2 bg-slate-200 p-1 rounded-md">
          <TipTapMenuButton format="alignLeft" editor={editor} />
          <TipTapMenuButton format="alignCenter" editor={editor} />
          <TipTapMenuButton format="alignRight" editor={editor} />
          <TipTapMenuButton format="alignJustify" editor={editor} />
        </div>

        {/* List Buttons */}
        <div className="flex space-x-2 bg-slate-200 p-1 rounded-md">
          <TipTapMenuButton format="bulletList" editor={editor} />
          <TipTapMenuButton format="orderedList" editor={editor} />
        </div>

        {/* Table Buttons */}
        <div className="flex space-x-2 bg-slate-200 p-1 rounded-md">
          <TipTapMenuButton format="table" editor={editor} />
          <TipTapMenuButton format="addColumnAfter" editor={editor} />
          <TipTapMenuButton format="deleteColumn" editor={editor} />
          <TipTapMenuButton format="addRowAfter" editor={editor} />
          <TipTapMenuButton format="deleteRow" editor={editor} />
          <TipTapMenuButton format="deleteTable" editor={editor} />
        </div>
      </div>
    </div>
  )
}
