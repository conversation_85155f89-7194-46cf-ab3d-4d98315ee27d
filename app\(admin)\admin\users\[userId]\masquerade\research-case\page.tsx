import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { LegalQuery } from '@/components/elements/chat/legal-query'
import { features } from '@/config/dashboard'
import type { ResearchStoreContent } from '@/types'
import { db } from '@/lib/db'
import { RecentResearchList } from '@/components/elements/research/recent-research-list'
import { AdminMasqueradeProps } from '../../layout'
import { ResearchInfoCard } from '@/components/elements/custom-components/feature-info-card'
import { getMasqueradeUserNonNullable } from '@/lib/session'
import { ResearchType } from '@prisma/client'

export const metadata = features['researchCase']

export default async function ResearchStartPageMasquerade({
  params
}: AdminMasqueradeProps) {
  const user = await getMasqueradeUserNonNullable(params.userId)

  const researchHistory = await db.researchStore.findMany({
    where: {
      userId: user.id,
      type: ResearchType.case
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 50
  })

  const researchProps: ResearchStoreContent = {
    model: 'brainstem',
    sources: [],
    court: [],
    year: [],
    sourcesForMessages: {}
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading={metadata.title + ': Masquerade ' + user.name}
        text={metadata.description}
      />
      <div className="grid gap-10">
        <LegalQuery
          researchProps={researchProps}
          user={user}
          researchType={ResearchType.case}
          showFilters={true}
          masquerade={true}
          emptyStateComponent={<ResearchInfoCard />}
        />

        {researchHistory.length > 0 && (
          <RecentResearchList
            researchHistory={researchHistory}
            path={'/dashboard/research'}
          />
        )}
      </div>
    </DashboardShell>
  )
}
