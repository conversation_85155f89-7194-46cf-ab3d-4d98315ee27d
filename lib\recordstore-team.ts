'use server'

import { CreditUsageStats } from '@/types'
import { db, handlePrismaError } from './db'
import type { CreditType, Team } from '@prisma/client'

export const createNewTeamRecord = async (name: string, owner: string) => {
  try {
    const newTeam = await db.team.create({
      data: {
        name: name
      }
    })

    await db.user.update({
      where: {
        id: owner
      },
      data: {
        teamId: newTeam.id
      }
    })

    return newTeam
  } catch (error) {
    handlePrismaError(error)
  }
}

export const findTeamFromId = async (id: string) => {
  try {
    const team = await db.team.findUnique({
      where: {
        id: id
      }
    })
    return (
      team ||
      ({
        id: 'pending',
        name: '...pending'
      } as Team)
    )
  } catch (error) {
    handlePrismaError(error)
  }
}

export const validateAndCreateTrialCreditOnFeature = async ({
  teamId,
  type
}: {
  teamId: string
  type: CreditType
}) => {
  try {
    const validate = await db.teamPeriodicCredit.count({
      where: {
        teamId: teamId,
        type: type
      }
    })

    if (validate === 0) {
      console.log('Creating trial credit for team ' + teamId)
      await db.teamPeriodicCredit.create({
        data: {
          teamId: teamId,
          type: type,
          period: 7,
          creditAvailable: 20,
          expiresAt: new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000)
        }
      })
    }
  } catch (error) {
    handlePrismaError(error)
  }
}

export const findTeamCreditStats = async ({
  teamId
}: {
  teamId: string
}): Promise<CreditUsageStats | undefined> => {
  try {
    const credits = await db.teamPeriodicCredit.findMany({
      where: {
        teamId: teamId,
        expiresAt: {
          gt: new Date()
        }
      }
    })

    const creditsMap = await credits.reduce(
      async (accPromise, curr) => {
        const acc = await accPromise

        if (!acc[curr.type]) {
          acc[curr.type] = {
            creditAvailable: 0,
            creditUsed: 0,
            startDate: curr.createdAt // Initialize startDate with curr.createdAt
          }
        }

        acc[curr.type].creditAvailable += curr.creditAvailable
        acc[curr.type].startDate =
          acc[curr.type].startDate < curr.createdAt
            ? acc[curr.type].startDate
            : curr.createdAt

        // Fetch credit used for the specific type and period
        const creditUsedEntries = await db.teamCreditUsed.findMany({
          where: {
            teamId: teamId,
            type: curr.type,
            createdAt: {
              gte: acc[curr.type].startDate
            }
          }
        })

        const creditUsed = creditUsedEntries.reduce((usedAcc, usedCurr) => {
          return usedAcc + usedCurr.creditUsed
        }, 0)

        acc[curr.type].creditUsed += creditUsed

        return acc
      },
      Promise.resolve(
        {} as {
          [key in CreditType]: {
            creditAvailable: number
            creditUsed: number
            startDate: Date
          }
        }
      )
    )

    return Object.keys(creditsMap).length > 0 ? creditsMap : undefined
  } catch (error) {
    handlePrismaError(error)
  }
}

export const findTeamPeriodicCreditOnFeature = async ({
  teamId,
  type
}: {
  teamId: string
  type: CreditType
}) => {
  try {
    const credit = await db.teamPeriodicCredit.findMany({
      where: {
        teamId: teamId,
        type: type,
        expiresAt: {
          gt: new Date()
        }
      }
    })

    const totalCredit = credit.reduce((acc, curr) => {
      return acc + curr.creditAvailable
    }, 0)

    const minStartDate = credit.reduce((acc, curr) => {
      return acc < curr.createdAt ? acc : curr.createdAt
    }, new Date())

    return {
      credit: totalCredit,
      startDate: minStartDate
    }
  } catch (error) {
    handlePrismaError(error)
  }
}

export async function addTeamPeriodicCreditOnFeature({
  teamId,
  type,
  period,
  credit
}: {
  teamId: string
  type: CreditType
  period: number
  credit: number
}) {
  try {
    const create = await db.teamPeriodicCredit.create({
      data: {
        teamId: teamId,
        type: type,
        period: period,
        creditAvailable: credit,
        expiresAt: new Date(new Date().getTime() + period * 24 * 60 * 60 * 1000)
      }
    })

    return create
  } catch (error) {
    handlePrismaError(error)
  }
}

export async function findTeamPeriodicCreditHistoryOnFeature({
  teamId,
  type,
  startDate
}: {
  teamId: string
  type: CreditType
  startDate?: Date
}) {
  try {
    const credit = await db.teamCreditUsed.findMany({
      where: {
        teamId: teamId,
        type: type,
        createdAt: {
          gte: startDate
        }
      }
    })

    const creditUsed = credit.reduce((acc, curr) => {
      return acc + curr.creditUsed
    }, 0)

    return creditUsed
  } catch (error) {
    handlePrismaError(error)
  }
}

export async function fetchPeriodicCreditHistory({
  teamId,
  type
}: {
  teamId: string
  type: CreditType
}) {
  try {
    const credit = await db.teamPeriodicCredit.findMany({
      where: {
        teamId: teamId,
        type: type
      }
    })

    return credit
  } catch (error) {
    handlePrismaError(error)
  }
}
