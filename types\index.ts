import { Icons } from '@/components/elements/icons'
import { CreditType, Region } from '@prisma/client'
import type { Message } from 'ai'

export type VercelChatMessage = Message

export type GeminiChatMessage = { role: string; parts: { text: string }[] }

export type NavItem = {
  title: string
  href: string
  disabled?: boolean
}

export type MainNavItem = NavItem

export type SidebarNavItem = {
  title: string
  disabled?: boolean
  external?: boolean
  icon?: keyof typeof Icons
} & (
  | {
      href: string
      items?: never
    }
  | {
      href?: string
      items: NavItem[]
    }
)

export type SiteConfig = {
  name: string
  description: string
  url: string
  ogImage: string
  links: {
    twitter: string
    github: string
    ensoview: string
  }
}

export type DocsConfig = {
  mainNav: MainNavItem[]
  sidebarNav: SidebarNavItem[]
}

export type MarketingConfig = {
  mainNav: MainNavItem[]
  footerNav: NavItem[]
}

export type DashboardConfig = {
  rootPath: string
  mainNav: MainNavItem[]
  sidebarNav: SidebarNavItem[]
}

export type FeatureKey =
  | 'case'
  | 'research'
  | 'researchCase'
  | 'researchPrivate'
  // | 'researchCriminalLaw'
  // | 'converse'
  | 'documentReview'
  | 'contractRevision'
  | 'searchDatabase'
  | 'redactionTool'

export type FeatureList = {
  [K in FeatureKey]: {
    title: string
    description: string
    link?: string
  }
}

// export type FeatureList = Partial<{
//   [K in FeatureKey]: {
//     title: string
//     description: string
//     link?: string
//   }
// }> &
//   { [K in FeatureKey]: {} }[keyof { [K in FeatureKey]: {} }] //At Least One Key Required

export type SubscriptionPlan = {
  name: string
  description: string
  stripePriceId: string
}

export type NewsletterGeneratorConfig = {
  type: {
    label: string
    value: string
  }[]
  description: {
    label: string
    value: string
  }[]
  languageStyle: {
    label: string
    value: string
  }[]
}

export type BrifConfig = {
  type: string
  description: string
  audience?: string
  languageStyle: string
}

export type NewsletterConfig = {
  title: string
  theme: string
  keyLegalUpdates: string[]
  content: string
  targetAudience?: string
  editorialTone: string
}

export interface TextDavinciResponse {
  id: string
  object: string
  created: number
  model: string
  choices?: OpenAIChoicesEntity[]
  usage: OpenAIUsage
}
export interface OpenAIChoicesEntity {
  text: string
  index: number
  logprobs?: null
  finish_reason: string
}
export interface OpenAIUsage {
  prompt_tokens: number
  completion_tokens: number
  total_tokens: number
}

export interface SmartCouncelChatMessage extends VercelChatMessage {
  userId?: string
  timestamp?: string
}

export interface ResearchStoreContent {
  researchId?: string
  court: string[]
  year: string[]
  namespace?: string
  model: string
  sources: string[]
  sourcesForMessages: Record<string, any>
  messages?: SmartCouncelChatMessage[]
  sourceLabels?: {
    id: string
    title: string
  }[]
}

export enum IndianLawSource {
  LabourLaw = 'labourlawreporter',
  IndiaGST = 'taxindiaonline',
  IndiaIncomeTax = 'taxindiaonline-income-tax'
}

export enum USLawSource {
  FederalSupremeCourt = 'federalcourts_ussupremecourt'
}

export interface StoredNamespace {
  [Region.IN]: IndianLawSource
  [Region.US]: USLawSource
}

export enum GPTModel {
  GPT3 = 'gpt-3.5-turbo',
  GPT4 = 'gpt-4',
  GPT4o = 'gpt-4o',
  GPT4x1 = 'gpt-4.1',
  O1 = 'o1',
  GPT4oMini = 'gpt-4o-mini',
  GPT4Turbo = 'gpt-4-turbo',
  GPTo4Mini = 'o4-mini',
  GPTo3 = 'o3'
}

export enum GeminiModel {
  Gemini25Flash = 'gemini-2.5-flash',
  Gemini25Pro = 'gemini-2.5-pro'
}

export enum GPTEmbeddingModel {
  TEsmall = 'text-embedding-3-small',
  TElarge = 'text-embedding-3-large',
  TEada002 = 'text-embedding-ada-002'
}

export type CreditUsageStats = {
  [key in CreditType]: {
    creditAvailable: number
    creditUsed: number
    startDate: Date
  }
}

export enum YesNo {
  YES = 'yes',
  NO = 'no'
}

export interface QueryAssessmentResponse {
  relevance: YesNo
  sector: StoredNamespace[Region] | string
  independent: YesNo
  specific_case: YesNo
  rewritten_question: string
  related_searches?: string[]
}

export interface QuestionIntent {
  question: string
  intent: string
}

export interface QueryClarityCheckResponse {
  relevance: YesNo
  independent: YesNo
  specific_case: YesNo
  rewritten_question_set: QuestionIntent[]
}

export enum RESEARCH_QUERY_TYPE {
  'SUMMARISE' = 'summarise',
  'REWRITE' = 'rewrite',
  'ASK' = 'ask'
}
