// hooks/useChatWindow.ts
import { useRef, useState, useEffect } from 'react'
import { useChat } from 'ai/react'
import { toast } from '@/components/ui/use-toast'
import {
  QuestionIntent,
  RESEARCH_QUERY_TYPE,
  ResearchStoreContent
} from '@/types'
import type { Session } from 'next-auth'
import { useAtom } from 'jotai'
import { selectedDocumentsAtom } from './atom'
import { admin } from '../utils'
import { ResearchType } from '@prisma/client'

// Reusable Hooks
import { useYearRange } from './chat-window/use-year-range'
import { useQueryClarification } from './chat-window/use-query-clarification'
import { useSourceUpdater } from './chat-window/use-source-updater'
import { useChatHandlers } from './chat-window/use-chat-handlers'
import { useStoreResearch } from './chat-window/use-research-store'

export function useChatWindow(props: {
  user: Session['user']
  researchProps: ResearchStoreContent
  researchType: ResearchType
  namespace?: string
  masquerade?: boolean
}) {
  const { researchProps } = props

  // 1. DOM references
  const messageContainerRef = useRef<HTMLDivElement | null>(null)

  // 2. Local states
  const [eventQuery, setEventQuery] = useState<RESEARCH_QUERY_TYPE | null>(null)
  const [openQueryClarification, setOpenQueryClarification] =
    useState<boolean>(false)
  const [chatEndpointIsLoading, setChatEndpointIsLoading] =
    useState<boolean>(false)
  const [questions, setQuestions] = useState<QuestionIntent[]>([])
  const [sources, setSources] = useAtom(selectedDocumentsAtom)
  const [sourceName, setSourceName] = useState<string | null>(null)
  const [finished, setFinished] = useState(false)

  // 3. Year range management
  const { startYear, setStartYear, endYear, setEndYear, year } =
    useYearRange(researchProps)

  // 4. Additional props from researchProps
  const [court, setCourt] = useState<ResearchStoreContent['court']>(
    researchProps.court || []
  )
  const [namespace, setNamespace] = useState<ResearchStoreContent['namespace']>(
    props.namespace
  )
  const [model, setModel] = useState<ResearchStoreContent['model']>(
    researchProps.model
  )
  const [sourcesForMessages, setSourcesForMessages] = useState<
    ResearchStoreContent['sourcesForMessages']
  >(researchProps.sourcesForMessages)

  // 5. Chat logic (from `ai/react`)
  const {
    input,
    setInput,
    handleInputChange,
    handleSubmit,
    isLoading,
    messages,
    setMessages
  } = useChat({
    api: `/api/gpt/${model}`,
    body: {
      metadata: {
        court,
        year,
        sources
      },
      namespace,
      researchType: props.researchType,
      summarise: eventQuery === RESEARCH_QUERY_TYPE.SUMMARISE
    },
    onResponse: (response) => {
      const sourcesHeader = response.headers.get('x-sources')
      const responseSources = sourcesHeader
        ? JSON.parse(atob(sourcesHeader))
        : []
      const messageIndexHeader = response.headers.get('x-message-index')
      const newNamespace = response.headers.get('x-namespace')

      // Log sources for admin
      admin.log(props.user.userType, [responseSources])

      // Update namespace if returned from the response
      if (newNamespace) {
        setNamespace(newNamespace)
      }

      // If we have sources, keep track of them
      if (responseSources.length && messageIndexHeader !== null) {
        // Add sources to global state
        setSources((prev) => [
          ...prev,
          ...responseSources.map(
            (source: { refId: string; docId: string; data: any[] }) =>
              source.docId
          )
        ])

        // Store them in local state for the specific message
        setSourcesForMessages((prev) => ({
          ...prev,
          [messageIndexHeader]: responseSources
        }))
      }
    },
    onFinish: () => {
      setFinished(true)
      setEventQuery(null)
    },
    onError: () => {
      toast({
        title: 'Something went wrong.',
        description: 'Your message was not sent. Please try again.',
        variant: 'destructive'
      })
    }
  })

  useEffect(() => {
    if (finished) {
      setFinished(false)
      storeResearch(messages)
    }
  }, [messages, finished])

  // Sync local loading states
  useEffect(() => {
    setChatEndpointIsLoading(isLoading)
  }, [isLoading])

  // If researchProps already has messages and sources, initialize them
  useEffect(() => {
    if (researchProps.messages && researchProps.messages.length > 0) {
      setMessages(researchProps.messages)
      setSources(researchProps.sources)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [researchProps])

  // Store research data to DB
  const { storeResearch } = useStoreResearch({
    user: props.user,
    researchId: researchProps.researchId,
    court,
    year,
    model,
    sources,
    sourcesForMessages,
    namespace,
    masquerade: props.masquerade || false,
    researchType: props.researchType
  })

  // 7. Handlers split out into smaller hooks
  // 7a. Sending messages
  const { sendMessage } = useChatHandlers({
    messageContainerRef,
    messages,
    chatEndpointIsLoading,
    handleSubmit
  })

  // 7b. Handling query clarification
  const { handleQueryClarification } = useQueryClarification({
    input,
    setInput,
    chatEndpointIsLoading,
    setChatEndpointIsLoading,
    researchType: props.researchType,
    messages,
    sources,
    court,
    year,
    namespace,
    setQuestions,
    setOpenQueryClarification
  })

  // 7c. Updating source
  const { updateSource } = useSourceUpdater({
    setSources,
    setSourceName,
    setEventQuery,
    setInput,
    researchType: props.researchType
  })

  // 8. Derived effect: If we have set an event query like "summarize" or "ask", auto-trigger send
  useEffect(() => {
    const shouldClickSubmit =
      input.includes('Summarize the case:') ||
      (input.includes('Summarize the document:') &&
        sourceName &&
        sources.length === 1) ||
      eventQuery === RESEARCH_QUERY_TYPE.ASK

    if (shouldClickSubmit) {
      document.getElementById('submit-button')?.click()
    }
  }, [sources, sourceName, eventQuery, input])

  // 9. Return everything needed by your component
  return {
    // Refs
    messageContainerRef,

    // Chat fields
    input,
    handleInputChange,
    sendMessage,
    chatEndpointIsLoading,
    messages,

    // Source logic
    updateSource,
    sourceName,
    sources,
    setSourceName,
    sourcesForMessages,
    setSources,
    model,
    setModel,

    // Court / year
    court,
    setCourt,
    startYear,
    setStartYear,
    endYear,
    setEndYear,

    // Query clarification
    openQueryClarification,
    setOpenQueryClarification,
    setInput,
    setEventQuery,
    questions,
    setQuestions,
    handleQueryClarification
  }
}
