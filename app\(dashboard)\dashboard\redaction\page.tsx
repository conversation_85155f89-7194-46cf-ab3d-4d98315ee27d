import { DashboardShell } from '@/components/elements/layout/shell'
import { DashboardHeader } from '@/components/elements/layout/header'
import { features } from '@/config/dashboard'
import { RedactionPanel } from '@/components/elements/redaction/redaction-panel'

export const metadata = features['redactionTool']

export default function Page() {
  return (
    <DashboardShell>
      <DashboardHeader heading={metadata.title} text={metadata.description} />
      <RedactionPanel />
    </DashboardShell>
  )
}
