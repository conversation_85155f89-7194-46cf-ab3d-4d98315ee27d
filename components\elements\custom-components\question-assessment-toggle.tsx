'use client'

import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { useState } from 'react'
import { toast } from '../../ui/use-toast'
import { updateQuestionAssessmentSetting } from '@/lib/actions/research'

const QuestionAssessmentToggle = ({
  userId,
  questionAssessmentSetting
}: {
  userId: string
  questionAssessmentSetting: boolean
}) => {
  const [questionAssessment, setQuestionAssessment] = useState<boolean>(
    questionAssessmentSetting
  )

  const handleQuestionAssessmentSettingsUpdate = async (newState: boolean) => {
    try {
      setQuestionAssessment(newState)
      const response = await updateQuestionAssessmentSetting({
        userId: userId,
        questionAssessmentSetting: newState
      })
      if (response.ok) {
        toast({
          title: 'Settings updated successfully',
          description: 'Your question assessment setting is updated'
        })
      } else throw new Error('Failed to update setting')
    } catch (error) {
      console.error('Error:', error)
      toast({
        title: 'Failed to update settings',
        description:
          'An error occurred while updating question assessment setting',
        variant: 'destructive'
      })
      setQuestionAssessment(!newState)
    }
  }

  return (
    <div className="flex items-center gap-4">
      <Label htmlFor="question-assessment" className="text-sm font-medium">
        Question Assessment
      </Label>
      <Switch
        id="question-assessment"
        checked={questionAssessment}
        onCheckedChange={handleQuestionAssessmentSettingsUpdate}
      />
    </div>
  )
}

export default QuestionAssessmentToggle
