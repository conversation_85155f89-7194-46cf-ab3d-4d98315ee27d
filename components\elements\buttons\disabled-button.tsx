'use client'

import * as React from 'react'

import { cn } from '@/lib/utils'
import { ButtonProps, buttonVariants } from '@/components/ui/button'

interface DisabledButtonProps extends ButtonProps {
  buttonText: string
}

export function DisabledButton({
  buttonText,
  className,
  variant,
  ...props
}: DisabledButtonProps) {
  return (
    <button
      className={cn(
        buttonVariants({ variant }),
        'cursor-not-allowed',
        className
      )}
      disabled
      {...props}
    >
      {buttonText}
    </button>
  )
}
