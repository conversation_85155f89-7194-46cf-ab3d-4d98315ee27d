import type { NextApiRequest, NextApiResponse } from 'next'
import { db } from '@/lib/db'
import { env } from '@/env.mjs'
import { DocumentRecords, Region } from '@prisma/client'
import { cleanUpString } from '@/lib/utils'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (env.NODE_ENV !== 'development') {
      throw new Error('Unauthorized')
    }

    const CHUNK_SIZE = 200
    const BATCH_SIZE = 500
    let lastId = req.query.lastId ? parseInt(req.query.lastId as string) : 0
    let endId = req.query.endId ? parseInt(req.query.endId as string) : 2691175

    try {
      for (let j = 0; j < 3000000; j += BATCH_SIZE) {
        const records = await db.documentRecords.findMany({
          select: {
            id: true,
            content: true
          },
          where: {
            id: {
              gt: lastId,
              lt: endId
            }
            // source: {
            //   notIn: teams.map((team) => team.id)
            // },
            // region: Region.US
          },
          take: BATCH_SIZE,
          orderBy: {
            id: 'asc'
          }
        })

        if (records.length === 0) {
          break
        }

        lastId = records[records.length - 1].id
        console.log('Total records: ', records.length, ' in batch: ', j)
        const size = records.length

        const startTimestamp = new Date().getTime()
        for (let i = 0; i < records.length; i += CHUNK_SIZE) {
          console.log('Processing chunk: ', i, ' to ', i + CHUNK_SIZE, ' of ', size) // prettier-ignore
          const chunk = records.slice(i, i + CHUNK_SIZE)
          const promises = chunk.map((record) => updateCleanedContent(record))
          await Promise.allSettled(promises)
        }
        const endTimestamp = new Date().getTime()
        const duration = (endTimestamp - startTimestamp) / 1000
        const chunkBatchesRemaining = Math.ceil((endId - j) / BATCH_SIZE)
        const estimatedTimeRemainingMins = (
          (chunkBatchesRemaining * duration) /
          60
        ).toFixed(2)
        console.info(`Chunk processed in ${duration} seconds. Estimated time remaining: ${estimatedTimeRemainingMins} mins of batch ending at ${lastId}`) // prettier-ignore
      }
    } catch (error: any) {
      console.log('error: ', error.message)
    }
    console.log('\n\n<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>\n')
    console.log('FINISHED')
    console.log('\n<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>\n')

    res.status(200).json({ message: 'Data saved successfully!' })
  } catch (error) {
    res.status(500).json({ error: 'Failed to index.', errorcontent: error })
  }
}

async function updateCleanedContent(
  record: Pick<DocumentRecords, 'id' | 'content'>
) {
  const cleanedHtml = cleanUpString(record.content)
  await db.documentRecords.update({
    where: {
      id: record.id
    },
    data: {
      content: cleanedHtml
    }
  })
}
