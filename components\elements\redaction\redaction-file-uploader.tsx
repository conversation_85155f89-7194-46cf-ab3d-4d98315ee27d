'use client'

import React, { useCallback } from 'react'
import { useDropzone, FileRejection, DropzoneOptions } from 'react-dropzone'
import { toast } from '@/components/ui/use-toast'

interface RedactionFileUploaderProps {
  onFileSelected: (file: File | null) => void
}

export function RedactionFileUploader({
  onFileSelected
}: RedactionFileUploaderProps) {
  const MAX_FILE_SIZE = 10 * 1024 * 1024

  const onDrop = useCallback(
    (acceptedFiles: File[], fileRejections: FileRejection[]) => {
      fileRejections.forEach((fileRejection) => {
        toast({
          title: 'File Rejected',
          description: `File rejected - ${fileRejection.file.name}: Size exceeds 5 MB or invalid type.`,
          variant: 'destructive'
        })
      })

      onFileSelected(acceptedFiles[0])
    },
    []
  )

  const dropzoneOptions: DropzoneOptions = {
    onDrop,
    maxSize: MAX_FILE_SIZE,
    accept: {
      'application/pdf': ['.pdf']
    }
  }

  const { getRootProps, getInputProps } = useDropzone(dropzoneOptions)

  return (
    <div>
      <div
        {...getRootProps()}
        className="border-2 border-dashed border-gray-400 px-4 py-16 mb-5 rounded-md cursor-pointer"
      >
        <input {...getInputProps()} />
        <p className="text-sm text-center">
          Drag &apos;n&apos; drop your PDF here, or click to select file.
        </p>
      </div>
    </div>
  )
}
