'use client'

import { useState, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import DocumentGeneration from './doc-generation'
import { Loader2, Save } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { Binder, CaseFile } from '@prisma/client'
import { CopyButton, DownloadButton } from '../document/document-actions'
import { useRouter } from 'next/navigation'
import { TipTapEditor } from '@/components/external/tiptap/tiptap-editor'
import {
  handleDemandLetterGeneration,
  updateDemandLetter
} from '@/lib/actions/case/demand-letter'
import { cn } from '@/lib/utils'
import { DocumentTitle } from '@/types/case'

const STEPS = {
  SELECT: 'step1',
  GENERATE: 'step2'
} as const

export default function DemandLetterGenerator({
  binder,
  demandLetter,
  usageStats = {
    available: 0,
    used: 0
  },
  refDocuments
}: {
  binder: Binder
  demandLetter: CaseFile | null
  usageStats?: {
    available: number
    used: number
  }
  refDocuments: DocumentTitle[]
}) {
  const router = useRouter()

  const [generatedDocument, setGeneratedDocument] = useState<string | null>(
    demandLetter?.content || null
  )
  const [isGenerating, setIsGenerating] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [editedDocument, setEditedDocument] = useState<string | null>(null)

  // Handler for document generation
  const handleFinalizeUploads = useCallback(async () => {
    // Prevent multiple submissions
    if (isGenerating) return

    // Update UI state for generation process
    setGeneratedDocument(null)
    setIsGenerating(true)

    try {
      const result = await handleDemandLetterGeneration(binder.id)

      if (result && result.combinedContent) {
        router.refresh()
        setGeneratedDocument(result.combinedContent)
        toast({
          title: 'Demand letter generated successfully',
          description: 'Your demand letter has been generated'
        })
      } else {
        throw new Error('Failed to generate demand letter')
      }
    } catch (error) {
      console.error('Error generating demand letter:', error)
      toast({
        title: 'Failed to generate demand letter',
        description:
          'An error occurred while generating your demand letter. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsGenerating(false)
    }
  }, [binder.id, isGenerating])

  // Handle saving changes to the generated document
  const handleSaveChanges = useCallback(async () => {
    if (!demandLetter?.id || !editedDocument) return

    setIsSaving(true)
    try {
      const result = await updateDemandLetter(demandLetter.id, editedDocument)

      if (result?.binderId) {
        setGeneratedDocument(editedDocument)
        setHasUnsavedChanges(false)
        router.refresh()
        toast({
          title: 'Changes saved successfully',
          description: 'Your changes have been saved'
        })
      } else {
        throw new Error('Failed to save changes')
      }
    } catch (error) {
      console.error('Error saving changes:', error)
      toast({
        title: 'Failed to save changes',
        description: 'An error occurred while saving your changes',
        variant: 'destructive'
      })
    } finally {
      setIsSaving(false)
    }
  }, [demandLetter?.id, editedDocument, router])

  // Handle content changes from the editor
  const handleContentChange = useCallback(
    (newContent: string) => {
      setEditedDocument(newContent)
      setHasUnsavedChanges(newContent !== generatedDocument)
    },
    [generatedDocument]
  )

  const allowDemandLetterGeneration = usageStats.used < usageStats.available

  return (
    <div className="space-y-4">
      {generatedDocument && (
        <>
          <div className="flex justify-end gap-4 mt-4">
            <Button
              onClick={handleSaveChanges}
              disabled={isSaving || !hasUnsavedChanges}
              variant="outline"
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
              {isSaving ? (
                <Loader2
                  className="ml-2 h-4 w-4 animate-spin"
                  aria-hidden="true"
                />
              ) : (
                <Save className="ml-2 h-4 w-4" aria-hidden="true" />
              )}
            </Button>
            <CopyButton content={editedDocument || generatedDocument} />
            <DownloadButton
              content={editedDocument || generatedDocument}
              filename={`${binder.name} - Demand Letter'`}
            />
          </div>
          <TipTapEditor
            content={generatedDocument}
            setContent={handleContentChange}
            isEditable={true}
            refDocuments={refDocuments}
          />
        </>
      )}

      {isGenerating ? (
        <DocumentGeneration
          document={generatedDocument}
          feature="demandLetterGeneration"
        />
      ) : (
        <Button
          onClick={handleFinalizeUploads}
          disabled={isGenerating || !allowDemandLetterGeneration}
          aria-busy={isGenerating}
          className={cn(
            generatedDocument ? '' : 'w-full h-16 text-lg rounded-lg mt-16'
          )}
        >
          {isGenerating
            ? 'Generating...'
            : !allowDemandLetterGeneration
              ? 'Out of Credits'
              : generatedDocument
                ? 'Regenerate Demand Letter'
                : 'Generate Demand Letter'}
          {isGenerating && (
            <Loader2 className="ml-2 h-4 w-4 animate-spin" aria-hidden="true" />
          )}
        </Button>
      )}
    </div>
  )
}
