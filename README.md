# LexLumen

**Seamless, Comprehensive, and Precise Legal Assistance for Indian Tax and Labour Laws**

Harness the power of AI to navigate legal landscapes. From research to contract revisions, we've got you covered.


## Features

Uncover a range of meticulously crafted legal tools tailored to meet the diverse needs of modern legal practices. Experience a blend of precision and efficiency as you navigate through:

- **Legal Research:** Access extensive databases and AI-powered insights.
- **Document Review:** Streamline the review process with automated suggestions.
- **Contract Revision:** Efficiently draft and revise contracts with AI assistance.
- **Database Search:** Quickly find relevant case law and legal precedents.


## Project Setup

### Requirements

Ensure you have the following installed:

- Node.js (version 20.x or above)
- npm

### Installation

Clone the repository and install the dependencies:

- `git clone https://github.com/your-username/lexlumen.git`
- `cd lexlumen`
- `npm install`

## Scripts

### Development Server
Starts the development server on port 3011.

`npm run dev`

### Build
Compiles the application for production.

`npm run build`

### Start
Starts the production server.

`npm run start`

### Linting
Runs ESLint to check for code quality.

`npm run lint`


### Prettier
Formats your TypeScript files according to the .prettierrc configuration.

`npm run prettier`


## Update Database

Pushes schema changes to the database and regenerates Prisma client.

`npm run update-db`
