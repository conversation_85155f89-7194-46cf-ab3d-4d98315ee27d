// <PERSON><PERSON>t to add missing case evaluation prompts to the database
const { PrismaClient } = require('@prisma/client');

const db = new PrismaClient();

const missingPrompts = [
  {
    source: 'AI_CASE_EVALUATION_PAST_MEDICAL_EXPENSES',
    role: 'SYSTEM',
    prompt: `Generate a detailed 'Past Medical Expenses' section with the following specific components and format:

        1) Begin with the heading 'Past Medical Expenses' in bold, followed by a line break

        2) Start with this opening sentence: 'To date, Mr./Ms. [LAST NAME] has incurred medical expenses as itemized below.'

        3) Create a comprehensive, properly formatted table with these exact columns:
           - Provider (name of medical provider/facility)
           - Date of Service (either specific date or date range in format 'MM/DD/YYYY' or 'MM/DD/YYYY to MM/DD/YYYY')
           - Amount Charged (dollar amount formatted with dollar sign, commas for thousands, and two decimal places)
           - Supporting Document(s) (reference to specific exhibit numbers)

        4) Include every medical provider mentioned in the MEDICAL_CHRONOLOGY, organized chronologically by first date of service

        5) For providers with multiple visits over a period, use the format:
           - Provider: Full facility/practice name
           - Date of Service: 'MM/DD/YYYY to MM/DD/YYYY'
           - Amount: Total for all services
           - Supporting Document(s) / Exhibits

        6) At the bottom of the table, include a 'Total' row that correctly sums all amounts

        7) After the table, include any necessary explanatory notes about potentially missing documentation, formatted as:
           '[NTD: As noted in the medical summary for [Provider Name], we believe there is missing documentation for [description of what's missing], which may include additional medical billing.]'

        8) Conclude with this exact paragraph:
           'If you dispute any of Mr./Ms. [LAST NAME]'s medical treatment or bills as unnecessary or unreasonable, please specify the disputed items in writing. Otherwise, we will assume you agree with the necessity and reasonableness of Mr./Ms. [LAST NAME]'s medical treatments and bills.'

        9) Format the table with:
           - Clear borders around all cells
           - Column headers in bold
           - Consistent cell padding
           - Proper alignment (left-align text, right-align numbers)
           - Proper spacing between table rows

        Ensure all dollar amounts are formatted consistently and match the totals referenced elsewhere in the document. The table should be comprehensive, accurate, and professional in appearance, exactly matching the format seen in the sample demand letter. All citations or exhibits must be provided as [doc-id:page] at the line, example: [123:2].

        Context data: {{context}}`,
    expectedOutput: `# Past Medical Expenses

To date, Mr./Ms. [LAST NAME] has incurred medical expenses as itemized below.

| Provider | Date of Service | Amount Charged | Supporting Document(s) |
|----------|----------------|----------------|------------------------|
| Emergency Medical Services | 06/15/2023 | $1,250.00 | [123:2] |
| Regional Medical Center | 06/15/2023 to 06/17/2023 | $15,750.00 | [124:1-5] |
| Dr. Smith Orthopedics | 06/20/2023 to 12/15/2023 | $8,500.00 | [125:1-12] |
| Physical Therapy Associates | 07/01/2023 to 11/30/2023 | $4,200.00 | [126:1-8] |
| **Total** | | **$29,700.00** | |

If you dispute any of Mr./Ms. [LAST NAME]'s medical treatment or bills as unnecessary or unreasonable, please specify the disputed items in writing. Otherwise, we will assume you agree with the necessity and reasonableness of Mr./Ms. [LAST NAME]'s medical treatments and bills.`
  },
  {
    source: 'AI_CASE_EVALUATION_FUTURE_MEDICAL_EXPENSES',
    role: 'SYSTEM',
    prompt: `Create the 'Future Medical Expenses' section with these specific elements and formatting:

        1) Begin with the heading 'Future Medical Expenses' in bold, followed by a line break

        2) Open with this sentence: 'Mr./Ms. [LAST NAME] will require additional future treatment as identified below.'

        3) Create a detailed table with these exact columns:
           - Procedure (name of the medical procedure/treatment/appointment)
           - Years (duration for which the treatment is needed, expressed as a number)
           - Per Year (frequency of treatment needed each year, expressed as a number)
           - Cost (cost per procedure, formatted with dollar sign, commas for thousands, and two decimal places)
           - Total (calculated by multiplying Years × Per Year × Cost, formatted with dollar sign, commas, and two decimal places)

        4) Include these specific categories of future medical expenses (if applicable based on the context):
           - Follow-up appointments with specialists (orthopedic, neurology, pain management, etc.)
           - Physical therapy or rehabilitation sessions
           - Diagnostic imaging (MRIs, X-rays, CT scans)
           - Injections or pain management procedures
           - Surgical interventions
           - Medication costs
           - Assistive devices or home modifications
           - Chiropractic care or alternative treatments

        5) For each row, include a superscript footnote number at the end of the procedure name that corresponds to the source document

        6) Calculate and include a 'Total' row at the bottom that correctly sums all amounts in the Total column

        7) After the table, include this exact paragraph:
           'Healthcare and medication costs are expected to rise, and we reserve the right to update or extend our estimate to account for further care needs.'

        8) After this paragraph, include footnotes that cite the source documents for each future expense, formatted as:
           'Exhibit - [Provider Name] - [Document Type] [doc-id:page]'
           'Exhibit - [Provider Name] - [Document Type] [doc-id:page]'
           etc.

        9) Format the table with:
           - Clear borders around all cells
           - Column headers in bold and centered
           - Proper alignment (left-align text, right-align numbers)
           - Consistent cell padding and spacing

        Ensure all projected treatments are based on physician recommendations documented in the medical records. The calculations should be mathematically correct, and the formatting should exactly match the sample demand letter's future medical expenses section. All citations or exhibits must be provided as [doc-id:page] at the line, example: [123:2].

        Context data: {{context}}`,
    expectedOutput: `# Future Medical Expenses

Mr./Ms. [LAST NAME] will require additional future treatment as identified below.

| Procedure | Years | Per Year | Cost | Total |
|-----------|-------|----------|------|-------|
| Orthopedic Follow-up¹ | 5 | 2 | $350.00 | $3,500.00 |
| Physical Therapy² | 2 | 12 | $125.00 | $3,000.00 |
| MRI Imaging³ | 3 | 1 | $1,200.00 | $3,600.00 |
| Pain Management Injections⁴ | 3 | 3 | $800.00 | $7,200.00 |
| **Total** | | | | **$17,300.00** |

Healthcare and medication costs are expected to rise, and we reserve the right to update or extend our estimate to account for further care needs.

¹ Exhibit - Dr. Smith Orthopedics - Treatment Plan [125:8]
² Exhibit - Physical Therapy Associates - Recommendation [126:9]
³ Exhibit - Regional Medical Center - Imaging Protocol [124:6]
⁴ Exhibit - Pain Management Clinic - Treatment Plan [127:3]`
  }
];

async function addMissingPrompts() {
  try {
    console.log('Adding missing case evaluation prompts...');
    
    for (const promptData of missingPrompts) {
      // Check if prompt already exists
      const existing = await db.prompt.findFirst({
        where: { source: promptData.source }
      });
      
      if (existing) {
        console.log(`Prompt ${promptData.source} already exists, skipping...`);
        continue;
      }
      
      // Create the prompt
      await db.prompt.create({
        data: {
          source: promptData.source,
          role: promptData.role,
          prompt: promptData.prompt,
          expectedOutput: promptData.expectedOutput,
          variables: {}
        }
      });
      
      console.log(`✓ Added prompt: ${promptData.source}`);
    }
    
    console.log('✅ All missing prompts have been added successfully!');
  } catch (error) {
    console.error('❌ Error adding prompts:', error);
  } finally {
    await db.$disconnect();
  }
}

addMissingPrompts();
