// 1. requestEventBasedMedicalChronology()
// ├── getCurrentUser()
// ├── db.$transaction()
// └── Creates QueuedEventProcess entry

// 2. processEventBasedMedicalChronology() [Background Process]
// ├── cleanupExistingRecords() [New cleanup step]
// ├── extractEventsFromDocuments()
// │ ├── db.documentRecords.findMany()
// │ └── For each document:
// │ └── processDocumentForEvents()
// │ ├── splitDocumentIntoChunks()
// │ ├── calculatePageRange()
// │ ├── extractEventsFromChunk()
// │ │ └── createLLMCompletion() [LLM call]
// │ └── db.documentEvent.create() [multiple]
// │
// ├── deduplicateEvents()
// │ ├── db.documentEvent.findMany()
// │ ├── chunkArray()
// │ └── For each batch:
// │ └── deduplicateBatch()
// │ ├── createLLMCompletion() [LLM call]
// │ ├── db.documentEvent.update() [merge]
// │ ├── db.documentEvent.deleteMany() [duplicates]
// │ └── db.documentEvent.updateMany() [mark processed]
// │
// ├── extractPlaintiffInfo()
// │ ├── db.documentEvent.findMany()
// │ └── createLLMCompletion() [LLM call]
// │
// ├── generateChronologyReports()
// │ ├── db.documentEvent.findMany()
// │ ├── generateTreatmentCalendar() [Pure code logic]
// │ │ ├── extractProvider()
// │ │ ├── mapEventTypeToDepartment()
// │ │ ├── getDepartmentEmoji()
// │ │ └── formatDateRange()
// │ ├── generateTimelineMarkdown() [Pure code logic]
// │ ├── generateFullMarkdownReport()
// │ │ └── createLLMCompletion() [LLM call]
// │ └── db.processedChronology.upsert() [2x - plaintiff & chronology]
// │
// ├── db.teamCreditUsed.create()
// └── db.queuedEventProcess.update() [completion status]

// 3. checkEventChronologyStatus() [Status Check]
// ├── db.processedChronology.findUnique() [chronology]
// └── db.processedChronology.findUnique() [plaintiff info]
