import Script from 'next/script'

export const ZohoSalesIQ = () => {
  return (
    <>
      <Script id="zoho-init" strategy="afterInteractive">
        {`window.$zoho=window.$zoho || {};$zoho.salesiq=$zoho.salesiq||{ready:function(){}}`}
      </Script>
      <Script
        id="zsiqscript"
        src={`https://salesiq.zohopublic.in/widget?wc=${process.env.ZOHO_SALESIQ_WC}`}
        strategy="afterInteractive"
      />
    </>
  )
}
