// lib/actions/case/case-eval.ts

'use server'

import { CaseFileType } from '@prisma/client'
import { db } from '../../db'
import { UnauthorizedError } from '../../exceptions'
import { getCurrentUser } from '../../session'
import { generateCaseEvaluation } from '@/lib/processes/case-eval-generator'
import { AttorneyInsightsFormData } from '@/components/elements/doc-selector/attorney-strategy-form'
import { CaseEvaluationOptions } from '@/types/case'

export async function handleCaseEvaluationGeneration({
  caseId,
  binderId,
  selectedDocumentsByType,
  attorneyInsights,
  options
}: {
  caseId: string
  binderId: string
  selectedDocumentsByType: Record<string, string[]>
  attorneyInsights: AttorneyInsightsFormData | null
  options?: CaseEvaluationOptions
}) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    // Store the selection in the database
    const store = await db.caseFile.findUnique({
      where: {
        binderId_fileType: {
          binderId: binderId,
          fileType: CaseFileType.CASE_EVALUATION
        }
      }
    })

    let caseFileId = ''
    if (store) {
      await db.caseFile.update({
        where: {
          id: store.id
        },
        data: {
          selectedDocumentsByType
        }
      })
      caseFileId = store.id
    } else {
      const createCaseFile = await db.caseFile.create({
        data: {
          binderId,
          fileType: CaseFileType.CASE_EVALUATION,
          creatorId: user.id,
          selectedDocumentsByType
        }
      })
      caseFileId = createCaseFile.id
    }

    // Extract all document IDs and convert to numbers
    const allDocumentIds = Object.values(selectedDocumentsByType)
      .flat()
      .map(Number)

    // Fetch all documents in a single query
    const documents = await db.documentRecords.findMany({
      where: {
        id: {
          in: allDocumentIds
        }
      },
      select: {
        id: true,
        title: true,
        indexed: true,
        content: true
      }
    })

    // Map content by document type
    const documentContentByType = Object.fromEntries(
      Object.entries(selectedDocumentsByType).map(([docType, docIds]) => [
        docType,
        documents.filter((doc) => docIds.includes(doc.id.toString()))
      ])
    )

    // Generate the case evaluation
    const caseEvaluation = await generateCaseEvaluation(
      caseId,
      documentContentByType,
      user,
      attorneyInsights,
      options
    )

    // Store attorney insights if provided
    if (attorneyInsights) {
      await db.caseFile.upsert({
        where: {
          binderId_fileType: {
            binderId,
            fileType: CaseFileType.ATTORNEY_STRATEGY_PLAN
          }
        },
        update: {
          content: JSON.stringify(attorneyInsights),
          updatedAt: new Date()
        },
        create: {
          binderId,
          creatorId: user.id,
          fileType: CaseFileType.ATTORNEY_STRATEGY_PLAN,
          content: JSON.stringify(attorneyInsights)
        }
      })
    }

    // Store the extracted financial data
    if (caseEvaluation.extractedFinancialData) {
      await db.caseFile.upsert({
        where: {
          binderId_fileType: {
            binderId,
            fileType: CaseFileType.EXTRACTED_FINANCIAL_DATA
          }
        },
        update: {
          content: JSON.stringify(caseEvaluation.extractedFinancialData),
          updatedAt: new Date()
        },
        create: {
          binderId,
          creatorId: user.id,
          fileType: CaseFileType.EXTRACTED_FINANCIAL_DATA,
          content: JSON.stringify(caseEvaluation.extractedFinancialData)
        }
      })
    }

    // Define all the case evaluation components to store
    const caseFileEntries: {
      fileType: CaseFileType
      content: string
    }[] = [
      {
        fileType: CaseFileType.CASE_EVALUATION,
        content:
          caseEvaluation.revisedMarkdownReport ||
          caseEvaluation.initialMarkdownReport
      },
      {
        fileType: CaseFileType.LIABILITY_ASSESSMENT,
        content: JSON.stringify(caseEvaluation.liabilityAssessment)
      },
      {
        fileType: CaseFileType.ECONOMIC_DAMAGES,
        content: JSON.stringify(caseEvaluation.economicDamages)
      },
      {
        fileType: CaseFileType.NON_ECONOMIC_DAMAGES,
        content: JSON.stringify(caseEvaluation.nonEconomicDamages)
      },
      {
        fileType: CaseFileType.PUNITIVE_DAMAGES,
        content: JSON.stringify(caseEvaluation.punitiveDamagesData)
      },
      {
        fileType: CaseFileType.DAMAGES_CALCULATION,
        content: JSON.stringify(caseEvaluation.damagesCalculation)
      },
      {
        fileType: CaseFileType.RISK_ASSESSMENT,
        content: JSON.stringify(caseEvaluation.riskAssessment)
      },
      {
        fileType: CaseFileType.LITIGATION_STRATEGY,
        content: JSON.stringify(caseEvaluation.litigationStrategy)
      }
    ]

    // Add defense perspective if available
    if (caseEvaluation.defensePerspective) {
      caseFileEntries.push({
        fileType: CaseFileType.DEFENSE_PERSPECTIVE,
        content: JSON.stringify(caseEvaluation.defensePerspective)
      })
    }

    // Store all components in the database
    await Promise.all(
      caseFileEntries.map(async ({ fileType, content }) => {
        await db.caseFile.upsert({
          where: {
            binderId_fileType: { binderId, fileType }
          },
          update: {
            content,
            updatedAt: new Date()
          },
          create: {
            binderId,
            creatorId: user.id,
            fileType,
            content
          }
        })
      })
    )

    await db.teamCreditUsed.create({
      data: {
        teamId: user.teamId,
        type: 'case',
        refId: caseFileId,
        eventId: new Date().getTime().toString()
      }
    })

    return {
      success: true,
      caseEvaluation
    }
  } catch (error) {
    console.error('Failed to process case evaluation documents:', error)
    return {
      success: false,
      error: 'Failed to process documents'
    }
  }
}

/**
 * Updates the extracted financial data and recalculates damages
 */
export async function updateFinancialDataAndRecalculate(
  binderId: string,
  updatedFinancialData: any,
  nonEconomicMultiplierOverride?: number,
  punitiveMultiplierOverride?: number
) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    // Import the calculator function dynamically to avoid circular dependencies
    const { processAndCalculateDamages } = await import(
      '@/lib/processes/case-eval-generator/damage-calculator'
    )

    // Update the stored financial data
    await db.caseFile.upsert({
      where: {
        binderId_fileType: {
          binderId,
          fileType: CaseFileType.EXTRACTED_FINANCIAL_DATA
        }
      },
      update: {
        content: JSON.stringify(updatedFinancialData),
        updatedAt: new Date()
      },
      create: {
        binderId,
        creatorId: user.id,
        fileType: CaseFileType.EXTRACTED_FINANCIAL_DATA,
        content: JSON.stringify(updatedFinancialData)
      }
    })

    // Recalculate damages based on the updated data
    const {
      economicDamages,
      nonEconomicDamages,
      punitiveDamagesData,
      damagesCalculation
    } = processAndCalculateDamages(
      updatedFinancialData,
      nonEconomicMultiplierOverride,
      punitiveMultiplierOverride
    )

    // Update all related damage calculations
    const caseFileEntries = [
      {
        fileType: CaseFileType.ECONOMIC_DAMAGES,
        content: JSON.stringify(economicDamages)
      },
      {
        fileType: CaseFileType.NON_ECONOMIC_DAMAGES,
        content: JSON.stringify(nonEconomicDamages)
      },
      {
        fileType: CaseFileType.PUNITIVE_DAMAGES,
        content: JSON.stringify(punitiveDamagesData)
      },
      {
        fileType: CaseFileType.DAMAGES_CALCULATION,
        content: JSON.stringify(damagesCalculation)
      }
    ]

    // Update all components in the database
    await Promise.all(
      caseFileEntries.map(async ({ fileType, content }) => {
        await db.caseFile.upsert({
          where: {
            binderId_fileType: { binderId, fileType }
          },
          update: {
            content,
            updatedAt: new Date()
          },
          create: {
            binderId,
            creatorId: user.id,
            fileType,
            content
          }
        })
      })
    )

    return {
      success: true,
      economicDamages,
      nonEconomicDamages,
      punitiveDamagesData,
      damagesCalculation
    }
  } catch (error) {
    console.error('Failed to update financial data and recalculate:', error)
    return {
      success: false,
      error: 'Failed to update and recalculate'
    }
  }
}

/**
 * Saves updated content for a case evaluation
 */
export async function saveCaseEvaluationContent(
  caseId: string,
  content: string
) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    // Update the case evaluation content
    const updatedEvaluation = await db.caseFile.update({
      where: { id: caseId },
      data: { content }
    })

    return {
      success: true,
      caseEvaluation: updatedEvaluation
    }
  } catch (error) {
    console.error('Failed to save case evaluation content:', error)
    return {
      success: false,
      error: 'Failed to save case evaluation content'
    }
  }
}
