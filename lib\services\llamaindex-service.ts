import { LlamaParseReader, Document as LLamaDocument } from 'llamaindex'
import { marked } from 'marked'

// Add proper error handling and text encoding validation in extractFileContentAndHtmlWithLlamaParse
export async function extractFileContentAndHtmlWithLlamaParse({
  file
}: {
  file: {
    buffer: Buffer
    type: string
    name: string
  }
}) {
  console.log('Extracting content from file:', file.name)

  const reader = new LlamaParseReader({
    resultType: 'markdown',
    premiumMode: false,
    splitByPage: false,
    output_tables_as_HTML: true
  })

  const buffer = new Uint8Array(file.buffer)

  try {
    const documentPages: LLamaDocument[] =
      await reader.loadDataAsContent(buffer)

    if (documentPages.length === 0) {
      return {
        textContent: '',
        htmlContent: ''
      }
    }

    // Sanitize text content to remove invalid Unicode sequences
    let textContent = documentPages
      .map((document) => document.getText())
      .join('\n')

    // Replace invalid surrogate pairs
    const invalidSurrogateRegex =
      '[\\ud800-\\udbff](?![\\udc00-\\udfff])|(?:[^\\ud800-\\udbff]|^)[\\udc00-\\udfff]'
    const re = new RegExp(invalidSurrogateRegex, 'g')
    textContent = textContent.replace(re, '')

    // Generate HTML safely
    let htmlContent = ''
    try {
      htmlContent = await marked(textContent)
    } catch (htmlError) {
      console.error('Error generating HTML:', htmlError)
      htmlContent = '<p>Error rendering document content</p>'
    }

    return { textContent, htmlContent }
  } catch (error) {
    console.error('Error parsing document:', error)
    return {
      textContent: '',
      htmlContent: ''
    }
  }
}
