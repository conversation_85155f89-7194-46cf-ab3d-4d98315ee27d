import { createEnv } from '@t3-oss/env-nextjs'
import { z } from 'zod'

export const env = createEnv({
  // 1. The server schema
  server: {
    // Typically used in development & production for NextAuth
    NEXTAUTH_URL: z.string().url().default('http://localhost:3011'),
    NEXTAUTH_SECRET: z.string().min(1),

    // OAuth
    GOOGLE_ID: z.string().min(1),
    GOOGLE_SECRET: z.string().min(1),

    // Databases
    DATABASE_URL: z.string().min(1),
    POSTGRESQL_DATABASE_URL: z.string().min(1),

    // Email & SMTP
    SMTP_FROM: z.string().min(1),
    POSTMARK_API_TOKEN: z.string().min(1),
    POSTMARK_SIGN_IN_TEMPLATE: z.string().min(1),
    POSTMARK_ACTIVATION_TEMPLATE: z.string().min(1),

    // Third-party APIs
    OPENAI_API_KEY: z.string().min(1),
    GEMINI_API_KEY: z.string().min(1),

    // Azure OpenAI
    AZURE_OPENAI_API_KEY: z.string().min(1),
    AZURE_OPENAI_ENDPOINT: z.string().url(),

    SLACK_TOKEN: z.string().min(1),
    PINECONE_ENVIRONMENT: z.string().min(1),
    PINECONE_API_KEY: z.string().min(1),
    PINECONE_INDEX: z.string().min(1),

    // AWS
    AWS_KEYID: z.string().min(1),
    AWS_SECRET: z.string().min(1),
    AWS_BUCKET: z.string().min(1),
    AWS_REGION: z.string().min(1),

    // Misc
    PROCESS_SERVER: z.string().min(1),

    // Next.js
    NODE_ENV: z.string().min(1)
  },

  // 2. The client schema
  client: {
    NEXT_PUBLIC_APP_URL: z.string().min(1).default('http://localhost:3011'),
    NEXT_PUBLIC_BACKEND_SERVER: z
      .string()
      .min(1)
      .default('http://localhost:8000')
  },

  // 3. Provide the actual environment values for runtime validation
  runtimeEnv: {
    // Server-side
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    GOOGLE_ID: process.env.GOOGLE_ID,
    GOOGLE_SECRET: process.env.GOOGLE_SECRET,
    DATABASE_URL: process.env.DATABASE_URL,
    POSTGRESQL_DATABASE_URL: process.env.POSTGRESQL_DATABASE_URL,
    SMTP_FROM: process.env.SMTP_FROM,
    POSTMARK_API_TOKEN: process.env.POSTMARK_API_TOKEN,
    POSTMARK_SIGN_IN_TEMPLATE: process.env.POSTMARK_SIGN_IN_TEMPLATE,
    POSTMARK_ACTIVATION_TEMPLATE: process.env.POSTMARK_ACTIVATION_TEMPLATE,
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    SLACK_TOKEN: process.env.SLACK_TOKEN,
    PINECONE_ENVIRONMENT: process.env.PINECONE_ENVIRONMENT,
    PINECONE_API_KEY: process.env.PINECONE_API_KEY,
    PINECONE_INDEX: process.env.PINECONE_INDEX,

    // Azure OpenAI
    AZURE_OPENAI_API_KEY: process.env.AZURE_OPENAI_API_KEY,
    AZURE_OPENAI_ENDPOINT: process.env.AZURE_OPENAI_ENDPOINT,
    AWS_KEYID: process.env.AWS_KEYID,
    AWS_SECRET: process.env.AWS_SECRET,
    AWS_BUCKET: process.env.AWS_BUCKET,
    AWS_REGION: process.env.AWS_REGION,
    PROCESS_SERVER: process.env.PROCESS_SERVER,
    NODE_ENV: process.env.NODE_ENV,
    GEMINI_API_KEY: process.env.GEMINI_API_KEY,

    // Client-side
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_BACKEND_SERVER: process.env.PROCESS_SERVER
  }
})
