'use client'

import { buttonVariants } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { toast } from '@/components/ui/use-toast'

export const CopyHrefButton = ({ title = 'Share' }: { title?: string }) => {
  function handleOnClick() {
    navigator.clipboard.writeText(window.location.href)
    toast({
      description: 'Link copied to clipboard.'
    })
  }

  return (
    <button onClick={handleOnClick} className={cn(buttonVariants())}>
      <span>{title}</span>
    </button>
  )
}
