import { <PERSON><PERSON>Header } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { LegalQuery } from '@/components/elements/chat/legal-query'
import { features } from '@/config/dashboard'
import { db } from '@/lib/db'
import { PrivateUploadButton } from '@/components/elements/buttons/private-upload-button'
import { PrivateUploadInstructor } from '@/components/elements/private-upload-instructor'
import { RecentResearchList } from '@/components/elements/research/recent-research-list'
import type { ResearchStoreContent } from '@/types'
import type { AdminMasqueradeProps } from '../../layout'
import { PrivateResearchInfoCard } from '@/components/elements/custom-components/feature-info-card'
import { getMasqueradeUserNonNullable } from '@/lib/session'
import { ResearchType } from '@prisma/client'

export const metadata = features['researchPrivate']

export default async function ResearchPrivateStartPageMasquerade({
  params
}: AdminMasqueradeProps) {
  const user = await getMasqueradeUserNonNullable(params.userId)

  const researchHistory = await db.researchStore.findMany({
    where: {
      userId: user.id,
      source: user.teamId,
      region: user.region
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 5
  })

  const documents = await db.documentRecords.findMany({
    select: {
      id: true,
      title: true
    },
    where: {
      source: user.teamId,
      region: user.region
    }
  })

  const researchProps: ResearchStoreContent = {
    model: 'brainstem',
    sources: documents.map((doc) => doc.id.toString()),
    court: [],
    year: [],
    sourcesForMessages: {},
    sourceLabels: documents.map((doc) => ({
      id: doc.id.toString(),
      title: doc.title
    }))
  }

  return (
    <DashboardShell>
      <DashboardHeader heading={metadata.title} text={metadata.description}>
        <PrivateUploadButton />
      </DashboardHeader>
      <div className="grid gap-10">
        {documents.length > 0 ? (
          <LegalQuery
            researchProps={researchProps}
            user={user}
            researchType={ResearchType.private}
            namespace={user.teamId}
            showFilters={false}
            emptyStateComponent={<PrivateResearchInfoCard />}
          />
        ) : (
          <PrivateUploadInstructor user={user} />
        )}

        {researchHistory.length > 0 && (
          <RecentResearchList
            researchHistory={researchHistory}
            path={`/admin/users/${user.id}/masquerade/research-private`}
          />
        )}
      </div>
    </DashboardShell>
  )
}
