import type { NextApiRequest, NextApiResponse } from 'next'
import { env } from '@/env.mjs'
import fs from 'fs'
import OpenAI from 'openai'
import { retrieveBatchStatus, uploadFileToOpenAi } from './batch-utilts'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (env.NODE_ENV !== 'development') {
      throw new Error('Unauthorized')
    }

    const {
      source,
      type
    }: {
      type: 1 | 2
      source: string
    } = req.body

    const openai = new OpenAI()
    let response: any = {}

    const sourcePath = `dev/gpt_results/${source}/source.jsonl`
    if (type === 1) {
      response = await uploadFileToOpenAi({ path: sourcePath, openai })

      // store response json to sourcepath
      const responseJson = JSON.stringify(response, null, 2)
      fs.writeFileSync(
        `dev/gpt_results/${source}/uploadFile.json`,
        responseJson
      )
    } else if (type === 2) {
      const uploadFileJson = fs.readFileSync(
        `dev/gpt_results/${source}/uploadFile.json`,
        'utf8'
      )
      const uploadFileResponse = JSON.parse(uploadFileJson)
      const batchId = uploadFileResponse.id

      response = await retrieveBatchStatus({ batchId, openai })

      // store response json to batchId path
      const responseJson = JSON.stringify(response, null, 2)
      fs.writeFileSync(
        `dev/gpt_results/${source}/retrieveBatchStatus.json`,
        responseJson
      )
    }

    res.status(200).json(response)
  } catch (error) {
    console.error(error)
    res.status(500).json({ error: 'Failed to index.' })
  }
}
