'use client'

import type { SupabaseExtendedMetadata } from '@/types/document'
import type { Message } from 'ai/react'
import { CaseDrawer } from './case-drawer'
import { RESEARCH_QUERY_TYPE } from '@/types'
import { cn, parseMarkdown } from '@/lib/utils'
import { Icons } from '../icons'
import { useState } from 'react'
import { storeResponseFeedback } from '@/lib/actions/research'
import { toast } from '../../ui/use-toast'
import { FeedbackType } from '@prisma/client'
import CreateResponseFeedbackDialog from '../research/reponse-feedback-dialog'

export function ChatMessageBubble(props: {
  message: Message & { rating?: FeedbackType }
  researchId?: string
  sources: {
    docId: string
    refId: string
    title: string
    year: number
    data: SupabaseExtendedMetadata[]
  }[]
  updateSource?: (
    source: string,
    eventQuery: RESEARCH_QUERY_TYPE | null
  ) => Promise<void>
}) {
  const [isFeedbackDialogOpen, setIsFeedbackDialogOpen] = useState(false)
  const [messageRating, setMessageRating] = useState(props.message.rating)

  const colorClassName =
    props.message.role === 'user'
      ? 'bg-ring dark:bg-accent dark:text-white'
      : 'bg-yellow-100 dark:bg-slate-800 text-black dark:text-white'
  const alignmentClassName =
    props.message.role === 'user' ? 'ml-auto' : 'mr-auto pl-10 pt-10'

  const deviceOrientation = window.matchMedia('(orientation: portrait)').matches
  const sheetSide = deviceOrientation ? 'bottom' : 'right'
  const bubbleStyle =
    props.message.role !== 'user' &&
    (!props.sources || props.sources.length === 0)
      ? 'mb-10'
      : 'mb-0'

  async function handleRatingSubmission({
    type,
    researchId,
    messageId
  }: {
    type: FeedbackType
    researchId: string
    messageId: string
    feedbackContent?: string
  }) {
    const response = await storeResponseFeedback({
      type,
      researchId: researchId || '',
      messageId: messageId
    })
    if (response.ok) {
      if (type === FeedbackType.POSITIVE)
        toast({
          title: 'Rating submitted successfully',
          description: 'Thank you for rating the responses'
        })
    } else {
      throw new Error('Failed to submit feedback')
    }
  }

  return (
    <div
      className={cn(
        'rounded-md px-4 py-2 mb-8 flex',
        alignmentClassName,
        colorClassName
      )}
    >
      <div
        className={`whitespace-pre-wrap flex flex-col message-bubble-main ${bubbleStyle}`}
      >
        <span
          dangerouslySetInnerHTML={{
            __html: parseMarkdown(props.message.content)
          }}
        ></span>
        {props.sources && props.sources.length ? (
          <article className="mt-10 mr-2 bg-amber-50 dark:bg-slate-900 px-2 py-3 rounded-lg border border-accent">
            <p className="font-semibold mx-2 text-sm">SOURCES:</p>
            <div className="max-h-[300px] overflow-y-auto">
              {props.sources?.map((source, i) => (
                <CaseDrawer
                  key={i}
                  source={source}
                  i={i}
                  sheetSide={sheetSide}
                  updateSource={props.updateSource}
                />
              ))}
            </div>
          </article>
        ) : (
          ''
        )}
        {props.message.role != 'user' && (
          <div className="flex gap-2 mt-3 ml-auto mr-3">
            <button
              className="hover:bg-amber-400 rounded-full duration-200 p-2 aspect-square flex items-center justify-center"
              onClick={async () => {
                await handleRatingSubmission({
                  type: FeedbackType.POSITIVE,
                  researchId: props.researchId || '',
                  messageId: props.message.id
                })
                setMessageRating(FeedbackType.POSITIVE)
              }}
            >
              <Icons.thumbsUp
                className="h-5 w-5"
                fill={messageRating == FeedbackType.POSITIVE ? 'white' : ''}
              />
            </button>
            <button
              className="hover:bg-amber-400 rounded-full duration-200 p-2 aspect-square flex items-center justify-center"
              onClick={async () => {
                const response = await handleRatingSubmission({
                  type: FeedbackType.NEGATIVE,
                  researchId: props.researchId || '',
                  messageId: props.message.id
                })
                setMessageRating(FeedbackType.NEGATIVE)
                setIsFeedbackDialogOpen(true)
              }}
            >
              <Icons.thumbsDown
                className="h-5 w-5"
                fill={messageRating == FeedbackType.NEGATIVE ? 'white' : ''}
              />
            </button>

            {isFeedbackDialogOpen && (
              <CreateResponseFeedbackDialog
                type={FeedbackType.NEGATIVE}
                researchId={props.researchId || ''}
                messageId={props.message.id}
                open={isFeedbackDialogOpen}
                setOpen={setIsFeedbackDialogOpen}
              />
            )}
          </div>
        )}
      </div>
    </div>
  )
}
