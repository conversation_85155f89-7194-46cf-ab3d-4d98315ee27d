import { google } from 'googleapis'
import { env } from '@/env.mjs'
import { Account, User } from '@prisma/client'
import { db } from '../db'
import { streamToBuffer } from '../file-handler'

function oAuth2Client() {
  return new google.auth.OAuth2(
    env.GOOGLE_ID,
    env.GOOGLE_SECRET,
    `${env.NEXTAUTH_URL}/api/auth/google-drive/callback`
  )
}

export function getAuthUrl() {
  const scopes = [
    'https://www.googleapis.com/auth/drive.readonly',
    'https://www.googleapis.com/auth/drive.metadata.readonly'
  ]

  const authUrl = oAuth2Client().generateAuthUrl({
    access_type: 'offline',
    scope: scopes,
    prompt: 'consent'
  })

  return authUrl
}

export async function refreshGoogleDriveToken(id: Account['id']): Promise<{
  access_token: string
  refresh_token: string
  expiry_date: number
}> {
  const { credentials } = await oAuth2Client().refreshAccessToken()
  const { access_token, refresh_token, expiry_date } = credentials

  await db.account.update({
    where: {
      id
    },
    data: {
      access_token: access_token,
      refresh_token: refresh_token,
      expires_at: expiry_date ? new Date(expiry_date).getTime() : null
    }
  })

  return validateTokens({ access_token, refresh_token, expiry_date })
}

export async function generateGoogleDriveToken({
  code,
  userId
}: {
  code: string
  userId: User['id']
}): Promise<{
  access_token: string
  refresh_token: string
  expiry_date: number
}> {
  const { tokens } = await oAuth2Client().getToken(code)
  const { access_token, refresh_token, expiry_date } = tokens

  const account = await db.account.findFirst({
    where: {
      userId,
      provider: 'google-drive'
    }
  })

  if (account) {
    await db.account.update({
      where: {
        id: account.id
      },
      data: {
        access_token,
        refresh_token,
        expires_at: expiry_date ? new Date(expiry_date).getTime() : null
      }
    })
  } else {
    await db.account.create({
      data: {
        userId,
        type: 'oauth2',
        provider: 'google-drive',
        providerAccountId: userId,
        access_token,
        refresh_token,
        expires_at: expiry_date ? new Date(expiry_date).getTime() : null
      }
    })
  }

  return validateTokens({ access_token, refresh_token, expiry_date })
}

function validateTokens({
  access_token,
  refresh_token,
  expiry_date
}: {
  access_token: string | null | undefined
  refresh_token: string | null | undefined
  expiry_date: number | null | undefined
}) {
  if (!access_token || !refresh_token || !expiry_date) {
    throw new Error('Invalid tokens')
  }

  return { access_token, refresh_token, expiry_date }
}

export async function getAuthorizedDriveClient(userId: string) {
  try {
    const account = await db.account.findFirst({
      where: {
        userId,
        provider: 'google-drive'
      }
    })
    if (!account || !account.access_token) {
      return null
    }

    let { access_token, refresh_token, expires_at } = account

    // if (account.expires_at && account.expires_at <= Date.now()) {
    //   const newToken = await refreshGoogleDriveToken(account.id)

    //   access_token = newToken.access_token
    //   refresh_token = newToken.refresh_token
    //   expires_at = newToken.expiry_date
    // }

    const oAuthClient = oAuth2Client()
    oAuthClient.setCredentials({
      access_token,
      refresh_token,
      expiry_date: expires_at ? expires_at * 1000 : undefined
    })

    return google.drive({ version: 'v3', auth: oAuthClient })
  } catch (error: any) {
    console.error(error.message)
    throw new Error(
      'An error occurred while setting up the Google Drive client'
    )
  }
}

export async function downloadFileContent(fileId: string, userId: string) {
  const drive = await getAuthorizedDriveClient(userId)
  if (!drive) {
    throw new Error('Google Drive client not authorized')
  }

  const fileMetadata = await drive.files.get({
    fileId,
    fields: 'name, mimeType'
  })
  const fileName = fileMetadata.data.name || `${fileId}`
  const mimeType = fileMetadata.data.mimeType || 'application/octet-stream'

  // Download the file content
  const response = await drive.files.get(
    { fileId, alt: 'media' },
    { responseType: 'stream' }
  )

  const buffer = await streamToBuffer(response.data)

  return { buffer, name: fileName, type: mimeType }
}
