import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { PrivateUploadButton } from './buttons/private-upload-button'
import type { AuthUser } from 'next-auth'
import { PrivateResearchInfoCard } from './custom-components/feature-info-card'

export function PrivateUploadInstructor({ user }: { user: AuthUser }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Upload Documents</CardTitle>
      </CardHeader>
      <CardContent>
        <PrivateResearchInfoCard />
        <PrivateUploadButton />
      </CardContent>
    </Card>
  )
}
