import { NextRequest, NextResponse } from 'next/server'
import { GPTModel } from '@/types'
import { getCurrentUserResponse } from '@/lib/session'
import {
  extractFileContentAndHtmlWithMammoth,
  fileToBuffer
} from '@/lib/file-handler'
import { createCompletion } from '@/lib/services/openai-service'

export const dynamic = 'force-dynamic'
export const maxDuration = 120

const GPT_MODEL = GPTModel.GPTo4Mini

export async function POST(req: NextRequest) {
  try {
    const user = await getCurrentUserResponse()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await req.formData()
    const file = formData.get('file') as File
    const buffer = await fileToBuffer(file)
    const { textContent } = await extractFileContentAndHtmlWithMammoth({
      file: {
        ...buffer
      }
    })

    const prompt = `You are an advanced text analysis system designed to identify and suggest sensitive information for redaction within PDF documents. Your task is to analyze the content provided from a PDF file and identify any critical information that should be redacted.

Guidelines for Analysis:

Types of Sensitive Information:

- Personally Identifiable Information (PII): Names, addresses, phone numbers, email addresses, Social Security Numbers (SSNs), and other unique identifiers.
- Financial Data: Bank account numbers, credit card numbers, or transactional details.
- Confidential Business Information: Trade secrets, proprietary methods, sensitive contracts, or strategic documents.
- Health Information: Medical records, health conditions, or any data protected under privacy laws like HIPAA.
- Legal or Regulatory Information: Case details, witness identities, or any restricted legal information.
- Classified or Government Data: State secrets or data subject to clearance levels.
- Other: Any information flagged as inappropriate for exposure based on context.
- Contextual Sensitivity: If the document provides contextual clues indicating that certain information is sensitive (e.g., mentions of "Confidential," "Proprietary," or "Classified"), prioritize these areas for review.

Output Format:

- Clearly list the type of sensitive information identified (e.g., PII, Financial Data).
- Provide the specific text to be redacted and its type/category.
- Where possible, provide reasoning or context for why the information is flagged.

Additional Instructions:

- If no sensitive information is found, indicate that explicitly.
- Avoid flagging generic information (e.g., common words, non-sensitive data).
- Maintain confidentiality of the data and treat it as strictly private.
- Exclude the labels (e.g., “Account Number,” “SSN”) and output only the sensitive values themselves.

IMPORTANT: The output has to be in the following JSON format:
{
    "redactionWords: ["John Doe", "1234 Elm St", "Springfield", "*********"]
}

IT SHOULD BE A JSON OBJECT WITH A SINGLE KEY "redactionWords" CONTAINING AN ARRAY OF STRINGS.

- Type: PII Details: ["John Doe", "1234 Elm St", "Springfield"] Reason: Full name and address are provided, which constitutes PII.
- Type: Financial Data Details: ["*********"] Reason: Bank account number is confidential.

Content for Analysis:
<content>
${textContent}
</content>
`

    const rawAssessment = await createCompletion({
      model: GPT_MODEL,
      messages: [{ role: 'system', content: prompt }],
      json: true,
      temperature: undefined
    })

    return NextResponse.json(JSON.parse(rawAssessment))
  } catch (e: any) {
    console.log(e)
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}
