// components/elements/buttons/private-upload-button.tsx
'use client'

import React, { useState, useCallback } from 'react'
import { useDropzone, FileRejection, DropzoneOptions } from 'react-dropzone'
import { Button, ButtonProps } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { toast } from '../../ui/use-toast'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Textarea } from '../../ui/textarea'
import { Icons } from '../icons'

interface PrivateUploadButtonProps extends ButtonProps {
  buttonText?: string
  binderId?: string
  className?: string
}

export function PrivateUploadButton({
  buttonText = 'Upload Documents',
  binderId,
  className,
  ...props
}: PrivateUploadButtonProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className={cn('min-w-fit', className)} {...props}>
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[625px]">
        <DialogHeader>
          <DialogTitle>Upload Documents</DialogTitle>
          <DialogDescription>
            Upload your internal documents for analysis. You can upload multiple
            .pdf, .doc, or .docx files.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <FileUploadComponent binderId={binderId} />
        </div>
      </DialogContent>
    </Dialog>
  )
}

export function PrivateUploadButtonMin() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="min-w-fit">Upload Documents</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[625px]">
        <DialogHeader>
          <DialogTitle>Upload Documents</DialogTitle>
          <DialogDescription>
            Upload your internal documents for analysis. You can upload multiple
            .pdf, .doc, or .docx files.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <FileUploadComponent />
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Fixed interfaces for both file and URL uploads

interface FileWithPreview extends File {
  preview: string
  status: 'pending' | 'uploading' | 'uploaded' | 'error'
  progress?: number // For tracking upload percentage
  s3Key?: string
  ref?: string
  errorMessage?: string // For detailed error messages
}

interface PathWithStatus {
  url: string
  name: string
  size: number
  status: 'pending' | 'uploading' | 'uploaded' | 'error'
  progress?: number // Added to match FileWithPreview
  errorMessage?: string // Added to match FileWithPreview
}

export function FileUploadComponent({ binderId }: { binderId?: string }) {
  const MAX_FILE_SIZE = 5242880 * 10 //50 MB in bytes

  const router = useRouter()
  const [files, setFiles] = useState<FileWithPreview[]>([])
  const [urlInput, setUrlInput] = useState('')
  const [urls, setUrls] = useState<PathWithStatus[]>([])
  const [status, setStatus] = useState<'idle' | 'uploading' | 'uploaded'>(
    'idle'
  )

  const handleUrlInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    // Replace commas with newlines
    const formattedValue = value.replace(/,/g, '\n')
    setUrlInput(formattedValue)
  }

  const validateUrls = async () => {
    const urlList = urlInput
      .split('\n')
      .map((url) => url.trim())
      .filter((url) => url !== '')
    const validUrls: PathWithStatus[] = []
    const failedList: string[] = []

    for (const url of urlList) {
      // Check if URL ends with .pdf
      if (!url.toLowerCase().endsWith('.pdf')) {
        toast({
          title: 'Invalid URL',
          description: `URL must end with .pdf: ${url}`,
          variant: 'destructive'
        })
        continue
      }

      try {
        const response = await fetch('/api/gpt/ingest/validate-url', {
          method: 'POST',
          body: JSON.stringify({ url: url, max_size: MAX_FILE_SIZE })
        })

        if (!response.ok) {
          throw new Error(`Unable to fetch URL: ${url}`)
        }

        const data = await response.json()

        validUrls.push({
          url,
          name: data.validUrl.name,
          size: data.validUrl.size,
          status: 'pending'
        })
      } catch (error) {
        console.error(`Error validating URL: ${url}`, error)
        failedList.push(url)
        toast({
          title: 'Validation Error',
          description: `Error validating URL: ${url}`,
          variant: 'destructive'
        })
      }
    }

    setUrls(validUrls)
    setUrlInput(failedList.join('\n'))
  }

  const onDrop = useCallback(
    (acceptedFiles: File[], fileRejections: FileRejection[]) => {
      // Handle file rejections based on size and type
      fileRejections.forEach((fileRejection) => {
        toast({
          title: 'File Rejected',
          description: `File rejected - ${fileRejection.file.name}: Size exceeds 50 MB or invalid type.`,
          variant: 'destructive'
        })
      })

      // Map files to add preview URL and maintain previous state
      const mappedFiles = acceptedFiles.map((file) =>
        Object.assign(file, {
          preview: URL.createObjectURL(file)
        })
      )

      setFiles((currFiles) => [
        ...currFiles,
        ...mappedFiles.map((file) =>
          Object.assign(file, { status: 'pending' as 'pending' })
        )
      ])
    },
    []
  )

  const dropzoneOptions: DropzoneOptions = {
    onDrop,
    maxSize: MAX_FILE_SIZE,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        ['.docx']
    }
  }

  const { getRootProps, getInputProps } = useDropzone(dropzoneOptions)

  // Get presigned URL and upload file directly to S3 with progress tracking
  const uploadFileToS3 = async (
    file: FileWithPreview,
    index: number
  ): Promise<boolean> => {
    try {
      // 1. Get presigned URL from our backend
      const response = await fetch('/api/gpt/ingest/presigned-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || 'Failed to get upload URL'
        toast({
          title: 'Upload Preparation Failed',
          description: errorMessage,
          variant: 'destructive'
        })
        throw new Error(errorMessage)
      }

      const { presignedUrl, s3Key, ref } = await response.json()

      // 2. Upload file directly to S3 using the presigned URL with progress tracking
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest()

        // Track upload progress
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percentComplete = Math.round(
              (event.loaded / event.total) * 100
            )

            // Update file status with progress
            setFiles((currFiles) => {
              const newFiles = [...currFiles]
              newFiles[index] = {
                ...newFiles[index],
                name: file.name,
                status: 'uploading',
                progress: percentComplete
              } as any
              return newFiles
            })
          }
        })

        // Handle successful upload
        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            // Store S3 key for later processing
            file.s3Key = s3Key
            file.ref = ref
            resolve(true)
          } else {
            reject(new Error(`Upload failed with status: ${xhr.status}`))
          }
        })

        // Handle errors
        xhr.addEventListener('error', () => {
          console.error('Upload failed. Possible CORS issue.')
          console.error('Request URL:', presignedUrl)
          console.error('Request method: PUT')
          console.error('Origin:', window.location.origin)
          console.error('Content-Type:', file.type)

          reject(
            new Error(
              'Network error during upload. This might be a CORS issue.'
            )
          )
        })

        // Add timeout handling
        xhr.timeout = 60000 // 60 seconds
        xhr.addEventListener('timeout', () => {
          reject(new Error('Upload timed out'))
        })

        xhr.addEventListener('abort', () => {
          reject(new Error('Upload aborted'))
        })

        // Start the upload
        xhr.open('PUT', presignedUrl, true)
        xhr.setRequestHeader('Content-Type', file.type)
        xhr.setRequestHeader('Origin', window.location.origin)
        xhr.send(file)
      })
    } catch (error: any) {
      console.error('Error uploading file to S3:', error)
      toast({
        title: 'Upload Failed',
        description: error.message || 'Failed to upload file to storage',
        variant: 'destructive'
      })
      return false
    }
  }

  // Process the uploaded file on the server
  const processUploadedFile = async (
    file: FileWithPreview
  ): Promise<boolean> => {
    try {
      const response = await fetch('/api/gpt/ingest/process-upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          s3Key: file.s3Key,
          fileName: file.name,
          fileType: file.type,
          binderId: binderId || '',
          ref: file.ref
        })
      })

      if (!response.ok) {
        throw new Error('Failed to process file')
      }

      return true
    } catch (error) {
      console.error('Error processing file:', error)
      return false
    }
  }

  async function uploadFile(files: FileWithPreview[], urls: PathWithStatus[]) {
    try {
      setStatus('uploading')

      // Function to upload a single file
      const uploadSingleFile = async (file: FileWithPreview, index: number) => {
        setFiles((currFiles) => {
          const newFiles = [...currFiles]
          newFiles[index].status = 'uploading'
          newFiles[index].progress = 0
          return newFiles
        })

        try {
          // 1. First upload the file to S3 (progress tracking is handled inside this function)
          const s3Success = await uploadFileToS3(file, index)
          if (!s3Success) {
            setFiles((currFiles) => {
              const newFiles = [...currFiles]
              newFiles[index].status = 'error'
              newFiles[index].errorMessage = 'Failed to upload to storage'
              return newFiles
            })
            throw new Error(`Error uploading file ${file.name} to S3`)
          }

          // Update status to show processing
          setFiles((currFiles) => {
            const newFiles = [...currFiles]
            newFiles[index].status = 'uploading'
            newFiles[index].progress = 100
            return newFiles
          })

          // 2. Then process the file on the server
          const processSuccess = await processUploadedFile(file)
          if (!processSuccess) {
            setFiles((currFiles) => {
              const newFiles = [...currFiles]
              newFiles[index].status = 'error'
              newFiles[index].errorMessage = 'Server processing failed'
              return newFiles
            })
            throw new Error(`Error processing file ${file.name}`)
          }

          setFiles((currFiles) => {
            const newFiles = [...currFiles]
            newFiles[index].status = 'uploaded'
            return newFiles
          })

          console.log('File uploaded and processed successfully', file.name)
        } catch (error) {
          console.error(`Error handling file ${file.name}:`, error)
          // Error state already set in the specific error handlers
          throw error // Rethrow to be caught by the main uploadFile function
        }
      }

      // Function to upload a single URL
      const uploadSingleUrl = async (url: PathWithStatus, index: number) => {
        setUrls((currUrls) => {
          const newUrls = [...currUrls]
          newUrls[index].status = 'uploading'
          return newUrls
        })

        const response = await fetch('/api/gpt/ingest/path', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ url_data: url, binderId })
        })

        if (response.ok) {
          setUrls((currUrls) => {
            const newUrls = [...currUrls]
            newUrls[index].status = 'uploaded'
            return newUrls
          })
          console.log(`URL uploaded successfully`, url.url)
        } else {
          throw new Error(`Error uploading URL ${url.url}`)
        }
      }

      // Create upload tasks for all files and URLs
      const fileUploadTasks = files.map(
        (file, index) => () => uploadSingleFile(file, index)
      )
      const urlUploadTasks = urls.map(
        (url, index) => () => uploadSingleUrl(url, index)
      )
      const allTasks = [...fileUploadTasks, ...urlUploadTasks]

      // Process uploads with a concurrency limit of 3
      await processConcurrentTasks(allTasks, 3)

      toast({
        title: 'Upload Successful',
        description: 'Files and URLs uploaded successfully'
      })

      router.refresh()
      setStatus('uploaded')
    } catch (error) {
      console.error('Error uploading files or URLs', error)
      toast({
        title: 'Upload Failed',
        description: 'An error occurred while uploading files or URLs.',
        variant: 'destructive'
      })
      setStatus('idle')
    }
  }

  // Helper function to process tasks with a concurrency limit
  async function processConcurrentTasks(
    tasks: (() => Promise<void>)[],
    concurrencyLimit: number
  ) {
    // Create a queue of tasks
    const queue = [...tasks]
    const inProgress = new Set()
    const results = []

    // Process queue
    while (queue.length > 0 || inProgress.size > 0) {
      // Fill up to the concurrency limit
      while (queue.length > 0 && inProgress.size < concurrencyLimit) {
        const task = queue.shift()!

        // Create a promise that will be removed from inProgress when done
        const taskPromise = task()
          .then((result) => {
            inProgress.delete(taskPromise)
            return result
          })
          .catch((error) => {
            inProgress.delete(taskPromise)
            throw error
          })

        inProgress.add(taskPromise)
        results.push(taskPromise)
      }

      // Wait for at least one task to complete if we have any in progress
      if (inProgress.size > 0) {
        await Promise.race(inProgress)
      }
    }

    // Wait for all tasks to complete and check for any errors
    await Promise.all(results)
  }

  function removeFile(index: number) {
    const newFiles = [...files]
    if (index >= files.length) {
      const urlIndex = index - files.length
      const newUrls = [...urls]
      newUrls.splice(urlIndex, 1)
      setUrls(newUrls)
    } else {
      newFiles.splice(index, 1)
      setFiles(newFiles)
    }
  }

  return (
    <div>
      <div
        {...getRootProps()}
        className="border-2 border-dashed border-gray-400 px-4 py-16 mb-5 rounded-md cursor-pointer"
      >
        <input {...getInputProps()} />
        <p className="text-sm text-center">
          Drag &apos;n&apos; drop your files or folder here, or click to select
          files.
        </p>
      </div>

      <hr className="border-1 border-slate-300 dark:border-white mx-2 my-5" />

      <div className="mb-5">
        <label
          htmlFor="url-input"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"
        >
          Or enter PDF URLs (separate with commas)
        </label>

        <Textarea
          className="grow mr-4 rounded !overflow-y-scroll max-h-36"
          placeholder="https://example.com/file1.pdf, https://example.com/file2.pdf"
          value={urlInput}
          onChange={handleUrlInputChange}
          rows={4}
        />

        {urlInput !== '' && (
          <Button
            variant="secondary"
            onClick={validateUrls}
            size="xs"
            className="mt-2"
          >
            Validate URLs
          </Button>
        )}
      </div>

      {[...urls, ...files].length > 0 && (
        <div className="text-sm">
          <h4 className="font-semibold">
            {[...urls, ...files].length} files selected:
          </h4>
          <ul className="my-2 list-decimal overflow-y-scroll bg-slate-100 dark:bg-slate-900 py-1 px-4 rounded-lg max-h-36">
            {[...urls, ...files].map((file, index) => (
              <li
                key={index}
                className="flex justify-between items-center my-1"
              >
                <p>
                  {
                    // truncate file name if it exceeds 30 characters
                    file.name.length > 40
                      ? `${file.name.slice(0, 40)}...`
                      : file.name
                  }{' '}
                  - {file.size && `${(file.size / 1024 / 1024).toFixed(2)} MB`}
                </p>
                {(() => {
                  switch (file.status) {
                    case 'uploading':
                      return (
                        <div className="flex items-center">
                          {typeof file.progress === 'number' && (
                            <div className="w-24 bg-gray-200 rounded-full h-2.5 mr-2 dark:bg-gray-700">
                              <div
                                className="bg-blue-600 h-2.5 rounded-full dark:bg-blue-500"
                                style={{ width: `${file.progress}%` }}
                              ></div>
                            </div>
                          )}
                          <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-gray-700 dark:text-gray-300">
                            {file.progress === 100
                              ? 'Processing...'
                              : `${file.progress || 0}%`}
                          </span>
                        </div>
                      )
                    case 'uploaded':
                      return (
                        <span className="bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-full dark:bg-green-900 dark:text-green-300">
                          Uploaded
                        </span>
                      )
                    case 'error':
                      return (
                        <span
                          className="bg-red-100 text-red-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-full dark:bg-red-900 dark:text-red-300"
                          title={file.errorMessage || 'Upload failed'}
                        >
                          Failed
                        </span>
                      )
                    default:
                      return (
                        <button
                          className="text-[.5rem] ml-2 hover:bg-red-100 px-2 aspect-square rounded-md duration-100"
                          onClick={() => removeFile(index)}
                        >
                          ❌
                        </button>
                      )
                  }
                })()}
              </li>
            ))}
          </ul>
          {files.length > 4 && (
            <p className="text-xs text-gray-500 ml-2 mt-2 mb-4">
              Note: Scroll to view all selected files
            </p>
          )}
          {status === 'uploaded' ? (
            <DialogClose asChild>
              <Button type="button" variant="secondary">
                Close
              </Button>
            </DialogClose>
          ) : (
            <Button
              disabled={status === 'uploading'}
              onClick={() => uploadFile(files, urls)}
            >
              {status === 'uploading' ? (
                <>
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  <span>
                    Uploading Files{' '}
                    {[...urls, ...files].filter(
                      (file) => file.status === 'uploaded'
                    ).length +
                      '/' +
                      [...urls, ...files].length}
                  </span>
                </>
              ) : (
                <span>Upload Files</span>
              )}
            </Button>
          )}
        </div>
      )}
    </div>
  )
}
