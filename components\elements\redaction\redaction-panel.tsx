'use client'

import { useState, useEffect } from 'react'
import { RedactionFileUploader } from './redaction-file-uploader'
import { RedactionInputForm } from './redaction-input-form'

export function RedactionPanel() {
  const [originalPdfUrl, setOriginalPdfUrl] = useState<string>('')
  const [redactedPdfUrl, setRedactedPdfUrl] = useState<string>('')
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [pdfKey, setPdfKey] = useState<number>(0) // Force re-render of PDF viewer

  // Cleanup blob URLs when component unmounts or URLs change
  useEffect(() => {
    return () => {
      if (originalPdfUrl && originalPdfUrl.startsWith('blob:')) {
        URL.revokeObjectURL(originalPdfUrl)
      }
      if (redactedPdfUrl && redactedPdfUrl.startsWith('blob:')) {
        URL.revokeObjectURL(redactedPdfUrl)
      }
    }
  }, [originalPdfUrl, redactedPdfUrl])

  // Called from the file upload component after successful file selection
  function handleFileSelected(file: File | null) {
    // Clean up previous blob URL
    if (originalPdfUrl && originalPdfUrl.startsWith('blob:')) {
      URL.revokeObjectURL(originalPdfUrl)
    }

    setUploadedFile(file)
    handleRedactedPdfUrlUpdate('')

    if (file) {
      const url = URL.createObjectURL(file)
      setOriginalPdfUrl(url)
      setPdfKey((prev) => prev + 1) // Force re-render
    } else {
      setOriginalPdfUrl('')
      setPdfKey((prev) => prev + 1) // Force re-render
    }
  }

  // Wrapper function to handle redacted PDF URL updates with cleanup
  function handleRedactedPdfUrlUpdate(
    urlOrUpdater: string | ((prevState: string) => string)
  ) {
    // Clean up previous redacted PDF blob URL
    if (redactedPdfUrl && redactedPdfUrl.startsWith('blob:')) {
      URL.revokeObjectURL(redactedPdfUrl)
    }

    const newUrl =
      typeof urlOrUpdater === 'function'
        ? urlOrUpdater(redactedPdfUrl)
        : urlOrUpdater
    setRedactedPdfUrl(newUrl)
    if (newUrl) {
      setPdfKey((prev) => prev + 1) // Force re-render when new redacted PDF is set
    }
  }

  return (
    <section className="flex">
      <div className="w-4/6 border-r p-4 space-y-4 flex flex-col">
        {(() => {
          switch (true) {
            case !!redactedPdfUrl:
              return (
                <div className="flex flex-col space-y-2">
                  <h2 className="font-semibold">Redacted PDF:</h2>
                  <object
                    key={`redacted-${pdfKey}`}
                    data={redactedPdfUrl}
                    type="application/pdf"
                    className="w-full h-[70dvh] rounded"
                  >
                    <p>PDF preview not available.</p>
                  </object>
                  {/* <a href={redactedPdfUrl} download="redacted.pdf">
                    <Button variant="secondary">Download Redacted PDF</Button>
                  </a> */}
                </div>
              )

            case !!originalPdfUrl:
              return (
                <div className="flex flex-col space-y-2">
                  <h2 className="font-semibold">Original PDF:</h2>
                  <object
                    key={`original-${pdfKey}`}
                    data={originalPdfUrl}
                    type="application/pdf"
                    className="w-full h-[70dvh] rounded"
                  >
                    <p>PDF preview not available.</p>
                  </object>
                </div>
              )

            default:
              return (
                <RedactionFileUploader
                  onFileSelected={(file) => handleFileSelected(file)}
                />
              )
          }
        })()}
      </div>

      <div className="w-2/6 p-3">
        <RedactionInputForm
          uploadedFile={uploadedFile}
          setRedactedPdfUrl={handleRedactedPdfUrlUpdate}
          onFileSelected={handleFileSelected}
        />
      </div>
    </section>
  )
}
