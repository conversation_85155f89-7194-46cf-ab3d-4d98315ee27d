import { notFound } from 'next/navigation'
import { db } from '@/lib/db'
import Head from 'next/head'
import '@/styles/document-viewer.css'
import { OutputData } from '@editorjs/editorjs'
import Image from 'next/image'

interface NewsletterReadPageProps {
  params: { newsletterSlug: string }
}

export default async function NewsletterReadPage({
  params
}: NewsletterReadPageProps) {
  const newsletter = await db.newsletter.findUnique({
    where: {
      slug: params.newsletterSlug
    }
  })

  if (!newsletter) {
    return notFound()
  }

  const renderBlocks = (data: OutputData) => {
    return data.blocks.map((block, index) => {
      switch (block.type) {
        case 'paragraph':
          return (
            <p key={index} className="mb-4">
              {block.data.text}
            </p>
          )
        case 'header':
          const level = block.data.level || 1
          const Tag = `h${level}` as keyof JSX.IntrinsicElements
          return (
            <Tag key={index} className={`text-${level * 2}xl font-bold my-4`}>
              {block.data.text}
            </Tag>
          )
        case 'list':
          return (
            <ul key={index} className="list-disc list-inside">
              {block.data.items.map((item: string, idx: number) => (
                <li key={idx}>{item}</li>
              ))}
            </ul>
          )
        case 'image':
          return (
            <div key={index} className="my-4">
              <Image
                src={block.data.file.url}
                alt={block.data.caption}
                className="mx-auto"
                width={1200}
                height={800}
              />
              {block.data.caption && (
                <p className="text-center text-gray-500 text-sm">
                  {block.data.caption}
                </p>
              )}
            </div>
          )
        default:
          return (
            <div key={index} className="p-4 my-2 bg-gray-200">
              Unsupported block type: {block.type}
            </div>
          )
      }
    })
  }

  return (
    <>
      <Head>
        <title>{newsletter.title}</title>
        <meta
          name="description"
          content={`${newsletter.title} dated ${newsletter.createdAt.toDateString()}`}
        />
        <meta
          property="og:description"
          content={`Case of ${newsletter.title} dated ${newsletter.createdAt.toDateString()}`}
        />
        {/* <meta
          property="og:image"
          content=""
        />
        <meta content="1200" property="og:image:width" />
        <meta content="630" property="og:image:height" /> */}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content="noindex,nofollow" />
      </Head>
      <h1 className="text-4xl lg:text-6xl font-bold text-center my-8">
        {newsletter.title}
      </h1>
      <main>{renderBlocks(newsletter.content as any)}</main>
    </>
  )
}
