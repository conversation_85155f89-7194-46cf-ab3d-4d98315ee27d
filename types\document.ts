export interface CaseData {
  case: string
  court: string
  judges: string[]
  parties: string[]
  citation: string
  precedent_citations?: string[]
  related_cases?: string[]
  outcome?: string
  date: Date
  argument_date?: Date
  decision_date?: Date
  year?: number
  headnotes: string
  title?: string
  summary?: string
}

export interface SupabaseExtendedMetadata {
  pageContent: string
  metadata: Metadata
}

export interface Metadata {
  loc: {
    lines: {
      from: number
      to: number
    }
  }
  internalId: number
  refId: string
  title?: string
  year?: number
  case?: string
  date?: string
  court?: string
  judges?: string[]
  parties?: string[]
  citation?: string
  headnotes?: string
  fileName?: string
}
