'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { addTeamPeriodicCreditOnFeature } from '@/lib/recordstore-team'
import { useRouter } from 'next/navigation'
import { CreditType } from '@prisma/client'

const FormSchema = z.object({
  credits: z.coerce.number().min(2, {
    message: 'Credits to add must be at least 2.'
  }),
  period: z.coerce.number().min(1, {
    message: 'Number of days must be at least 1.'
  }),
  type: z.nativeEnum(CreditType, {
    message: 'Please select a valid credit type.'
  })
})

export function TeamCreditManagerModal({ teamId }: { teamId: string }) {
  const router = useRouter()
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      credits: 10,
      period: 30,
      type: CreditType.research
    }
  })

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    try {
      const addCredit = await addTeamPeriodicCreditOnFeature({
        teamId,
        type: data.type,
        period: Number(data.period),
        credit: Number(data.credits)
      })

      if (addCredit) {
        toast({
          title: 'Success',
          description: `${data.credits} credits added successfully. Expires on ${addCredit.expiresAt}`
        })

        router.refresh()
      } else {
        throw new Error('Failed to add credit')
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        variant: 'destructive',
        description: error.message
      })
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="xs">
          Add Credit
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Team Credit</DialogTitle>
          <DialogDescription>
            Add credits to your team balance with a specific type and expiration
            period.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Credit Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a credit type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(CreditType).map((type) => (
                        <SelectItem key={type} value={type}>
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Select the type of credit to add to the team.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="credits"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Credit</FormLabel>
                  <FormControl>
                    <Input placeholder="10" type="number" {...field} />
                  </FormControl>
                  <FormDescription>
                    This will add to the team&apos;s credit balance.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="period"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Period</FormLabel>
                  <FormControl>
                    <Input placeholder="10" type="number" {...field} />
                  </FormControl>
                  <FormDescription>
                    The credits will expire on{' '}
                    {new Date(
                      Date.now() + Number(field.value) * 24 * 60 * 60 * 1000
                    ).toLocaleDateString('en-US', {
                      month: 'long',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="submit">Submit</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
