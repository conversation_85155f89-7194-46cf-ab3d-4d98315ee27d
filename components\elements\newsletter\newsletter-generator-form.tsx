'use client'

import { useRouter } from 'next/navigation'
import { zodResolver } from '@hookform/resolvers/zod'
import { Newsletter } from '@prisma/client'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { FormField, FormItem } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { Icons } from '@/components/elements/icons'
import { newsletterGeneratorProps } from '@/config/newsletter'
import { Textarea } from '../../ui/textarea'
import { useEffect } from 'react'
import { newsletterPostSchema } from '@/lib/validations/newsletter'

interface NewsletterGeneratorFormProps
  extends React.HTMLAttributes<HTMLFormElement> {
  newsletterId: Newsletter['id']
  title: string
  setTitle: React.Dispatch<React.SetStateAction<string>>
  className?: string
  isSaving: boolean
  setIsSaving: React.Dispatch<React.SetStateAction<boolean>>
}

type FormData = z.infer<typeof newsletterPostSchema>

export function NewsletterGeneratorForm({
  newsletterId,
  title,
  setTitle,
  className,
  isSaving,
  setIsSaving,
  ...props
}: NewsletterGeneratorFormProps) {
  const router = useRouter()
  const {
    handleSubmit,
    register,
    setValue,
    watch,
    control,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(newsletterPostSchema),
    defaultValues: {
      theme: 'GST newsletter',
      title: title
    }
  })

  const titleWatch = watch('title')

  useEffect(() => {
    setTitle(titleWatch)
  }, [titleWatch, setTitle])

  async function onSubmit(data: FormData) {
    setIsSaving(true)

    const response = await fetch(`/api/newsletter/${newsletterId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },

      body: JSON.stringify({
        title: data.title,
        theme: data.theme,
        content: data.content,
        targetAudience: data.targetAudience,
        keyLegalUpdates: data.keyLegalUpdates,
        editorialTone: data.editorialTone
      })
    })

    setIsSaving(false)

    if (!response?.ok) {
      return toast({
        title: 'Uh Oh - something went wrong please try again.',
        description:
          'Your newsletter was not generated. My circuits have failed me. Tell my motherboard I loved her..',
        variant: 'destructive'
      })
    }

    toast({
      description: 'Drumroll, please... Your newsletter is shining and ready.'
    })

    router.refresh()
  }

  return (
    <form
      className={cn(className)}
      onSubmit={handleSubmit(onSubmit)}
      {...props}
    >
      <Card>
        <CardHeader>
          <CardTitle>Instant Newsletters</CardTitle>
          <CardDescription>
            Create your next Newsletter in seconds ⚡
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-1 py-3">
            <Label htmlFor="theme">Theme</Label>
            <Input
              id="theme"
              className="w-auto xl:w-[400px]"
              size={32}
              {...register('theme')}
            />
            {errors?.theme && (
              <p className="px-1 text-xs text-red-600">
                {errors.theme.message}
              </p>
            )}
          </div>
          <div className="grid gap-1 py-3">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              className="w-auto xl:w-[400px]"
              size={32}
              {...register('title')}
            />
            {errors?.title && (
              <p className="px-1 text-xs text-red-600">
                {errors.title.message}
              </p>
            )}
          </div>
          <div className="grid gap-1 py-3">
            <Label htmlFor="description">Describe newsletter</Label>
            <Textarea
              id="description"
              className="w-auto xl:w-[400px] h-[100px] text-left"
              placeholder="Eg: This newsletter is about the latest trends GST."
              {...register('content')}
            />
          </div>

          <div className="grid gap-1 py-3">
            <Label htmlFor="title">Target Audience</Label>
            <Input
              id="targetAudience"
              className="w-auto xl:w-[400px]"
              size={32}
              placeholder="Eg: Tax professionals, Accountants, Business owners"
              {...register('targetAudience')}
            />
            {errors?.targetAudience && (
              <p className="px-1 text-xs text-red-600">
                {errors.targetAudience.message}
              </p>
            )}
          </div>

          <div className="grid gap-1 py-3">
            <Label htmlFor="title">Key Legal Updates</Label>
            <Input
              id="keyLegalUpdates"
              className="w-auto xl:w-[400px]"
              size={32}
              placeholder="Eg: GST rate changes, New tax laws"
              {...register('keyLegalUpdates')}
            />
            {errors?.keyLegalUpdates && (
              <p className="px-1 text-xs text-red-600">
                {errors.keyLegalUpdates.message}
              </p>
            )}
          </div>

          <div className="grid gap-1 py-3">
            <FormField
              control={control}
              name="editorialTone"
              render={({ field }) => (
                <FormItem>
                  <Label htmlFor="editorialTone">Editorial Tone</Label>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger className="w-auto xl:w-[400px]">
                      <SelectValue placeholder="Formal / Casual" />
                    </SelectTrigger>
                    <SelectContent>
                      {newsletterGeneratorProps.languageStyle.map(
                        (item, index) => (
                          <SelectItem key={index} value={item.value}>
                            {item.label}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                  {errors?.editorialTone && (
                    <p className="px-1 text-xs text-red-600">
                      {errors.editorialTone.message}
                    </p>
                  )}
                </FormItem>
              )}
            />
          </div>
        </CardContent>
        <CardFooter>
          <button
            type="submit"
            className={cn(buttonVariants(), className)}
            disabled={isSaving}
          >
            {isSaving && (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            )}
            <span>Generate</span>
          </button>
        </CardFooter>
      </Card>
    </form>
  )
}
