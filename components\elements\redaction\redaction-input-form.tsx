'use client'

import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { WithContext as ReactTags } from 'react-tag-input'

import { Button } from '@/components/ui/button'
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage
} from '@/components/ui/form'
import { toast } from '@/components/ui/use-toast'
import { RedactionFileSelector } from './redaction-file-selector'

const FormSchema = z.object({
  wordRedaction: z
    .array(
      z.object({ id: z.string(), text: z.string(), className: z.string() })
    )
    .optional(),
  sentenceRedaction: z
    .array(
      z.object({ id: z.string(), text: z.string(), className: z.string() })
    )
    .optional(),
  paraRedaction: z
    .array(
      z.object({ id: z.string(), text: z.string(), className: z.string() })
    )
    .optional()
})

type RedactionFormValues = z.infer<typeof FormSchema>

const KeyCodes = {
  comma: 188,
  enter: 13
}
const delimiters = [KeyCodes.comma, KeyCodes.enter]

export function RedactionInputForm({
  uploadedFile,
  setRedactedPdfUrl,
  onFileSelected
}: {
  uploadedFile: File | null
  setRedactedPdfUrl: React.Dispatch<React.SetStateAction<string>>
  onFileSelected: (file: File | null) => void
}) {
  const form = useForm<RedactionFormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      wordRedaction: [],
      sentenceRedaction: [],
      paraRedaction: []
    }
  })

  const {
    handleSubmit,
    setValue,
    getValues,
    control,
    watch,
    formState: { errors }
  } = form

  // Watch all redaction fields to determine if redact button should be enabled
  const wordRedaction = watch('wordRedaction')
  const sentenceRedaction = watch('sentenceRedaction')
  const paraRedaction = watch('paraRedaction')

  const hasItemsToRedact =
    (wordRedaction && wordRedaction.length > 0) ||
    (sentenceRedaction && sentenceRedaction.length > 0) ||
    (paraRedaction && paraRedaction.length > 0)

  async function onSubmit(data: RedactionFormValues) {
    if (!uploadedFile) {
      toast({
        title: 'No file uploaded',
        description: 'Please upload a PDF file first.',
        variant: 'destructive'
      })
      return
    }

    const uploadToast = toast({
      title: 'Redacting...',
      description: 'Please wait while we redact your document.',
      duration: 60000
    })

    const formData = new FormData()
    formData.append('file', uploadedFile)

    // Convert tags array to just strings before appending
    formData.append(
      'wordRedaction',
      JSON.stringify((data.wordRedaction || []).map((tag) => tag.text))
    )
    formData.append(
      'sentenceRedaction',
      JSON.stringify((data.sentenceRedaction || []).map((tag) => tag.text))
    )
    formData.append(
      'paraRedaction',
      JSON.stringify((data.paraRedaction || []).map((tag) => tag.text))
    )

    const response = await fetch(`https://redact.smartcounsel.ai/redact`, {
      method: 'POST',
      body: formData
    })

    if (response.ok) {
      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      setRedactedPdfUrl(url)
      toast({
        itemID: uploadToast.id,
        title: 'Redaction Complete',
        description: 'Your document has been redacted.'
      })
    } else {
      toast({
        itemID: uploadToast.id,
        title: 'Error',
        description: 'Redaction failed.',
        variant: 'destructive'
      })
    }
  }

  async function assessWords() {
    if (!uploadedFile) {
      toast({
        title: 'No file selected',
        description: 'Please upload a PDF file first.',
        variant: 'destructive'
      })
      return
    }
    const formData = new FormData()
    formData.append('file', uploadedFile)

    const assess = toast({
      title: 'Assessing words...',
      description: 'Please wait while we assess your document.',
      duration: 60000
    })

    const response = await fetch('/api/gpt/redactor', {
      method: 'POST',
      body: formData
    })
    if (response.ok) {
      const data = await response.json()
      const wordRedaction = data.redactionWords
      // Convert string array to Tag objects
      setValue(
        'wordRedaction',
        wordRedaction.map((w: string) => ({ id: w, text: w, className: '' }))
      )
      toast({
        itemID: assess.id,
        title: 'Assessment Complete',
        description: `${wordRedaction.length} words to redact.`
      })
    } else {
      toast({
        itemID: assess.id,
        title: 'Error',
        description: 'Failed to assess words.',
        variant: 'destructive'
      })
    }
  }

  const handleAddTag = (fieldName: keyof RedactionFormValues) => (tag: any) => {
    const current = getValues(fieldName) || []
    setValue(fieldName, [...current, tag])
  }

  const handleDeleteTag =
    (fieldName: keyof RedactionFormValues) => (index: number) => {
      const current = getValues(fieldName) || []
      setValue(
        fieldName,
        current.filter((_, i) => i !== index)
      )
    }

  const handleDragTag =
    (fieldName: keyof RedactionFormValues) =>
    (tag: any, currPos: number, newPos: number) => {
      const current = getValues(fieldName) || []
      const newTags = current.slice()
      newTags.splice(currPos, 1)
      newTags.splice(newPos, 0, tag)
      setValue(fieldName, newTags)
    }

  return (
    <Form {...form}>
      <div className="grid grid-cols-1 gap-2 justify-between mb-5">
        {uploadedFile && (
          <RedactionFileSelector onFileSelected={onFileSelected} />
        )}
        <Button
          type="button"
          onClick={assessWords}
          disabled={!uploadedFile}
          variant="shiny"
        >
          AI assess words to redact
        </Button>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Button
          type="submit"
          disabled={!uploadedFile || !hasItemsToRedact}
          className="w-full"
        >
          {!uploadedFile || !hasItemsToRedact
            ? 'Add items to redact'
            : 'Redact your document'}
        </Button>

        <FormField
          control={control}
          name="wordRedaction"
          render={() => (
            <FormItem>
              <FormLabel>Words to Redact</FormLabel>
              <FormControl>
                <ReactTags
                  tags={getValues('wordRedaction') || []}
                  delimiters={delimiters}
                  handleAddition={handleAddTag('wordRedaction')}
                  handleDelete={handleDeleteTag('wordRedaction')}
                  handleDrag={handleDragTag('wordRedaction')}
                  placeholder="Press comma or enter to add a new word"
                  autofocus={false}
                  classNames={
                    {
                      tags: 'border p-2 rounded',
                      tagInputField: 'outline-none w-full',
                      tag: 'inline-block dark:bg-gray-700 dark:text-black space-x-2 text-sm rounded px-2 py-1 m-1',
                      remove: 'ml-2 cursor-pointer hover:text-red-700'
                    } as {
                      tags: string
                      tagInput: string
                      tagInputField: string
                      selected: string
                      tag: string
                      remove: string
                      suggestions: string
                      activeSuggestion: string
                      editTagInput: string
                      editTagInputField: string
                      clearAll: string
                    }
                  }
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="sentenceRedaction"
          render={() => (
            <FormItem>
              <FormLabel>Sentences to Redact</FormLabel>
              <FormControl>
                <ReactTags
                  tags={getValues('sentenceRedaction') || []}
                  delimiters={delimiters}
                  handleAddition={handleAddTag('sentenceRedaction')}
                  handleDelete={handleDeleteTag('sentenceRedaction')}
                  handleDrag={handleDragTag('sentenceRedaction')}
                  placeholder="Press comma or enter to add a new sentence"
                  autofocus={false}
                  classNames={
                    {
                      tags: 'border p-2 rounded',
                      tagInputField: 'outline-none w-full',
                      tag: 'inline-block bg-gray-200 rounded px-2 py-1 m-1'
                    } as {
                      tags: string
                      tagInput: string
                      tagInputField: string
                      selected: string
                      tag: string
                      remove: string
                      suggestions: string
                      activeSuggestion: string
                      editTagInput: string
                      editTagInputField: string
                      clearAll: string
                    }
                  }
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Uncomment if you want paragraph-level redaction
        <FormField
          control={control}
          name="paraRedaction"
          render={() => (
            <FormItem>
              <FormLabel>Paragraphs to Redact</FormLabel>
              <FormControl>
                <ReactTags
                  tags={getValues('paraRedaction') || []}
                  delimiters={delimiters}
                  handleAddition={handleAddTag('paraRedaction')}
                  handleDelete={handleDeleteTag('paraRedaction')}
                  handleDrag={handleDragTag('paraRedaction')}
                  placeholder="Press comma or enter to add a new paragraph indicator"
                  autofocus={false}
                  classNames={{
                    tags: 'border p-2 rounded',
                    tagInputField: 'outline-none w-full',
                    tag: 'inline-block bg-gray-200 rounded px-2 py-1 m-1'
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        /> */}
      </form>
    </Form>
  )
}
