'use server'

import { Prompt } from '@prisma/client'
import { db } from '../db'

export async function deletePrompt(id: string) {
  const prompt = await db.prompt.findUnique({
    where: {
      id: id
    }
  })

  if (!prompt) {
    return false
  }

  await db.prompt.delete({
    where: {
      id: id
    }
  })

  return true
}

async function storePromptHistory(prompt: Prompt) {
  await db.promptsHistory.create({
    data: {
      refId: prompt.id,
      source: prompt.source,
      role: prompt.role || 'SYSTEM',
      prompt: prompt.prompt,
      expectedOutput: prompt.expectedOutput,
      variables: prompt.variables || {}
    }
  })
}

export async function editPrompt({
  id,
  editedPrompt
}: {
  id: string
  editedPrompt: Partial<Prompt>
}) {
  const prompt = await db.prompt.findUnique({
    where: {
      id: id
    }
  })

  if (!prompt) {
    return false
  }

  // Store the current state in history before updating
  await storePromptHistory(prompt)

  const update = await db.prompt.update({
    where: {
      id: id
    },
    data: {
      role: editedPrompt.role,
      prompt: editedPrompt.prompt,
      expectedOutput: editedPrompt.expectedOutput
    }
  })

  if (!update) {
    return false
  }
  return true
}

export async function createPrompt({
  role,
  source,
  prompt,
  expectedOutput,
  variables
}: {
  role?: string
  source: string
  prompt: string
  expectedOutput: string
  variables: string[]
}) {
  const create = await db.prompt.create({
    data: {
      role: role,
      source: source,
      prompt: prompt,
      expectedOutput: expectedOutput,
      variables: variables
    }
  })

  if (!create) {
    return false
  }
  return true
}
