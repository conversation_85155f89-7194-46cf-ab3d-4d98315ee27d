import { NextRequest, NextResponse } from 'next/server'
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter'
import { PDFLoader } from 'langchain/document_loaders/fs/pdf'
import { combineDocumentsFn } from '@/lib/utils'
import { db } from '@/lib/db'
import { getCurrentUserResponse } from '@/lib/session'

export async function POST(req: NextRequest) {
  try {
    const data = await req.formData()
    const user = await getCurrentUserResponse()
    const file: File | null = data.get('file') as unknown as File
    const text: string = data.get('text') as unknown as string
    let textToAnalyze = text
    let title = ''
    if (file && file.type === 'application/pdf') {
      title = file.name
      const pdfLoader = new PDFLoader(file)
      const docs = await pdfLoader.load()
      textToAnalyze = combineDocumentsFn(docs)
    }

    const createTeamDocument = await db.teamDocument.create({
      data: {
        teamId: user.teamId,
        source: 'document-review'
      }
    })

    if (!createTeamDocument) {
      throw new Error('Unable to create team document')
    }

    const splitter = RecursiveCharacterTextSplitter.fromLanguage('markdown', {
      chunkSize: 1000,
      chunkOverlap: 200
    })

    const splitText = await splitter.splitText(textToAnalyze)
    if (title === '') {
      title = splitText[0].slice(0, 50) + '...'
    }

    await db.teamDocument.update({
      where: {
        id: createTeamDocument.id
      },
      data: {
        title: title
      }
    })

    await db.teamDocumentChunk.createMany({
      data: splitText.map((chunk) => ({
        teamDocumentId: createTeamDocument.id,
        content: chunk
      }))
    })

    return new Response(JSON.stringify(createTeamDocument))
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}

export interface DocumentReviewResponse {
  topic: string
  review: string
  priority: string
  reason: string
}
