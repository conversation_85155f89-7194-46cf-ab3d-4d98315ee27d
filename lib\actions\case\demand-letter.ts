'use server'

import { CaseFileType } from '@prisma/client'
import { db } from '../../db'
import { UnauthorizedError } from '../../exceptions'
import { getCurrentUser } from '../../session'
import { generateDemandLetter } from '@/lib/processes/demand-letter-generator'

export async function handleDemandLetterGeneration(binderId: string) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    const demandLetter = await generateDemandLetter(binderId)

    const caseFile = await db.caseFile.upsert({
      where: {
        binderId_fileType: {
          binderId,
          fileType: CaseFileType.DEMAND_LETTER
        }
      },
      update: {
        content: demandLetter.combinedContent
      },
      create: {
        binderId,
        creatorId: user.id,
        fileType: CaseFileType.DEMAND_LETTER,
        content: demandLetter.combinedContent
      }
    })

    await db.teamCreditUsed.create({
      data: {
        teamId: user.teamId,
        type: 'case',
        refId: caseFile.id,
        eventId: new Date().getTime().toString()
      }
    })

    return demandLetter
  } catch (error) {
    console.error('Error in handleDemandLetterGeneration:', error)
    throw error
  }
}

export async function updateDemandLetter(binderId: string, content: string) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    const store = await db.caseFile.upsert({
      where: {
        binderId_fileType: {
          binderId,
          fileType: CaseFileType.DEMAND_LETTER
        }
      },
      update: {
        content
      },
      create: {
        binderId,
        creatorId: user.id,
        fileType: CaseFileType.DEMAND_LETTER,
        content
      }
    })

    return store
  } catch (error) {
    console.error('Error in updateDemandLetter:', error)
    throw error
  }
}
