import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '../../ui/card'

export const PrivateResearchInfoCard = () => {
  return (
    <section className="lg:p-8">
      <CardHeader>
        <CardTitle>User Guide</CardTitle>
        <CardDescription>
          Welcome to our Private Research and Deposition Prep tool. This
          intuitive platform is tailored for professionals seeking answers to
          specific topics from your internal documents.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ol className="list-decimal list-inside mb-4">
          <li className="mb-2">
            Upload your documents (.pdf, .doc, .docx) for analysis using the
            provided file uploader.
          </li>
          <li className="mb-2">
            You can then input details of your query in the text box for a more
            refined search.
          </li>
          <li className="mb-2">
            Click the <strong>&quot;Ask&quot;</strong> button to begin
            processing your query and document analysis.
          </li>
          <li className="mb-2">
            Once processing is complete, you will receive comprehensive insights
            based on the uploaded documents and your query.
          </li>
          <li>
            If needed, adjust your query and repeat the search for better
            results.
          </li>
        </ol>

        <p className="italic">
          Note: Your documents and inputs are processed securely. We ensure
          privacy by not storing any files or queries after processing.
        </p>
      </CardContent>
    </section>
  )
}

export const ResearchInfoCard = () => {
  return (
    <section className="lg:p-8">
      <CardHeader>
        <CardTitle>User Guide</CardTitle>
        <CardDescription>
          Welcome to our Legal Research and Deposition Prep tool. This intuitive
          platform is tailored for legal professionals seeking answers to
          specific legal issues.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ol className="list-decimal list-inside mb-4">
          <li className="mb-2">
            In the provided text box, input the details of your legal query. Be
            as specific as possible for precise results.
          </li>
          <li className="mb-2">
            Click the <strong>&quot;Ask&quot;</strong> button to initiate the
            query processing.
          </li>
          <li className="mb-2">
            Review the comprehensive answer displayed below. Relevant sources or
            documents will be provided for reference.
          </li>
          <li>
            If necessary, refine your query and search again for better results.
          </li>
        </ol>

        <p className="italic">
          Note: We prioritize user privacy. Your inputs are processed securely
          and aren&apos;t stored post-query.
        </p>
      </CardContent>
    </section>
  )
}

export const ExecutiveOrdersResearchInfoCard = () => {
  return (
    <section className="lg:p-8">
      <CardHeader>
        <CardTitle>Research Guide</CardTitle>
        <CardDescription>
          Welcome to our <strong>Executive Order Research</strong> tool. This
          intuitive platform is tailored for guest users seeking information on
          the newly issued executive orders from President Trump’s second term.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ol className="list-decimal list-inside mb-4">
          <li className="mb-2">
            In the provided text box, enter the executive order number or topic
            you want to explore. For the best results, be as specific as
            possible.
          </li>
          <li className="mb-2">
            Click the <strong>&quot;Ask&quot;</strong> button to initiate your
            research query.
          </li>
          <li className="mb-2">
            Review the comprehensive answer displayed below. If relevant, we
            will provide official references or supporting documentation for
            that executive order.
          </li>
          <li>
            If necessary, refine your search parameters and click
            &quot;Ask&quot; again for even more targeted insights.
          </li>
        </ol>
        <p className="italic">
          <strong>Note:</strong> We value your privacy. Your inputs are
          processed securely and are not stored after the query completes.
        </p>
      </CardContent>
    </section>
  )
}

export const MedicalChronologyUploadInfoCard = () => {
  return (
    <section className="lg:p-8">
      <CardHeader>
        <CardTitle>Medical Chronology Upload</CardTitle>
        <CardDescription>
          A streamlined process for uploading and organizing medical records
          within SmartCounsel.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ol className="list-decimal list-inside mb-4">
          <li className="mb-2">
            Upload primary documents such as Police Reports, EMS Reports,
            Medical Records, Imaging Reports, and Hospital Discharge Summaries.
          </li>
          <li className="mb-2">
            If needed, click{' '}
            <strong>&ldquo;Need to Upload Other Documents?&ldquo;</strong>
            to select and upload additional medical records.
          </li>
          <li className="mb-2">
            Review uploaded files in the summary table with options to replace
            or delete.
          </li>
          <li>
            Click <strong>&ldquo;Finalize Uploads&ldquo;</strong> to process
            documents for AI analysis.
          </li>
        </ol>

        <p className="italic">
          Note: Files are securely processed, ensuring privacy and
          confidentiality.
        </p>
      </CardContent>
    </section>
  )
}
