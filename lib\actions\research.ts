'use server'

import { Session } from 'next-auth'
import { db } from '../db'
import { type ResearchStoreContent } from '@/types'
import {
  CreditType,
  type FeedbackType,
  type ResearchStore,
  type ResearchType
} from '@prisma/client'
import { CaseData } from '@/types/document'
import { PrismaClient as PostgreSQLClient } from '../../generated/postgres'
import {
  getCurrentUser,
  getCurrentUserResponse,
  getFeatureUsageStats
} from '../session'

export async function storeResearchContent({
  user,
  content,
  namespace,
  masquerade,
  researchType,
  binderId
}: {
  user: Session['user']
  content: ResearchStoreContent
  namespace: string | undefined
  masquerade: boolean
  researchType: ResearchType
  binderId?: string
}) {
  const timestamp = new Date().getTime().toString()
  content.messages?.forEach((message) => {
    if (!message.userId) {
      message.userId = user.id
      message.timestamp = timestamp
    }
  })

  let research: ResearchStore | undefined = undefined
  let updateType: 'create' | 'update' = 'create'

  if (content.researchId) {
    research = await db.researchStore.update({
      where: { id: content.researchId },
      data: {
        region: user.region,
        source: namespace,
        content: content as any
      }
    })
    updateType = 'update'
  } else {
    const question = content.messages?.[0]?.content
    const questionPreview =
      question && question.length > 900
        ? question.slice(0, 900) + '...'
        : question || 'Unused research'

    research = await db.researchStore.create({
      data: {
        userId: user.id,
        region: user.region,
        source: namespace,
        type: researchType,
        content: content as any,
        question: questionPreview,
        binderId: binderId
      }
    })
  }

  if (masquerade === false) {
    await db.teamCreditUsed.create({
      data: {
        teamId: user.teamId,
        type: 'research',
        refId: research.id,
        eventId: timestamp
      }
    })
  }

  return {
    data: research,
    type: updateType
  }
}

export async function getCaseDocument(documentId: string) {
  const refProps = documentId.split('-')
  let refValue = refProps[1]
  let condition: any = {
    ref: refValue
  }

  switch (refProps[0]) {
    case 'llr':
      condition = {
        source: {
          in: ['labourlawreporter']
        },
        ref: refValue
      }
      break

    case 'tio':
      condition = {
        source: {
          in: ['taxindiaonline', 'taxindiaonline-income-tax']
        },
        ref: refValue
      }

    default:
      condition = {
        ref: documentId
      }
  }

  const document = await db.documentRecords.findFirst({
    where: condition
  })

  if (!document) {
    return {
      metadata: null,
      htmlbody: null
    }
  }

  const metadata = document.meta
    ? (JSON.parse(document.meta) as CaseData)
    : null

  let htmlbody = document.html
  if (htmlbody.split('<body class="mid1">')[1]) {
    htmlbody = htmlbody.split('<body class="mid1">')[1].split('</body>')[0]
  }

  return {
    metadata,
    htmlbody
  }
}

export async function findDocumentRecordById(documentId: number) {
  return db.documentRecords.findUnique({
    select: {
      title: true
    },
    where: {
      id: documentId
    }
  })
}

export async function deleteUploadedDocFromDatabase(docId: number) {
  try {
    const deletedDocument = await db.documentRecords.delete({
      where: {
        id: docId
      }
    })
    return deletedDocument.id
  } catch (error) {
    console.log('Error deleteing the document from database', error)
    throw new Error('Error deleting the document')
  }
}

export async function deleteDocumentVectorsFromDatabase(docId: number) {
  try {
    const postgres = new PostgreSQLClient()

    const result =
      await postgres.$executeRaw`DELETE FROM "UserVectorStore" WHERE "docId" = ${docId}`

    return result
  } catch (error) {
    console.log('Error deleteing vectors from database', error)
    throw new Error('Error deleting vectors')
  }
}

export async function deleteUploadedDocument(docId: number) {
  try {
    const deleteDataPromise = deleteUploadedDocFromDatabase(docId)
    const deleteVectorPromise = deleteDocumentVectorsFromDatabase(docId)

    await Promise.all([deleteDataPromise, deleteVectorPromise])

    return true
  } catch (error) {
    console.log('Error deleting the document')
    return false
  }
}

export async function deleteDataset({
  datasetId,
  teamId,
  deleteDocs
}: {
  datasetId: number
  teamId: string
  deleteDocs?: boolean
}) {
  try {
    // Delete the dataset first
    const deletedDataset = await db.dataset.delete({
      where: { id: datasetId }
    })

    // Find all related document records before deleting the dataset map
    const deleteMaps = await db.documentRecordDatasetMap.findMany({
      where: { datasetId: datasetId }
    })

    if (deleteDocs) {
      // Collect all document IDs associated with the dataset
      const documentIds = deleteMaps.map((map) => map.documentId)

      if (documentIds.length > 0) {
        // Delete document records and related vectors
        await Promise.all(
          documentIds.map((docId) => deleteUploadedDocument(docId))
        )

        // Remove all mappings of these documents from other datasets
        await db.documentRecordDatasetMap.deleteMany({
          where: { documentId: { in: documentIds } }
        })
      }
    } else {
      // Delete mappings for the dataset
      await db.documentRecordDatasetMap.deleteMany({
        where: { datasetId: datasetId }
      })
    }

    // Clean up any datasets that no longer have any document mappings
    const emptyDatasets = await db.dataset.findMany({
      where: {
        id: {
          not: datasetId // Exclude the already deleted dataset
        },
        createdBy: teamId,
        NOT: {
          DocumentRecordDatasetMap: {
            some: {} // This ensures that datasets with no mappings are selected
          }
        }
      }
    })

    // Delete all datasets that are empty (have no related documents)
    await Promise.all(
      emptyDatasets.map((emptySet) =>
        db.dataset.delete({ where: { id: emptySet.id } })
      )
    )

    return true
  } catch (error) {
    console.log('Error deleting the dataset from database', error)
    return false
  }
}

export async function groupDocumentsInDataset(
  documentIds: number[],
  datasetName: string
) {
  const user = await getCurrentUserResponse()

  try {
    if (!documentIds || !datasetName || !user) {
      return { ok: false }
    }

    const newDataset = await db.dataset.create({
      data: {
        name: datasetName,
        createdBy: user.teamId.toString()
      }
    })

    if (newDataset) {
      await db.documentRecordDatasetMap.createMany({
        data: documentIds.map((docId: number) => ({
          datasetId: newDataset.id,
          documentId: parseInt(docId.toString())
        }))
      })
    }

    return { ok: true }
  } catch (error: any) {
    console.error(error)
    return { ok: false }
  }
}

export async function storeResponseFeedback({
  type,
  researchId,
  messageId,
  feedbackContent
}: {
  type: FeedbackType
  researchId: string
  messageId: string
  feedbackContent?: string
}) {
  try {
    const feedbackRecordExists = await db.responseFeedback.findFirst({
      where: {
        researchId: researchId,
        messageId: messageId
      }
    })
    if (feedbackRecordExists) {
      await db.responseFeedback.update({
        where: {
          id: feedbackRecordExists.id
        },
        data: {
          type: type,
          feedbackContent: feedbackContent
        }
      })
    } else {
      const response = await db.responseFeedback.create({
        data: {
          type,
          researchId,
          messageId,
          feedbackContent
        }
      })
    }

    const research = await db.researchStore.findUnique({
      where: {
        id: researchId
      },
      select: {
        content: true
      }
    })

    if (research && research.content) {
      const content: any = research.content

      const message = content.messages.find((msg: any) => msg.id === messageId)

      if (message) {
        message.rating = type
      }

      await db.researchStore.update({
        where: { id: researchId },
        data: {
          content: content // Ensure content is properly typed as JsonValue for Prisma
        }
      })
    }
    return { ok: true }
  } catch (error: any) {
    console.error(error)
    return { ok: false }
  }
}

export async function fetchUserQuestionAssessmentSettings(userId: string) {
  try {
    const userSettings = await db.userSettings.findFirst({
      where: {
        userId
      },
      select: {
        questionAssessment: true
      }
    })
    return userSettings?.questionAssessment ?? true
  } catch (error: any) {
    console.error(error)
  }
}

export async function updateQuestionAssessmentSetting({
  userId,
  questionAssessmentSetting
}: {
  userId: string
  questionAssessmentSetting: boolean
}) {
  try {
    const userSettingsExists = await db.userSettings.findFirst({
      where: {
        userId
      }
    })
    if (userSettingsExists) {
      await db.userSettings.update({
        where: {
          userId
        },
        data: {
          questionAssessment: questionAssessmentSetting
        }
      })
    } else {
      const response = await db.userSettings.create({
        data: {
          userId,
          questionAssessment: questionAssessmentSetting
        }
      })
    }

    return { ok: true }
  } catch (error: any) {
    console.error(error)
    return { ok: false }
  }
}

export async function fetchResearchProps({
  researchId
}: {
  researchId: string
}) {
  const user = await getCurrentUser()

  if (!user) {
    return null
  }

  const usageStats = await getFeatureUsageStats({
    feature: CreditType.research
  })

  const research = await db.researchStore.findFirst({
    where: {
      id: researchId
    }
  })

  if (!research) {
    return null
  }

  const researchPropsRaw = research.content as unknown as ResearchStoreContent
  const researchProps: ResearchStoreContent = {
    model: researchPropsRaw.model,
    sources: researchPropsRaw.sources,
    court: researchPropsRaw.court || [],
    year: researchPropsRaw.year || [],
    sourcesForMessages: researchPropsRaw.sourcesForMessages,
    sourceLabels: researchPropsRaw.sourceLabels || [],
    messages: researchPropsRaw.messages
  }
  researchProps.researchId = research.id

  return { user, research, stats: usageStats, researchProps }
}
