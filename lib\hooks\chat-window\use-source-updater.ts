import { useCallback } from 'react'
import { findDocumentRecordById } from '@/lib/actions/research'
import { RESEARCH_QUERY_TYPE } from '@/types'
import { ResearchType } from '@prisma/client'

interface UseSourceUpdaterProps {
  setSources: (val: string[]) => void
  setSourceName: (val: string | null) => void
  setEventQuery: (val: RESEARCH_QUERY_TYPE | null) => void
  setInput: (val: string) => void
  researchType: ResearchType
}

/**
 * Manages the logic for updating the selected source and optionally
 * initiating a summary query if that’s what the user wants.
 */
export function useSourceUpdater({
  setSources,
  setSourceName,
  setEventQuery,
  setInput,
  researchType
}: UseSourceUpdaterProps) {
  const updateSource = useCallback(
    async (source: string, eventQuery: RESEARCH_QUERY_TYPE | null) => {
      // Replace all selected sources with just the one user picked
      setSources([source])
      setSourceName(null)
      setEventQuery(eventQuery)

      async function getSourceName() {
        const data = await findDocumentRecordById(parseInt(source))
        if (data) {
          setSourceName(data.title)
          if (eventQuery === RESEARCH_QUERY_TYPE.SUMMARISE) {
            setInput(
              researchType === ResearchType.case
                ? `Summarize the case: ${data.title}`
                : `Summarize the document: ${data.title}`
            )
          }
        }
      }

      await getSourceName()

      document.getElementById('chat-form-question-input')?.scrollIntoView({
        behavior: 'smooth',
        block: 'end', // Align the element to the end of the viewport
        inline: 'nearest' // Align horizontally to the nearest edge
      })

      // Small delay so that the UI can catch up
      await new Promise((resolve) => setTimeout(resolve, 1000))

      if (!eventQuery) {
        // If no special event was triggered, focus the chat input
        document.getElementById('chat-input')?.focus()
      }
    },
    [setSources, setSourceName, setEventQuery, setInput]
  )

  return { updateSource }
}
