import { db } from '@/lib/db'
import { EmptyPlaceholder } from '@/components/elements/custom-components/empty-placeholder'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { DataTablePagination } from '@/components/elements/data-table-pagination'
import { NewsletterCreateButton } from '@/components/elements/newsletter/newsletter-create-button'
import { NewsletterItem } from '@/components/elements/newsletter/newsletter-item'

export const metadata = {
  title: 'Newsletter List'
}

export default async function NewslettersListPage({
  searchParams
}: {
  searchParams: { [key: string]: string | string[] }
}) {
  let pageLimiter = searchParams.limit ? Number(searchParams.limit) : 20
  const page = searchParams.page ? Number(searchParams.page) : 1

  const newsletters = await db.newsletter.findMany({
    orderBy: {
      createdAt: 'desc'
    },
    skip: (page - 1) * pageLimiter,
    take: pageLimiter
  })

  const totalNewsletter = await db.newsletter.count()
  const totalPage = Math.ceil(totalNewsletter / pageLimiter)

  const startIndex = totalNewsletter - (page - 1) * pageLimiter

  const newslettersWithIndex = newsletters?.map((newsletter, index) => ({
    index: startIndex - index,
    ...newsletter
  }))

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Newsletter"
        text="List of newsletters on SmartCounsel"
      >
        <NewsletterCreateButton />
      </DashboardHeader>
      <div>
        {newslettersWithIndex?.length ? (
          <div className="divide-y divide-border rounded-md border bg-white dark:bg-slate-950">
            {newslettersWithIndex.map((newsletter) => (
              <NewsletterItem key={newsletter.id} newsletter={newsletter} />
            ))}
            <DataTablePagination
              currentPage={page}
              totalPage={totalPage}
              searchParams={searchParams}
            />
          </div>
        ) : (
          <EmptyPlaceholder>
            <EmptyPlaceholder.Icon name="post" />
            <EmptyPlaceholder.Title>
              No newsletters found
            </EmptyPlaceholder.Title>
            <EmptyPlaceholder.Description>
              Create a new newsletter to get started
            </EmptyPlaceholder.Description>
          </EmptyPlaceholder>
        )}
      </div>
    </DashboardShell>
  )
}
