import { redirect } from 'next/navigation'

import { authOptions } from '@/lib/auth'
import { getCurrentUser } from '@/lib/session'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { features } from '@/config/dashboard'
import { SearchWindow } from '@/components/elements/custom-components/search-window'
import { vectorSearchConversation } from '@/lib/actions/search'

export const metadata = features['searchDatabase']

export default async function SettingsPage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect(authOptions?.pages?.signIn || '/login')
  }

  return (
    <DashboardShell>
      <DashboardHeader heading={metadata.title} text={metadata.description} />
      <div className="grid gap-10">
        <SearchWindow
          region={user.region}
          vectorSearchConversation={vectorSearchConversation}
        />
      </div>
    </DashboardShell>
  )
}
