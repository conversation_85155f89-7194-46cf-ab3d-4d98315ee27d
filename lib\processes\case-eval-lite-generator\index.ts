import { createGeminiCompletion } from '@/lib/services/gemini-service'
import { DocumentContent } from '@/types/case'
import { AuthUser } from 'next-auth'
import { logger } from '../utils-llm'
import { GeminiModel } from '@/types'

/**
 * Generates a rapid case evaluation lite assessment
 */
export async function generateCaseEvaluationLite(
  caseId: string,
  documents: Record<string, DocumentContent[]>,
  user: AuthUser
): Promise<string> {
  logger.start('generateCaseEvaluationLite', {
    documentCount: Object.values(documents).flat().length
  })

  try {
    // Extract and structure document content
    const documentSummary = await extractDocumentData(documents, user)

    // Generate the case evaluation lite (no medical chronology required)
    const caseEvalLite = await generateLiteReport(documentSummary, user)

    logger.end('generateCaseEvaluationLite')
    return caseEvalLite
  } catch (error: any) {
    logger.error('generateCaseEvaluationLite', error)
    throw new Error(`Failed to generate case evaluation lite: ${error.message}`)
  }
}

/**
 * Extract key data from documents for rapid assessment
 */
async function extractDocumentData(
  documents: Record<string, DocumentContent[]>,
  user: AuthUser
): Promise<string> {
  logger.start('extractDocumentData')

  try {
    // Combine all document content
    const allDocuments = Object.values(documents).flat()
    const consolidatedContent = allDocuments
      .map((doc) => `DOCUMENT: ${doc.title}\n${doc.content}`)
      .join('\n\n---\n\n')

    const extractionPrompt = `
You are SmartCounsel AI, analyzing medical-legal documents for rapid case assessment.

Extract the following key information from the provided documents:

TIMELINE OF EVENTS:
- Incident date and details
- Initial medical treatment
- Follow-up treatments and procedures
- Current status

INJURIES AND MEDICAL ISSUES:
- Primary injuries sustained
- Secondary complications
- Current symptoms and limitations
- Treatment gaps or inconsistencies

POTENTIAL BREACHES AND LIABILITY:
- Standard of care issues
- Delayed diagnosis or treatment
- Medication errors
- Communication failures
- Documentation problems

GAPS AND CONCERNS:
- Missing documentation
- Inconsistent records
- Treatment delays
- Diagnostic oversights

Provide a concise, factual summary focusing on these four areas. Cite specific record references where possible as "Record, p. ##".

DOCUMENTS:
${consolidatedContent}
`

    const response = await createGeminiCompletion({
      message: extractionPrompt,
      systemInstruction:
        'You are a precise medical-legal analyst. Focus on facts and cite specific record references.',
      teamId: user.teamId,
      purpose: 'case-eval-lite',
      activity: 'document-data-extraction'
    })

    logger.end('extractDocumentData')
    return response as string
  } catch (error: any) {
    logger.error('extractDocumentData', error)
    throw new Error(`Failed to extract document data: ${error.message}`)
  }
}

/**
 * Generate the final lite case evaluation report
 */
async function generateLiteReport(
  documentAnalysis: string,
  user: AuthUser
): Promise<string> {
  logger.start('generateLiteReport')

  try {
    const liteReportPrompt = `
You are SmartCounsel AI, an expert medico-legal analyst for North-American plaintiff's attorneys.

Your task is to draft a one-page "Case Evaluation" memorandum that tells the lawyer whether a prima facie medical-malpractice claim exists.

Follow these rules:
- Write in formal legal style; do not use contractions. Do not include the memo heading (TO, FROM, DATE, RE) in the generated output.
- Limit the entire memo to one US-letter page (≈ 450 words).
- Use the section headings shown below, in this exact order.
- Begin with a bold Recommendation: Accept, Further Investigation Needed, or Decline.
- Support every factual or medical assertion with (a) an internal record citation—"Record, p. ##"—or (b) an external hyperlink from medical authorities (max 8).
- Address all four negligence elements (duty, breach, causation, damages).
- Include a short disclaimer: "This memorandum is privileged attorney work product and a preliminary evaluation, not a final opinion."

Required Layout:
**Executive Summary** – one-sentence recommendation plus headline facts.
**Elements Analysis** – numbered bullets for Duty, Breach, Causation, Damages.
**Key Risks / Damages** – caps, SOL, liens, jury factors (3-5 lines).
**Open Issues** – table of missing critical documents or facts.
**Sources Reviewed** – numbered list of external authorities (title, year, one-line relevance, live URL).

DOCUMENT ANALYSIS:
${documentAnalysis}

EXTERNAL_SOURCES (for reference):
1. "Standard of Care Guidelines" - Medical practice standards
2. "Diagnostic Imaging Best Practices" - Radiology protocols
3. "Emergency Medicine Protocols" - ER standard procedures
4. "Medication Administration Guidelines" - Pharmacy standards
5. "Patient Safety Standards" - Joint Commission guidelines
6. "Medical Record Documentation" - Legal requirements
7. "Informed Consent Requirements" - Legal standards
8. "Statute of Limitations" - Jurisdiction-specific timeframes

Generate the Case Evalulation memorandum.
`

    const response = await createGeminiCompletion({
      modelName: GeminiModel.Gemini25Pro,
      message: liteReportPrompt,
      systemInstruction:
        'You are an expert medico-legal analyst. Write precisely and formally. Stay within 450 words.',
      teamId: user.teamId,
      purpose: 'case-eval-lite',
      activity: 'lite-report-generation'
    })

    logger.end('generateLiteReport')
    return response as string
  } catch (error: any) {
    logger.error('generateLiteReport', error)
    throw new Error(`Failed to generate lite report: ${error.message}`)
  }
}
