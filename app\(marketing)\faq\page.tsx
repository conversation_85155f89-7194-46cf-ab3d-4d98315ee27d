import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion'

export const metadata = {
  title: 'FAQ'
}

export default function FaqPage() {
  return (
    <section className="container flex flex-col  gap-6 py-8 md:max-w-[64rem] md:py-12 lg:py-24">
      <div className="mx-auto flex w-full flex-col gap-4 md:max-w-[58rem]">
        <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
          Frequently Asked Questions
        </h2>

        <Accordion type="single" collapsible className="w-full mt-10">
          <AccordionItem value="item-1">
            <AccordionTrigger className="font-bold lg:text-2xl">
              What is Smart Counsel?
            </AccordionTrigger>
            <AccordionContent className="tracking-wide text-lg my-8">
              Smart Counsel is an AI-powered tool that assists law professionals
              by making tasks such as legal research, document analysis,
              drafting, contract review, and Knowledge Management easier and
              faster. It uses advanced technology to streamline complex legal
              tasks, improving both accuracy and efficiency.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger className="font-bold lg:text-2xl">
              How does Smart Counsel work?
            </AccordionTrigger>
            <AccordionContent className="tracking-wide text-lg my-8">
              Smart Counsel uses proprietary AI models specifically designed for
              legal work. These models are trained with extensive legal data,
              including case law, statutes, and regulations. This allows the
              platform to understand and process legal language with a high
              degree of accuracy.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger className="font-bold lg:text-2xl">
              How accurate is Smart Counsel in legal analysis?
            </AccordionTrigger>
            <AccordionContent className="tracking-wide text-lg my-8">
              Smart Counsel uses advanced AI models specifically trained on
              legal data, delivering highly accurate legal analysis. While no
              system is perfect, Smart Counsel is continually updated to enhance
              precision. It works alongside legal professionals to speed up
              analysis, catch errors, and provide reliable results.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger className="font-bold lg:text-2xl">
              Does Smart Counsel require specialized training?
            </AccordionTrigger>
            <AccordionContent className="tracking-wide text-lg my-8">
              No, Smart Counsel features an intuitive interface designed for
              easy use without technical training. However, onboarding and
              support are available. To request a demo, email us at{' '}
              <strong>
                <a
                  href="mailto:<EMAIL>."
                  className="underline text-blue-600 dark:text-blue-400"
                >
                  <EMAIL>
                </a>
              </strong>
              .
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-5">
            <AccordionTrigger className="font-bold lg:text-2xl">
              Is Smart Counsel secure?
            </AccordionTrigger>
            <AccordionContent className="tracking-wide text-lg my-8">
              Yes, Smart Counsel is built with robust security in mind. It
              follows a strict &quot;eye-shut&quot; policy, ensuring that no
              data is stored, used, or transferred. The platform uses a secure
              system to safeguard all operations, providing users with full
              control over their data and ensuring compliance with strict
              privacy and regulatory standards.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </section>
  )
}
