import type { NextApiRequest, NextApiResponse } from 'next'
import { db } from '@/lib/db'
import { env } from '@/env.mjs'
import { DocumentRecords } from '@prisma/client'
import { storeInternalLegalTextEmbedding } from '@/lib/pg-db'
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter'
import { COURT_NAME_SPACES } from '@/config/research'
import { cleanUpString } from '@/lib/utils'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (env.NODE_ENV !== 'development') {
      throw new Error('Unauthorized')
    }

    let totalUpdated = 0

    console.log('Starting source-based update process...')

    // Loop through each source in the array
    for (const source of COURT_NAME_SPACES.US) {
      const updateResult = await processSource(source)

      totalUpdated += updateResult
      console.log(
        `Updated ${updateResult} records for source: ${source} (Total: ${totalUpdated}).`
      )
    }

    console.info('Indexing complete!')

    res.status(200).json({ message: 'Data saved successfully!' })
  } catch (error) {
    res.status(500).json({ error: 'Failed to index.' })
  }
}

async function processSource(source: string) {
  try {
    const CHUNK_SIZE = 20
    const BATCH_SIZE = 500
    let lastId = 0

    const condition = {
      meta_ready: true,
      indexed: false,
      source: source
    }

    const size = await db.documentRecords.count({
      where: condition
    })

    console.log('Total records to index: ', size)

    let totalDuration = 0
    let totalChunksProcessed = 0

    if (size === 0) {
      return 0
    }

    const firstId = await db.documentRecords.findFirst({
      select: {
        id: true
      },
      where: {
        source: source
      }
    })

    lastId = firstId!.id

    console.log('First ID: ', lastId)

    for (let i = 0; i < 500000; i += BATCH_SIZE) {
      console.log('Processing batch: ', i)
      const records = await db.documentRecords.findMany({
        select: {
          id: true,
          content: true,
          source: true,
          region: true,
          ref: true,
          meta: true
        },
        where: {
          ...condition,
          id: {
            gt: lastId
          }
        },
        take: BATCH_SIZE,
        orderBy: {
          id: 'asc'
        }
      })

      if (records.length === 0) {
        break
      } else {
        console.log(`Processing ${records.length} records...`)
      }

      lastId = records[records.length - 1].id

      for (let j = 0; j < records.length; j += CHUNK_SIZE) {
        const startTimestamp = new Date().getTime()
        const chunk = records.slice(j, j + CHUNK_SIZE)
        const promises = chunk.map((record) => indexDocument(record))
        await Promise.allSettled(promises)
        const endTimestamp = new Date().getTime()
        const duration = (endTimestamp - startTimestamp) / 1000

        totalDuration += duration
        totalChunksProcessed += 1

        const totalBatchesRemaining = Math.ceil(
          (size - totalChunksProcessed * CHUNK_SIZE) / CHUNK_SIZE
        )
        const averageChunkDuration = totalDuration / totalChunksProcessed
        const estimatedTimeRemainingMins = (
          (totalBatchesRemaining * averageChunkDuration) /
          60
        ).toFixed(2)
        const estimatedTimeRemainingHrs = (
          Number(estimatedTimeRemainingMins) / 60
        ).toFixed(2)

        console.info(
          `${source} Chunk processed in ${duration} seconds. Estimated time remaining: ${estimatedTimeRemainingMins} mins or (${estimatedTimeRemainingHrs} hrs)`
        )
      }
    }

    return size
  } catch (error: any) {
    console.log('Error processing source: ', source, error.message)
    return 0
  }
}

async function indexDocument(
  record: Pick<
    DocumentRecords,
    'id' | 'content' | 'source' | 'ref' | 'meta' | 'region'
  >
) {
  try {
    const splitter = RecursiveCharacterTextSplitter.fromLanguage('markdown', {
      chunkSize: 1000,
      chunkOverlap: 200
    })

    let textContent = cleanUpString(record.content)

    const internal = JSON.parse(record.meta)

    const splitDocuments = await splitter.createDocuments([textContent])
    let success = true

    for (const doc of splitDocuments) {
      doc.metadata = {
        ...doc.metadata,
        documentRecordsId: record.id,
        documentRecordsSource: record.source,
        refId: record.ref
      }
      const store = await storeInternalLegalTextEmbedding({
        inputText: doc.pageContent,
        region: record.region,
        namespace: record.source,
        docId: record.id,
        metadata: doc.metadata,
        year: internal.year,
        court: internal.court
      })

      console.log('Stored:', store)

      if (store === 1) {
        success = false
        break
      }
    }

    await db.documentRecords.update({
      where: {
        id: record.id
      },
      data: {
        indexed: true,
        content: textContent
      }
    })
    console.log('Indexed: ', record.id)
  } catch (error: any) {
    console.log('Error indexing: ', record.id, error.message)
  }
}
