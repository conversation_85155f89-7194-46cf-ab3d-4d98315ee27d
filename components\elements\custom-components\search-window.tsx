'use client'

import { toast } from '@/components/ui/use-toast'

import { useRef, useState } from 'react'
import * as z from 'zod'

import { Input } from '../../ui/input'
import { Icons } from '../icons'
import { cn } from '@/lib/utils'
import { buttonVariants } from '../../ui/button'
import {
  SelectYearFilter,
  SelectNamespaceFilter
} from '../chat/chat-window-form-components'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '../../ui/card'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { dbSearchSchema } from '@/lib/validations/query'

import { CaseData } from '@/types/document'
import {
  IndianLawSource,
  ResearchStoreContent,
  StoredNamespace,
  USLawSource,
  type VercelChatMessage
} from '@/types'
import type { VectorSearchConversationType } from '@/lib/actions/search'
import Link from 'next/link'
import { Region, ResearchType } from '@prisma/client'

type FormData = z.infer<typeof dbSearchSchema>

export function SearchWindow({
  region,
  vectorSearchConversation
}: {
  region: Region
  vectorSearchConversation: VectorSearchConversationType
}) {
  const messageContainerRef = useRef<HTMLDivElement | null>(null)

  const [court, setCourt] = useState<ResearchStoreContent['court']>([])
  const [year, setYear] = useState<ResearchStoreContent['year']>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [messages, setMessages] = useState<VercelChatMessage[]>([])
  const [documents, setDocuments] = useState<number[]>([])
  const [namespace, setNamespace] = useState<StoredNamespace[Region]>(
    region === 'IN'
      ? IndianLawSource.LabourLaw
      : USLawSource.FederalSupremeCourt
  )

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(dbSearchSchema)
  })

  async function onSubmit(data: FormData) {
    setIsLoading(true)

    try {
      const lastquestion =
        messages.length > 1 ? messages[messages.length - 2] : undefined

      if (lastquestion?.content !== data.query) {
        messages.push({
          id: messages.length.toString(),
          role: 'user',
          content: data.query
        })
      }

      const searchResult = await vectorSearchConversation({
        messages: messages,
        region: region,
        researchType: ResearchType.case,
        filter: {
          namespace,
          court,
          year,
          documentRecordsId: documents.length
            ? {
                $nin: documents
              }
            : undefined
        }
      })

      if (searchResult.documents.length === documents.length) {
        setIsLoading(false)
        return toast({
          title: 'No results found.',
          description: 'Your search returned no results. Please try again.',
          variant: 'destructive'
        })
      }
      setDocuments(searchResult.documents)
      setMessages(searchResult.messages)
    } catch (error: any) {
      setIsLoading(false)
      return toast({
        title: error.message || 'Something went wrong.',
        description: 'Please refresh page and try again.',
        variant: 'destructive'
      })
    }

    setIsLoading(false)
  }

  return (
    <Card>
      <div
        className={`flex flex-col items-center p-4 md:p-8 rounded grow overflow-hidden ${
          messages.length > 0 ? 'border' : ''
        }`}
      >
        {messages.length === 0 ? <InfoCard /> : ''}
        <div
          className="flex flex-col-reverse w-full mb-4 overflow-auto transition-[flex-grow] ease-in-out"
          ref={messageContainerRef}
        >
          {messages.length > 0
            ? [...messages].reverse().map((m, i) => {
                return <ChatMessageBubble key={i} message={m} />
              })
            : ''}
        </div>

        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex w-full flex-col"
        >
          <div className="flex flex-col md:flex-row w-full mt-4 gap-2">
            <SelectNamespaceFilter
              region={region}
              value={namespace}
              setValue={setNamespace}
            />

            <SelectYearFilter
              startYear={1900}
              endYear={new Date().getFullYear()}
              value={year[0]}
              setValue={(value) => setYear([value])}
            />
          </div>
          <div className="flex w-full mt-4 gap-2">
            <Input
              className="grow p-4 rounded"
              disabled={isLoading}
              placeholder={'Ask a question...'}
              {...register('query')}
            />

            <button className={cn(buttonVariants())} disabled={isLoading}>
              {isLoading && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              <span>Search</span>
            </button>

            {messages.length > 1 && (
              <>
                <button className={cn(buttonVariants())} disabled={isLoading}>
                  {isLoading && (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  <span>More</span>
                </button>

                <button
                  className={cn(buttonVariants({ variant: 'destructive' }))}
                  onClick={() => {
                    setMessages([])
                  }}
                >
                  <span>Clear</span>
                </button>
              </>
            )}
          </div>
        </form>
      </div>
    </Card>
  )
}

const InfoCard = () => {
  return (
    <section className="lg:p-8">
      <CardHeader>
        <CardTitle>Case Search Guide</CardTitle>
        <CardDescription>
          Welcome to our Advanced Case Search tool. This platform is designed
          for users who need to find cases closely related to specific queries
          or topics.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ol className="list-decimal list-inside mb-4">
          <li className="mb-2">
            Enter the key terms or questions related to your topic in the
            provided search box. The more specific your query, the more relevant
            the results.
          </li>
          <li className="mb-2">
            Press the <strong>&quot;Search&quot;</strong> button to begin the
            case retrieval.
          </li>
          <li className="mb-2">
            Browse through the list of cases that are most relevant to your
            query. Each entry will include a brief summary or excerpt for quick
            reference.
          </li>
          <li className="mb-2">
            To read the case in detail, click on the case title to open the
            entire case document in a new tab.
          </li>
          <li>
            If you have not found what you are looking for, you can add more
            keywords or load more results by clicking on the{' '}
            <strong>&quot;More&quot;</strong> button.
          </li>
        </ol>

        <p className="italic">
          Note: Your search queries are handled with strict confidentiality. All
          searches are secure and no personal data is stored after your session.
        </p>
      </CardContent>
    </section>
  )
}

function ChatMessageBubble(props: { message: VercelChatMessage }) {
  const colorClassName =
    props.message.role === 'user'
      ? 'bg-ring dark:bg-accent dark:text-white px-4'
      : 'text-black dark:text-white'
  const alignmentClassName =
    props.message.role === 'user' ? 'ml-auto' : 'mr-auto'

  return (
    <div
      className={`${alignmentClassName} ${colorClassName} rounded-lg py-2 mb-8 flex`}
    >
      <div className="whitespace-pre-wrap flex flex-col">
        {props.message.role === 'user' ? (
          <span>{props.message.content}</span>
        ) : (
          <SourceDocuments sources={props.message.content} />
        )}
      </div>
    </div>
  )
}

const SourceDocuments = ({ sources }: { sources: string }) => {
  const documents = JSON.parse(sources) as (CaseData & {
    ref: string
    pageContent: string
  })[]

  return (
    <article className="mt-10 mr-2 bg-amber-50 dark:bg-slate-900 px-2 py-3 rounded-lg">
      <h2 className="text-lg font-semibold m-2">Sources:</h2>
      <hr className="border-1 border-slate-300 dark:border-white mx-2 my-2" />
      {documents?.map((metadata, i) => {
        return (
          <Link
            href={`/document/${metadata.ref}`}
            key={'source:' + i}
            target="_blank"
          >
            <div className="mt-2 text-xs text-left border-2 p-5 hover:bg-ring hover:dark:bg-slate-700 cursor-pointer duration-200 rounded-lg">
              <p className="font-bold hover:underline">
                {i + 1}. {metadata.court}
                {metadata.date
                  ? ' - ' + new Date(metadata.date).toLocaleDateString()
                  : ''}
                {metadata.citation
                  ? ' - ' + metadata.citation.split(', Dt/')[0]
                  : ''}
              </p>

              <div className="mt-2 italic">
                &quot;
                {metadata.pageContent}
                &quot;
              </div>
            </div>
          </Link>
        )
      })}
    </article>
  )
}
