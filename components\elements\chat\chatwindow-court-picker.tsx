'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Checkbox } from '@/components/ui/checkbox'
import { Region, ResearchType } from '@prisma/client'
import { COURT_DATA, CourtData, LAW_DATA } from '@/config/research'

type CourtSelectorModalProps = {
  type: ResearchType
  region: Region
  selectedCourts: string[]
  setSelectedCourts: React.Dispatch<React.SetStateAction<string[]>>
}

export function CourtSelectorModal({
  type,
  region,
  selectedCourts,
  setSelectedCourts
}: CourtSelectorModalProps) {
  // Handle checkbox changes for individual courts
  const handleCheckboxChange = (courtValue: string) => {
    setSelectedCourts((prevSelectedCourts) => {
      if (prevSelectedCourts.includes(courtValue)) {
        return prevSelectedCourts.filter((court) => court !== courtValue)
      } else {
        return [...prevSelectedCourts, courtValue]
      }
    })
  }

  const configSet =
    type === ResearchType.case ? COURT_DATA[region] : LAW_DATA[region]

  // Handle select/deselect all courts in a courtType
  const handleSelectAllChange = (courts: CourtData[], selectAll: boolean) => {
    setSelectedCourts((prevSelectedCourts) => {
      let newSelectedCourts = [...prevSelectedCourts]

      courts.forEach((court) => {
        if (selectAll) {
          if (!newSelectedCourts.includes(court.value) && court.available) {
            newSelectedCourts.push(court.value)
          }
        } else {
          newSelectedCourts = newSelectedCourts.filter(
            (selectedCourt) => selectedCourt !== court.value
          )
        }
      })

      return newSelectedCourts
    })
  }

  // Check if a court is selected
  const isCourtSelected = (courtValue: string) =>
    selectedCourts.includes(courtValue)

  // Check if all courts in a courtType are selected
  const areAllCourtsSelected = (courts: CourtData[]) =>
    courts
      .filter((court) => court.available)
      .every((court) => selectedCourts.includes(court.value)) &&
    courts.filter((court) => court.available).length > 0

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="w min-w-fit">
          {type === ResearchType.case
            ? 'Select Courts to Research'
            : 'Select Jurisdiction to Research'}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[90%] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>
            {type === ResearchType.case
              ? 'Select Courts for research'
              : 'Select Jurisdiction to research'}
          </DialogTitle>
          <DialogDescription>
            {type === ResearchType.case
              ? 'Select the courts you would like to research in'
              : 'Select the jurisdiction you would like to research in'}
          </DialogDescription>
        </DialogHeader>
        <Tabs
          defaultValue={
            type === ResearchType.case
              ? region === Region.US
                ? 'Federal Courts'
                : 'Supreme Court'
              : 'Federal Laws'
          }
        >
          <TabsList className="grid w-full grid-cols-2">
            {Object.keys(configSet).map((courtType) => (
              <TabsTrigger
                value={courtType}
                className="text-center"
                key={courtType}
              >
                {courtType}
              </TabsTrigger>
            ))}
          </TabsList>

          {Object.entries(configSet).map(([courtType, courts]) => (
            <TabsContent
              value={courtType}
              key={courtType}
              className="max-h-[50vh] overflow-scroll"
            >
              <div className="grid gap-5 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                {Object.entries(courts).map(([court, courtData]) => (
                  <div key={court}>
                    <div className="flex items-center space-x-2 pt-3">
                      <Checkbox
                        id={courtType}
                        checked={areAllCourtsSelected(courtData)}
                        onCheckedChange={(e) =>
                          handleSelectAllChange(
                            courtData,
                            e === true ? true : false
                          )
                        }
                      />
                      <h4 className="font-semibold">{court}</h4>
                    </div>
                    <hr className="w-full border-t border-gray-300 mt-1 mb-3" />
                    {courtData.map((court) => (
                      <div
                        key={court.value}
                        className="flex items-center space-x-2 my-1.5"
                      >
                        <Checkbox
                          id={court.value}
                          disabled={!court.available}
                          checked={isCourtSelected(court.value)}
                          onCheckedChange={() =>
                            handleCheckboxChange(court.value)
                          }
                        />
                        <label
                          htmlFor={court.value}
                          className="text-sm font-medium leading-normal peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {court.title}{' '}
                          {!court.available && (
                            <span className="text-xs text-yellow-400">
                              (Coming Soon)
                            </span>
                          )}
                        </label>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="secondary">
              {type === ResearchType.case
                ? 'Continue with selected courts'
                : 'Continue with selected jurisdiction'}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
