'use client'

import * as React from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'
import { useRouter } from 'next/navigation'

import { cn } from '@/lib/utils'
import { userWelcomeSchema } from '@/lib/validations/auth'
import { buttonVariants } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { Icons } from '@/components/elements/icons'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Region } from '@prisma/client'
import { AuthUser } from 'next-auth'
import type { CreditUsageStats } from '@/types'
import { updateUserInformation } from '@/lib/actions/user'

interface UserWelcomeFormProps extends React.HTMLAttributes<HTMLDivElement> {
  user: AuthUser
  teamName?: string
  creditUsageStats?: CreditUsageStats
}

type FormData = z.infer<typeof userWelcomeSchema>

export function UserWelcomeForm({
  user,
  teamName,
  creditUsageStats,
  className,
  ...props
}: UserWelcomeFormProps) {
  const {
    setValue,
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>({
    defaultValues: {
      name: user.name || '',
      teamName: teamName,
      region: Region.US
    },
    resolver: zodResolver(userWelcomeSchema)
  })
  const [isLoading, setIsLoading] = React.useState<boolean>(false)
  const router = useRouter()

  async function onSubmit(data: FormData) {
    setIsLoading(true)

    try {
      const update = await updateUserInformation({
        userId: user.id,
        teamId: user.teamId,
        name: data.name,
        teamName: data.teamName,
        region: data.region
      })

      if (!update) {
        throw new Error('Failed to update user information.')
      }

      router.refresh()
      router.push('/dashboard')
    } catch (error: any) {
      setIsLoading(false)
      return toast({
        title: error.message || 'Something went wrong.',
        description: 'Your sign in request failed. Please try again.',
        variant: 'destructive'
      })
    }

    setIsLoading(false)

    router.refresh()
  }

  return (
    <div className={cn('grid gap-6', className)} {...props}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-6">
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              placeholder="Your Name"
              type="text"
              autoCapitalize="none"
              autoComplete="name"
              autoCorrect="off"
              disabled={isLoading}
              {...register('name')}
            />
            {errors?.name && (
              <p className="px-1 text-xs text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="teamName">Company Name</Label>
            <Input
              id="teamName"
              placeholder="My Company"
              type="text"
              autoCapitalize="none"
              autoCorrect="off"
              disabled={isLoading}
              {...register('teamName')}
            />
            {errors?.teamName && (
              <p className="px-1 text-xs text-red-600">
                {errors.teamName.message}
              </p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="region">Country</Label>
            <Select
              disabled={true}
              defaultValue={Region.US}
              onValueChange={(value: Region) => setValue('region', value)}
            >
              <SelectTrigger>
                <SelectValue id="region" placeholder="Select region" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={Region.US}>
                  <div className="flex gap-4">
                    <Icons.flagUSA className="h-6 w-6" />
                    <span>United States</span>
                  </div>
                </SelectItem>
                <SelectItem value={Region.IN}>
                  <div className="flex gap-4">
                    <Icons.flagIndia className="h-6 w-6" />
                    <span> India</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            {errors?.region && (
              <p className="px-1 text-xs text-red-600">
                {errors.region.message}
              </p>
            )}
          </div>

          <div className="grid gap-2">
            {creditUsageStats &&
              Object.entries(creditUsageStats).map(([feature, stats]) => (
                <div key={feature} className="grid gap-2">
                  <span className="text-sm capitalize font-semibold">
                    Credits available on {feature}
                  </span>
                  <span className="bg-yellow-100 text-yellow-900 text-sm font-semibold me-2 px-2.5 py-2 rounded-lg dark:bg-yellow-900 dark:text-yellow-300">
                    {stats.creditUsed > 0
                      ? `Used: ${stats.creditUsed} of `
                      : ''}
                    {stats.creditAvailable}
                  </span>
                </div>
              ))}
          </div>

          <button className={cn(buttonVariants())} disabled={isLoading}>
            {isLoading && (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            )}
            Continue
          </button>
        </div>
      </form>
    </div>
  )
}
