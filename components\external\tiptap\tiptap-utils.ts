import { parseMarkdown } from '@/lib/utils'

/**
 * Preprocesses content before feeding it to Tiptap editor
 * Converts [docId:page] citations to proper HTML elements
 *
 * @param content The raw editor content
 * @returns Processed content with citation spans
 */
export function preprocessTiptapContent(content: string): string {
  const html = parseMarkdown(
    content.replace(/markdown/g, '').replace(/```/g, '')
  )

  // Match [docId:page] pattern, e.g. [3243:3] or [3243:3-6]
  const processedContent = html.replace(
    /\[(\d+):(\d+(?:-\d+)?)\]/g,
    (_match, docId, page) => {
      return `<span data-modal-link="true" data-doc-id="${docId}" data-page="${page}"></span>`
    }
  )

  return processedContent
}

/**
 * Convert Tiptap HTML back to storable format
 * Useful when saving editor content
 *
 * @param html The HTML from Tiptap editor
 * @returns Content with citations in [docId:page] format
 */
export function postprocessTiptapContent(html: string): string {
  // Convert citation spans back to [docId:page] format
  const processedContent = html.replace(
    /<span data-modal-link="true" data-doc-id="(\d+)" data-page="(\d+(?:-\d+)?)"><\/span>/g,
    (_match, docId, page) => {
      return `[${docId}:${page}]`
    }
  )

  return processedContent
}
