import type {
  CreditType,
  Region,
  Role,
  TeamPlan,
  UserSettings
} from '@prisma/client'
import type { User } from 'next-auth'
import type { JWT } from 'next-auth/jwt'

type UserId = string

declare module 'next-auth/jwt' {
  interface JWT {
    id: UserId
  }
}

declare module 'next-auth' {
  interface UserSettingsConfig
    extends Pick<UserSettings, 'questionAssessment'> {}

  interface AuthUser extends User {
    id: UserId
    teamId: string
    plan: TeamPlan
    userType: Role
    region: Region
    settings: UserSettingsConfig
  }
  interface Session {
    user: AuthUser
    usage?: {
      [key in CreditType]?: {
        available: number
        used: number
      }
    }
  }
}
