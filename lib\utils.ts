import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import type { Document } from 'langchain/document'
import type { SupabaseExtendedMetadata } from '@/types/document'
import { env } from '@/env.mjs'
import { marked } from 'marked'
import katex from 'katex'
import { Role } from '@prisma/client'
import { DocumentTitle } from '@/types/case'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(input: string | number): string {
  const date = new Date(input)
  return date.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  })
}

export function isJsonString(str: string) {
  try {
    JSON.parse(str)
  } catch (e) {
    return false
  }
  return true
}

export function convertToFormattedText(editorData: EditorJS.OutputData) {
  let html = ''

  editorData.blocks.forEach((block) => {
    switch (block.type) {
      case 'paragraph':
        html += `<p>${block.data.text}</p>`
        break

      case 'header':
        html += `<h${block.data.level}>${block.data.text}</h${block.data.level}>`
        break

      case 'list':
        const listType = block.data.style === 'ordered' ? 'ol' : 'ul'
        html += `<${listType}>`
        block.data.items.forEach((item: string) => {
          html += `<li>${item}</li>`
        })
        html += `</${listType}>`
        break

      case 'code':
        html += `<pre><code>${block.data.code}</code></pre>`
        break

      case 'linkTool':
        html += `<a href="${block.data.link}" target="_blank">${block.data.link}</a>`
        break

      case 'inlineCode':
        html += `<code>${block.data.text}</code>`
        break

      case 'table':
        html += '<table>'
        block.data.content.forEach((row: string[]) => {
          html += '<tr>'
          row.forEach((cell) => {
            html += `<td>${cell}</td>`
          })
          html += '</tr>'
        })
        html += '</table>'
        break

      case 'embed':
        // This will depend on what your embed block data looks like.
        // For example, if it's a YouTube video:
        html += `<iframe width="560" height="315" src="${block.data.embed}" frameborder="0" allowfullscreen></iframe>`
        break

      // Add more cases based on other blocks you may have.
      default:
        console.warn(`Unknown block type: ${block.type}`)
        break
    }
  })

  return html
}

export const combineDocumentsFn = (docs: Document[]) => {
  const separator = '\n\n'
  const serializedDocs = docs.map((doc) => doc.pageContent)
  return serializedDocs.join(separator)
}

export function formSearchParams(
  searchParams: { [key: string]: string | string[] },
  addParams: { [key: string]: string | string[] }[]
) {
  const params = new URLSearchParams()

  for (const [k, v] of Object.entries(searchParams)) {
    if (Array.isArray(v)) {
      params.set(k, v.join(','))
    } else {
      params.set(k, v)
    }
  }

  for (const addParam of addParams) {
    for (const [k, v] of Object.entries(addParam)) {
      if (Array.isArray(v)) {
        params.set(k, v.join(','))
      } else {
        params.set(k, v)
      }
    }
  }
  return '?' + params.toString()
}

export function textToHTML(text: string) {
  const lines = text.split('\n\n')
  let html = ''

  lines.forEach((line) => {
    if (line.trim().length === 0) {
      html += '<br>' // Add a line break for empty lines
    } else if (line.startsWith('## ')) {
      // Assuming '## ' denotes a subheading
      html += `<h2>${line.substring(3)}</h2>`
    } else if (line.startsWith('* ')) {
      // Assuming '* ' denotes a bullet point
      html += `<li>${line.substring(2)}</li>`
    } else {
      html += `<p>${line}</p>` // Treat as a paragraph
    }
  })

  // Wrap list items in <ul> tags
  html = html.replace(/<li>(.*?)<\/li>/g, (match, item) => {
    return `<ul>${match}</ul>`
  })

  return html
}

export function generateCaseTitle({
  metadata,
  title,
  year,
  i
}: {
  metadata: SupabaseExtendedMetadata
  title: string
  year: number

  i?: number
}) {
  const caseMetadata = metadata.metadata

  const fileName = (caseMetadata.fileName || '').split('.pdf')[0]
  const court = caseMetadata.court ? ` - ${caseMetadata.court}` : ''
  const yearStr = year && year !== 1000 ? ` - ${year}` : ''

  // const formattedCitation = caseMetadata.citation?.split(', Dt/')[0]
  //   ? ` - ${caseMetadata.citation.split(', Dt/')[0]}`
  //   : ''

  const caseTitle = `${i !== undefined ? i + 1 : ''}. ${fileName}${title}${court}${yearStr}`

  return caseTitle
}

export const developer = {
  log: (texts: any[]): void => {
    if (env.NODE_ENV === 'development') {
      texts.forEach((text) => console.log(text))
    }
  },
  error: (texts: any[]): void => {
    if (env.NODE_ENV === 'development') {
      texts.forEach((text) => console.log(text))
    }
  }
}

export const admin = {
  log: (role: Role, texts: any[]): void => {
    if (role === 'admin') {
      texts.forEach((text) => console.log(text))
    }
  },
  error: (role: Role, texts: any[]): void => {
    if (role === 'admin') {
      texts.forEach((text) => console.log(text))
    }
  }
}

function renderMath(formula: string): string {
  return katex.renderToString(formula, {
    throwOnError: false,
    displayMode: true
  })
}

export function parseMarkdown(text: string) {
  const mathRegex = /\$\$([^$]+)\$\$|\\\[([^\]]+)\\\]|\\\(([^)]+)\\\)/g

  const parsedContent = text.replace(mathRegex, (match, p1, p2, p3) => {
    const formula = p1 || p2 || p3
    if (formula) {
      return renderMath(`\\displaystyle ${formula}`) // Force display math mode
    }
    return match // Return original match if no formula is found
  })
  let occurrenceIndex = 0
  return (
    marked
      .parse(parsedContent)
      .toString()
      // Replace [[...]] with clickable links to citations
      .replace(/\[\[(.*?)\]\]/g, (match, p1) => {
        occurrenceIndex += 1
        const safeP1 = p1.match(/\d+/g)?.[0]
        return `<a class="text-blue-500 text-xs cursor-pointer" onClick="(function() {document.querySelector(\`[x-data='${safeP1}']\`)?.click()})()"
        >[${occurrenceIndex}]</a>`
      })
      // Style table with tailwind classes
      .replace(/<th>/g, '<th class="px-4 py-2 font-medium bg-slate-200">')
      .replace(/<td>/g, '<td class="border px-4 py-2">')
      .replace(/<table>/g, '<table class="border-collapse my-4">')
      // Add tailwind classes to lists to style them
      .replace(/<ul>/g, '<ul class="ml-6 list-disc">')
      .replace(/<ol>/g, '<ol class="ml-6 list-decimal">')
  )
}

export function addAvailability(data: any, availableValues: string[]) {
  // Helper function to check availability of individual court entries
  function checkAvailability(court: any) {
    // If the court's value exists in the availableValues array, set available to true, else false
    court.available = availableValues.includes(court.value)
  }

  // Recursively process the main object
  function processCourts(courts: any) {
    // Iterate over the keys of the object, which could be circuit names or state names
    for (let key in courts) {
      if (Array.isArray(courts[key])) {
        // If the value is an array (e.g., a list of court objects), process each court
        courts[key].forEach(checkAvailability)
      } else {
        // If the value is another nested object (e.g., another level of circuits/states), recurse
        processCourts(courts[key])
      }
    }
  }

  // Start the recursive process
  processCourts(data)
  // const updatedData = addAvailability(COURT_DATA.US, available)
  return data
}

export function cleanUpString(input: string, limit?: number): string {
  // Replace multiple spaces with a single space
  let cleaned = input.replace(/\s+/g, ' ')

  // Replace multiple newlines (with or without spaces) to a single newline
  cleaned = cleaned.replace(/\n\s*\n+/g, '\n')

  // Remove double spaces after periods
  cleaned = cleaned.replace(/\.\s*\./g, '.')

  // Trim any leading or trailing spaces/newlines from the final result
  cleaned = cleaned.trim()

  // If a character limit is specified, truncate the cleaned string
  if (limit) {
    cleaned = cleaned.substring(0, limit)
  }

  return cleaned
}

export function fileToUint8Array(file: File): Promise<Uint8Array> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = () => {
      if (reader.result instanceof ArrayBuffer) {
        resolve(new Uint8Array(reader.result))
      } else {
        reject(new Error('Unexpected result type from FileReader.'))
      }
    }

    reader.onerror = () => {
      reject(new Error('An error occurred while reading the file.'))
    }

    reader.readAsArrayBuffer(file)
  })
}

/**
 * Prepares markdown content by replacing document ID references with document titles
 * @param content Markdown content
 * @param documents List of documents with titles
 * @returns Processed markdown content with document titles
 */
export const prepareMarkdown = (
  content: string,
  documents: DocumentTitle[] = []
): string => {
  // Early return if no content or documents
  if (!content || documents.length === 0) return content

  // Regular expression to match document references in the format [docId:page] or [docId:page-range]
  const docRefRegex = /\[(\d+):(\d+(?:-\d+)?)\]/g

  // Replace all occurrences of the pattern with document titles
  return content.replace(docRefRegex, (match, docId, page) => {
    // Find the document with the matching ID
    const document = documents.find((doc) => doc.id === docId)

    // If document is found, replace with document title, otherwise keep the original reference
    return document
      ? `<span class="text-blue-500 text-sm">${document.title}</span>`
      : match
  })
}

/**
 * Processes citations in markdown content by numbering them and creating a citation list
 * @param content Markdown content with citations in [docId:page] format
 * @param documents List of documents with titles
 * @returns Object containing processed content and citations list
 */
export const processCitations = (
  content: string,
  documents: DocumentTitle[] = []
): { processedContent: string; citations: string[] } => {
  // Early return if no content
  if (!content) return { processedContent: content, citations: [] }

  // Regular expression to match document references in the format [docId:page] or [docId:page-range]
  const docRefRegex = /\[(\d+):(\d+(?:-\d+)?)\]/g

  // Map to store unique citations with their assigned numbers
  const citationMap = new Map<string, number>()
  const citationsList: string[] = []
  let citationNumber = 1

  // First pass: Find all unique citations and assign numbers
  const matches = Array.from(content.matchAll(docRefRegex))

  matches.forEach((match) => {
    const [fullMatch, docId, page] = match
    const citationKey = `${docId}:${page}`

    if (!citationMap.has(citationKey)) {
      citationMap.set(citationKey, citationNumber)

      // Find the document title
      const document = documents.find((doc) => doc.id === parseInt(docId))
      const documentTitle = document ? document.title : `Document ${docId}`

      // Add to citations list with anchor ID
      citationsList.push(
        `<span id="citation-${citationNumber}" class='text-blue-500 text-sm'>[${citationNumber}]</span> ${documentTitle}, page ${page}`
      )
      citationNumber++
    }
  })

  // Second pass: Replace citations with numbers
  const processedContent = content.replace(
    docRefRegex,
    (match, docId, page) => {
      const citationKey = `${docId}:${page}`
      const number = citationMap.get(citationKey)
      return `<sup><a href="#citation-${number}" class="text-blue-600 hover:text-blue-800 no-underline">[${number}]</a></sup>`
    }
  )

  return { processedContent, citations: citationsList }
}

export function calculateEstimatedStepPosition({
  lastUpdatedAt,
  duration
}: {
  lastUpdatedAt: Date | null
  duration: number
}): number {
  if (!lastUpdatedAt) return 0

  const now = new Date()
  const elapsedMs = now.getTime() - lastUpdatedAt.getTime()
  const elapsedSeconds = elapsedMs / 1000

  // Total process duration: 5 hours = 18,000 seconds
  const totalProcessDurationSeconds = duration * 60 * 60
  const totalSteps = 15
  const secondsPerStep = totalProcessDurationSeconds / totalSteps

  // Calculate current step (0-based index)
  const currentStep = Math.floor(elapsedSeconds / secondsPerStep)

  // Ensure step is within bounds
  return Math.min(Math.max(currentStep, 1), totalSteps - 1)
}
