import { notFound } from 'next/navigation'
import { db } from '@/lib/db'
import { ShowCauseOrderEditor } from '@/components/elements/showcause-order-editor'

interface ShowCauseOrderPageProps {
  params: { showCauseId: string }
}

export default async function ShowCauseOrderPage({
  params
}: ShowCauseOrderPageProps) {
  const dataProps = await db.showCauseNotice.findUnique({
    where: {
      id: params.showCauseId
    }
  })

  if (!dataProps) {
    notFound()
  }

  return (
    <ShowCauseOrderEditor
      showCauseNoticeOrder={{
        id: dataProps.id,
        title: dataProps.title,
        orderLength: dataProps.orderLength,
        processedLength: dataProps.processedLength,
        finalJudgementSummary: dataProps.finalJudgementSummary,
        status: dataProps.status
      }}
    />
  )
}
