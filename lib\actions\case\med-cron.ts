'use server'

import { CaseFileType, QueuedEventStatus } from '@prisma/client'
import { generateMedicalChronology } from '../../processes/med-cron-generator'
import { db } from '../../db'
import { UnauthorizedError } from '../../exceptions'
import { getCurrentUser } from '../../session'
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library'
import { AuthUser } from 'next-auth'

export async function requestMedicalChronologyGeneration(
  binderId: string,
  selectedDocumentsByType: Record<string, string[]>,
  strategyInputs?: any
) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    // Use a transaction for atomicity and consistency
    return await db.$transaction(async (tx) => {
      // Use upsert pattern to eliminate the find-then-update/create pattern
      const caseFile = await tx.caseFile.upsert({
        where: {
          binderId_fileType: {
            binderId,
            fileType: CaseFileType.MEDICAL_CHRONOLOGY
          }
        },
        update: {
          selectedDocumentsByType
        },
        create: {
          binderId,
          fileType: CaseFileType.MEDICAL_CHRONOLOGY,
          creatorId: user.id,
          selectedDocumentsByType
        }
      })

      // Create queue entry
      const queue = await tx.queuedEventProcess.create({
        data: {
          type: 'medical-chronology',
          payload: JSON.stringify({
            user,
            payload: {
              binderId,
              caseFileId: caseFile.id,
              selectedDocumentsByType,
              strategyInputs
            }
          })
        }
      })

      // Update the case file with queue ID
      await tx.caseFile.update({
        where: { id: caseFile.id },
        data: { queueId: queue.id }
      })

      return {
        success: true,
        caseFileId: caseFile.id,
        queueId: queue.id
      }
    })
  } catch (error) {
    console.error('Failed to request medical chronology generation:', error)

    // Improved error handling with more specific error types
    if (error instanceof UnauthorizedError) {
      return { success: false, error: error.message }
    } else if (error instanceof PrismaClientKnownRequestError) {
      return {
        success: false,
        error: 'Database operation failed',
        details: error.message
      }
    }

    return {
      success: false,
      error: 'Failed to request medical chronology generation'
    }
  }
}

export async function handleMedicalChronologyGeneration({
  processId,
  user,
  payload: { binderId, caseFileId, selectedDocumentsByType, strategyInputs }
}: {
  processId: string
  user: AuthUser
  payload: {
    binderId: string
    caseFileId: string
    selectedDocumentsByType: Record<string, string[]>
    strategyInputs?: any
  }
}) {
  const startTime = Date.now()
  console.log(
    `🔍 Starting medical chronology generation for binder: ${binderId}`
  )

  try {
    // console.log(`📄 Extracting document IDs from selection`)
    // Extract all document IDs and convert to numbers in one step
    const allDocumentIds = Object.values(selectedDocumentsByType)
      .flat()
      .map(Number)

    // console.log(`🔍 Fetching ${allDocumentIds.length} documents from database`)
    // Fetch all documents in a single query
    const documents = await db.documentRecords.findMany({
      where: {
        id: {
          in: allDocumentIds
        }
      },
      select: {
        id: true,
        title: true,
        indexed: true,
        content: true
      }
    })
    // console.log(`✅ Successfully retrieved ${documents.length} documents`)

    // Organize documents by type
    // console.log(`🔧 Organizing documents by type for processing`)
    const documentContentByType = Object.fromEntries(
      Object.entries(selectedDocumentsByType).map(([docType, docIds]) => [
        docType,
        documents.filter((doc) => docIds.includes(doc.id.toString()))
      ])
    )

    // console.log(`🧠 Generating medical chronology from documents`)
    const medicalChronology = await generateMedicalChronology(
      documentContentByType,
      user,
      strategyInputs
    )
    // console.log(`✅ Medical chronology generated successfully`)

    // console.log(`💾 Preparing case file entries for storage`)
    const caseFileEntries = [
      {
        fileType: CaseFileType.PLAINTIFF_INFO,
        content: JSON.stringify(medicalChronology.plaintiffInfo)
      },
      {
        fileType: CaseFileType.MEDICAL_CHRONOLOGY,
        content: medicalChronology.markdownReport
      },
      {
        fileType: CaseFileType.INCIDENT_DETAILS,
        content: JSON.stringify(medicalChronology.incidentDetails)
      },
      {
        fileType: CaseFileType.EVENTS,
        content: JSON.stringify(medicalChronology.events)
      },
      {
        fileType: CaseFileType.SOURCE_LINKS,
        content: JSON.stringify(medicalChronology.sourceLinks)
      },
      {
        fileType: CaseFileType.CASE_GAPS,
        content: JSON.stringify(medicalChronology.caseGaps)
      }
    ]

    // console.log(`📝 Storing ${caseFileEntries.length} case file entries`)
    await Promise.all(
      caseFileEntries.map(async ({ fileType, content }, index) => {
        console.log(
          `🔄 Storing case file entry ${index + 1}/${caseFileEntries.length}: ${fileType}`
        )
        await db.caseFile.upsert({
          where: {
            binderId_fileType: { binderId, fileType }
          },
          update: {
            content,
            updatedAt: new Date()
          },
          create: {
            binderId,
            creatorId: user.id,
            fileType,
            content
          }
        })
      })
    )
    // console.log(`✅ All case file entries stored successfully`)

    // console.log(`💰 Recording credit usage for team: ${user.teamId}`)
    await db.teamCreditUsed.create({
      data: {
        teamId: user.teamId,
        type: 'case',
        refId: caseFileId,
        eventId: new Date().getTime().toString()
      }
    })
    // console.log(`✅ Credit usage recorded successfully`)

    const endTime = Date.now()
    const duration = (endTime - startTime) / 1000
    console.log(
      `✅ Medical chronology generation completed in ${duration} seconds`
    )

    // Update queue status to completed
    await db.queuedEventProcess.update({
      where: { id: processId },
      data: {
        status: QueuedEventStatus.completed,
        response: JSON.stringify({
          duration,
          documentCount: documents.length,
          generatedAt: new Date().toISOString()
        })
      }
    })
    // console.log(`✅ Queue process marked as completed`)
  } catch (error) {
    const endTime = Date.now()
    const duration = (endTime - startTime) / 1000
    console.error(`❌ Error generating medical chronology:`, error)

    // Update queue status to failed
    await db.queuedEventProcess.update({
      where: { id: processId },
      data: {
        status: QueuedEventStatus.failed,
        response: JSON.stringify({
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          duration
        })
      }
    })
    // console.log(`❌ Queue process marked as failed`)
  } finally {
    await db.caseFile.update({
      where: { id: caseFileId },
      data: {
        queueId: null
      }
    })
  }
}

/**
 * Saves updated content for a medical chronology
 */
export async function saveCaseFileContent(caseId: string, content: string) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    // Update the medical chronology content
    const updatedChronology = await db.caseFile.update({
      where: { id: caseId },
      data: { content }
    })

    return {
      success: true,
      medicalChronology: updatedChronology
    }
  } catch (error) {
    console.error('Failed to save medical chronology content:', error)
    return {
      success: false,
      error: 'Failed to save medical chronology content'
    }
  }
}

export async function checkMedicalChronologyStatus(
  binderId: string,
  lastUpdatedAt?: Date
) {
  try {
    const medicalChronology = await db.caseFile.findUnique({
      where: {
        binderId_fileType: {
          binderId,
          fileType: CaseFileType.MEDICAL_CHRONOLOGY
        }
      },
      select: {
        content: true,
        updatedAt: true,
        queueId: true
      }
    })

    if (!medicalChronology) {
      return { success: false, content: null, isUpdated: false }
    }

    const isUpdated =
      lastUpdatedAt !== undefined
        ? medicalChronology.updatedAt > lastUpdatedAt
        : !!medicalChronology.content

    return {
      success: true,
      content: medicalChronology.content,
      updatedAt: medicalChronology.updatedAt,
      queueId: medicalChronology.queueId,
      isUpdated: isUpdated && !!medicalChronology.content
    }
  } catch (error) {
    console.error('Error checking medical chronology status:', error)
    return { success: false, content: null, isUpdated: false }
  }
}
