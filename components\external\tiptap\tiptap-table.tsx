import Table from '@tiptap/extension-table'
import TableHeader from '@tiptap/extension-table-header'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import { mergeAttributes } from '@tiptap/core'

const StyledTable = Table.extend({
  renderHTML({ HTMLAttributes }) {
    return [
      'table',
      mergeAttributes(HTMLAttributes, {
        class: 'table-auto border-collapse w-full text-left rounded'
      }),
      0
    ]
  }
}).configure({
  resizable: true
})

const StyledTableHeader = TableHeader.extend({
  renderHTML({ HTMLAttributes }) {
    return [
      'th',
      mergeAttributes(HTMLAttributes, {
        class: 'bg-slate-200 px-4 py-2 font-medium'
      }),
      0
    ]
  }
})

const StyledTableRow = TableRow.extend({
  renderHTML({ HTMLAttributes }) {
    return ['tr', HTMLAttributes, 0]
  }
})

const StyledTableCell = TableCell.extend({
  renderHTML({ node, HTMLAttributes }) {
    const tag = node.attrs.header ? 'th' : 'td'
    const baseClass = node.attrs.header
      ? 'px-4 py-2 font-medium text-slate-700'
      : 'border px-4 py-2'

    return [
      tag,
      mergeAttributes(HTMLAttributes, {
        class: baseClass
      }),
      0
    ]
  }
})

export const TableExtensions = [
  StyledTable,
  StyledTableHeader,
  StyledTableRow,
  StyledTableCell
]
