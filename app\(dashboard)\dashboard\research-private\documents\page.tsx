import { getCurrentUser } from '@/lib/session'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { db } from '@/lib/db'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { PrivateUploadButton } from '@/components/elements/buttons/private-upload-button'
import { UploadedDatasetsTable } from '@/components/elements/uploaded-datasets-table'
import { PrivateGDriveButton } from '@/components/elements/buttons/private-gdrive-button'
import { UploadedDocumentsTable } from '@/components/elements/document/uploaded-documents-table'

export const metadata = {
  title: 'Settings',
  description: 'Manage account and website settings.'
}

export default async function SettingsPage() {
  const user = await getCurrentUser()

  const documents = await db.documentRecords.findMany({
    select: {
      id: true,
      title: true,
      indexed: true,
      createdAt: true
    },
    where: {
      source: user!.teamId,
      region: user!.region
    },
    orderBy: {
      title: 'asc'
    }
  })

  const datasets = (
    await db.dataset.findMany({
      where: {
        createdBy: user!.teamId
      },
      select: {
        id: true,
        name: true,
        createdAt: true,
        DocumentRecordDatasetMap: true
      }
    })
  ).map((dataset) => ({
    id: dataset.id,
    title: dataset.name,
    createdAt: dataset.createdAt,
    docCount: dataset.DocumentRecordDatasetMap.length
  }))

  return (
    <DashboardShell>
      <div className="flex justify-between">
        <DashboardHeader
          heading="Uploaded Documents"
          text="Manage uploaded files and datasets."
        />
        <PrivateUploadButton className="flex" />
      </div>

      <div className="flex gap-3">
        <PrivateGDriveButton />
      </div>

      <Tabs defaultValue="uploaded_docs">
        <TabsList className="w-full mb-5">
          <TabsTrigger value="uploaded_docs" className="w-1/2">
            Uploaded Documents
          </TabsTrigger>
          <TabsTrigger value="datasets" className="w-1/2">
            Datasets
          </TabsTrigger>
        </TabsList>

        <TabsContent value="uploaded_docs">
          <div className="grid gap-10 grid-cols-1">
            {documents && documents.length > 0 ? (
              <UploadedDocumentsTable documents={documents} />
            ) : (
              <div className="mx-auto flex flex-col gap-4 rounded-lg border p-5">
                <h3 className="font-bold text-lg">
                  Start by uploading documents
                </h3>
                <p>
                  You can upload documents to use in your research. These
                  documents will be used to generate insights and answers for
                  your queries.
                </p>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="datasets">
          <div className="grid gap-10 grid-cols-1">
            {datasets && datasets.length > 0 ? (
              <UploadedDatasetsTable datasets={datasets} />
            ) : (
              <div className="mx-auto flex flex-col gap-4 rounded-lg border p-5">
                <h3 className="font-bold text-lg">
                  Start by creating datasets
                </h3>
                <p>
                  You can upload datasets to use in your research. These
                  datasets will be used to generate insights and answers for
                  your queries.
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}
