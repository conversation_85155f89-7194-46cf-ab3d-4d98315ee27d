import { NextRequest, NextResponse } from 'next/server'
import * as htmlDocx from 'html-docx-js'
import { marked } from 'marked'

export async function POST(request: NextRequest) {
  try {
    const { content, filename = 'Document' } = await request.json()

    if (!content) {
      return NextResponse.json(
        { message: 'Missing document content' },
        { status: 400 }
      )
    }

    const contentHtml = await marked.parse(content)

    // Add proper DOCTYPE and HTML structure to ensure valid document
    const htmlContent = `<!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
    </head>
    <body>
      ${contentHtml}
    </body>
    </html>`

    // Generate the docx blob
    const docxBlob = htmlDocx.asBlob(htmlContent) as Blob

    // Convert blob to array buffer
    const arrayBuffer = await docxBlob.arrayBuffer()

    // Create response with the binary data directly
    const response = new NextResponse(arrayBuffer)

    // Set appropriate headers
    response.headers.set(
      'Content-Disposition',
      `attachment; filename="${encodeURIComponent(filename)}.docx"`
    )
    response.headers.set(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    )

    return response
  } catch (error) {
    console.error('Error generating DOCX:', error)
    return NextResponse.json(
      { message: 'Failed to generate document', error: String(error) },
      { status: 500 }
    )
  }
}
