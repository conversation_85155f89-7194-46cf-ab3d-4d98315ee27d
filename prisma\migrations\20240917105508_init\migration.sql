-- migration.sql

-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create the US VectorStore table
CREATE TABLE "USVectorStore" (
  "id" SERIAL PRIMARY KEY,
  "namespace" TEXT NOT NULL,
  "docId" INT NOT NULL,
  "year" INT,
  "court" TEXT,
  "metadata" JSONB,
  "chunkText" TEXT NOT NULL,
  "embedding" VECTOR(1536) NOT NULL,
  "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX us_vectorstore_embedding_idx
ON "USVectorStore"
USING hnsw ("embedding" vector_cosine_ops);

CREATE INDEX us_vectorstore_namespace_idx ON "USVectorStore" ("namespace");
CREATE INDEX us_vectorstore_year_idx ON "USVectorStore" ("year");
CREATE INDEX us_vectorstore_court_idx ON "USVectorStore" ("court");

-- Create the Indian VectorStore table
CREATE TABLE "INVectorStore" (
  "id" SERIAL PRIMARY KEY,
  "namespace" TEXT NOT NULL,
  "docId" INT NOT NULL,
  "year" INT,
  "court" TEXT,
  "metadata" JSONB,
  "chunkText" TEXT NOT NULL,
  "embedding" VECTOR(1536) NOT NULL,
  "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX in_vectorstore_embedding_idx
ON "INVectorStore"
USING hnsw ("embedding" vector_cosine_ops);

CREATE INDEX in_vectorstore_namespace_idx ON "INVectorStore" ("namespace");
CREATE INDEX in_vectorstore_year_idx ON "INVectorStore" ("year");
CREATE INDEX in_vectorstore_court_idx ON "INVectorStore" ("court");

-- Create the generic VectorStore table
CREATE TABLE "UserVectorStore" (
  "id" SERIAL PRIMARY KEY,
  "teamid" TEXT NOT NULL,
  "docId" INT NOT NULL,
  "metadata" JSONB,
  "chunkText" TEXT NOT NULL,
  "embedding" VECTOR(1536) NOT NULL,
  "createdAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX uservectorstore_embedding_idx 
ON "UserVectorStore" 
USING hnsw ("embedding" vector_cosine_ops);

CREATE INDEX uservectorstore_teamid_docid_idx ON "UserVectorStore" ("teamid", "docId");