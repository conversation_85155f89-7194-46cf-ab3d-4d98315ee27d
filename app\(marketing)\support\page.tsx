export const metadata = {
  title: 'Pricing'
}

export default function PricingPage() {
  return (
    <section className="container flex flex-col  gap-6 py-8 md:max-w-[64rem] md:py-12 lg:py-24">
      <div className="mx-auto flex w-full flex-col gap-4 md:max-w-[58rem]">
        <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
          Need help?
        </h2>
        {/* <p className="max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7">
          Unlock all features including Legal Research, Deposition Preparation,
          Document Upload, Contract Revision and contextual database search.
        </p> */}
      </div>

      <div className="mx-auto flex w-full max-w-[58rem] flex-col gap-4">
        <p className="max-w-[85%] leading-normal text-muted-foreground sm:leading-7">
          At SmartCounsel, we are continuously enhancing our product with new
          datasets and improvements to our proprietary models. If you encounter
          a response that does not meet your expectations, please let us know by
          contacting us at{' '}
          <strong>
            <a
              href="mailto:<EMAIL>"
              className="underline text-blue-600 dark:text-blue-400"
            >
              <EMAIL>
            </a>
          </strong>
          , and we will address it promptly. While we are in the early stages of
          our journey with a lean team, you can count on a swift response to
          your inquiries as we strive to deliver the best experience possible.
        </p>
      </div>
      <div className="mx-auto flex w-full max-w-[58rem] flex-col gap-4">
        <p className="max-w-[85%] leading-normal text-muted-foreground sm:leading-7">
          <strong>
            Our AI models extend from OpenAI and are thoroughly reviewed by
            legal experts. If you come across any disparities in our results,
            please reach out to us.
          </strong>
        </p>
      </div>
    </section>
  )
}
