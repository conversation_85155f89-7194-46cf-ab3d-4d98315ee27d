import * as z from 'zod'
import { getCurrentUserResponse } from '@/lib/session'
import { errorHandler } from '@/lib/exception-handler'
import {
  createNewNewsletter,
  findNewslettersWithUserId
} from '@/lib/recordstore-newsletter'

const newsletterCreateSchema = z.object({
  title: z.string(),
  content: z.string().optional()
})

export async function GET() {
  try {
    const user = await getCurrentUserResponse()
    const newsletters = await findNewslettersWithUserId({
      userId: user.id
    })

    return new Response(JSON.stringify(newsletters))
  } catch (error) {
    return errorHandler(error)
  }
}

export async function POST(req: Request) {
  try {
    const user = await getCurrentUserResponse()
    const json = await req.json()
    const body = newsletterCreateSchema.parse(json)

    const post = await createNewNewsletter({
      title: body.title,
      content: body.content || '',
      userId: user.id
    })

    return new Response(JSON.stringify(post))
  } catch (error) {
    return errorHandler(error)
  }
}
