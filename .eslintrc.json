{
  "extends": ["next/core-web-vitals"],
  "plugins": ["spellcheck"],
  "rules": {
    "@typescript-eslint/no-explicit-any": "off",
    "spellcheck/spell-checker": [
      "warn",
      {
        // Check only strings & templates (no comments/identifiers)
        "comments": false,
        "strings": true,
        "templates": true,
        "identifiers": false,
        "lang": "en_US",
        "minLength": 3,

        // 1) Fully skip these known words/tokens
        "skipWords": [
          "Adhiniyam",
          "Arial",
          "aws",
          "bluedark",
          "Bharatiya",
          "bulletpoints",
          "calc",
          "checkbox",
          "cmdk",
          "cri",
          "datasets",
          "dataset",
          "deleteing",
          "docx",
          "Drumroll",
          "editorjs",
          "embeddings",
          "favicon",
          "ffffff",
          "gdrive",
          "gst",
          "Hon'ble",
          "href",
          "ico",
          "iframe",
          "indian",
          "injest",
          "isplaystyle",
          "jcard",
          "judgement",
          "labour",
          "linkover",
          "lucide",
          "mailto",
          "microsoft",
          "mins",
          "Nagarik",
          "namespace",
          "nofollow",
          "noindex",
          "Noprint",
          "Nyaya",
          "oauth2",
          "officedocument",
          "openaimono",
          "openxmlformats",
          "Prisma",
          "radix",
          "revalidate",
          "resize",
          "rtl",
          "sakshya",
          "salesiq",
          "scrollbar",
          "semibold",
          "sublinks",
          "Sanhita",
          "smartcounsel",
          "Sakshya",
          "summarise",
          "Suraksha",
          "teamid",
          "timelines",
          "txt",
          "utf",
          "vnd",
          "woff2",
          "wordprocessingml",
          "zoho",
          "antialiased",
          "enquiry",
          "lte",
          "htm",
          "xlsx",
          "whitespace",
          "nowrap",
          "charset",
          "Helvetica",
          "rgb",
          "6zm",
          "3zm44",
          "1zm",
          "7zm32",
          "llr",
          "taxindiaonline",
          "tio",
          "brainstem",
          "federalcourts",
          "ussupremecourt",
          "supremecourtofindia",
          "latin",
          "bak",
          "blockquote",
          "labourlawreporter",
          "courtlistener",
          "justicia",
          "num",
          "headnotes",
          "evenodd",
          "zsiqscript",
          "Dropdown",
          "Textarea",
          "pdf'",
          "signups",
          "stdout"
        ],

        // 2) Skip sub-words if they match these regexes
        "skipWordIfMatch": [
          "^(2|3|4|5|6|7)xl$", // e.g. 2xl, 3xl
          "^[0-9]+(vh|vw|dvh)$", // e.g. 80vh, 100vw
          "^[0-9]+(px|em|rem|%)$", // e.g. 100px in sub-words
          "^mailto$",
          "^pdf$",
          "^docx$",
          "^gpt$",
          "^jsonl$",
          "^noprint$",
          "^svg$",
          "^cmdk$",
          "^cri$",
          "^scotus$",
          "^gst$",
          "^zoho$",
          "^salesiq$",
          "^href$",
          "^ico$",
          "^rtl$"
        ],

        // 3) Skip entire tokens if they match these regexes
        "skipIfMatch": [
          "http://[^\\s]*", // URLs
          "^[-\\w]+/[-\\w\\.]+$", // file paths
          "^#[0-9a-fA-F]{3,6}$", // hex colors
          "^rgba?\\([^)]+\\)$", // rgb(...)
          "^hsl\\([^)]+\\)$", // hsl(...)
          "^[a-z]+\\([^)]+\\)$", // e.g. calc()
          "^(Zm2|Zm3)[^a-zA-Z0-9]*$" // e.g. 2zm, 3zm
        ]
      }
    ]
  }
}
