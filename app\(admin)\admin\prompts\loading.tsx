import { CardSkeleton } from '@/components/elements/custom-components/card-skeleton'
import { <PERSON>boardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'

export default function DashboardSettingsLoading() {
  return (
    <DashboardShell>
      <DashboardHeader heading="Prompts" text="Manage prompts." />
      <div className="grid gap-10">
        <CardSkeleton />
      </div>
    </DashboardShell>
  )
}
