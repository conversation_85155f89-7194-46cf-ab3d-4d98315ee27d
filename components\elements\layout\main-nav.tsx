'use client'

import * as React from 'react'
import Link from 'next/link'
import { useSelectedLayoutSegment } from 'next/navigation'

import { MainNavItem } from '@/types'
import { cn } from '@/lib/utils'
import { Icons } from '@/components/elements/icons'
import { MobileNav } from '@/components/elements/layout/mobile-nav'

interface MainNavProps {
  items?: MainNavItem[]
  home?: string
  children?: React.ReactNode
}

export function MainNav({ items, home, children }: MainNavProps) {
  const segment = useSelectedLayoutSegment()
  const [showMobileMenu, setShowMobileMenu] = React.useState<boolean>(false)
  const menuRef = React.useRef<HTMLDivElement | null>(null)

  React.useEffect(() => {
    const clickOutside = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        setShowMobileMenu(false)
      }
    }
    if (showMobileMenu) {
      document.addEventListener('click', clickOutside)
    }

    return () => {
      document.removeEventListener('click', clickOutside)
    }
  }, [showMobileMenu])

  return (
    <div className="flex gap-6 lg:gap-10">
      <Link
        href={home || '/'}
        className="hidden items-center space-x-2 lg:flex"
      >
        <Icons.logoWide className="h-10" width={250} />
      </Link>
      {items?.length ? (
        <nav className="hidden gap-6 lg:flex">
          {items?.map((item, index) => (
            <Link
              key={index}
              href={item.disabled ? '#' : item.href}
              target={item.href.startsWith('http') ? '_blank' : undefined}
              className={cn(
                'flex items-center text-lg font-medium transition-colors hover:text-ring sm:text-sm',
                item.href.startsWith(`/${segment}`) ? 'text-ring' : '',
                item.disabled && 'cursor-not-allowed opacity-80'
              )}
            >
              {item.title}
            </Link>
          ))}
        </nav>
      ) : null}
      <button
        className="flex items-center space-x-2 lg:hidden"
        onClick={() => setShowMobileMenu(!showMobileMenu)}
      >
        {showMobileMenu ? <Icons.close /> : <Icons.menu />}
        <span className="font-bold">Menu</span>
      </button>
      {showMobileMenu && items && (
        <MobileNav items={items} menuRef={menuRef}>
          {children}
        </MobileNav>
      )}
    </div>
  )
}
