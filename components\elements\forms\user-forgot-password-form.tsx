'use client'

import * as React from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { cn } from '@/lib/utils'
import { userForgotPasswordSchema } from '@/lib/validations/auth'
import { buttonVariants } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { Icons } from '@/components/elements/icons'
import { validateEmailAndSendPasswordResetEmail } from '@/lib/actions/user'

interface UserForgotPasswordFormProps
  extends React.HTMLAttributes<HTMLDivElement> {}

type FormData = z.infer<typeof userForgotPasswordSchema>

export function UserForgotPasswordForm({
  className,
  ...props
}: UserForgotPasswordFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(userForgotPasswordSchema)
  })
  const [isLoading, setIsLoading] = React.useState<boolean>(false)

  async function onSubmit(data: FormData) {
    setIsLoading(true)
    try {
      const sendMail = await validateEmailAndSendPasswordResetEmail({
        email: data.email
      })

      if (sendMail?.MessageID) {
        toast({
          title: 'Email Sent',
          description: 'Check your email for a password reset link'
        })
      } else throw new Error('An error occurred while setting the password')
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An error occurred while sending the email',
        variant: 'destructive'
      })
    }
    setIsLoading(false)
  }

  return (
    <div className={cn('grid gap-6', className)} {...props}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-2">
          <div className="grid gap-1">
            <Label htmlFor="email">Enter registered email address</Label>
            <Input
              id="email"
              placeholder="<EMAIL>"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading}
              {...register('email')}
            />
            {errors?.email && (
              <p className="px-1 text-xs text-red-600">
                {errors.email.message}
              </p>
            )}
          </div>

          <button className={cn(buttonVariants())} disabled={isLoading}>
            {isLoading && (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            )}
            Send Reset Email
          </button>
        </div>
      </form>
    </div>
  )
}
