'use server'

import { db, handlePrismaError } from './db'
import bcrypt from 'bcryptjs'

export const findUserById = async (id: string) => {
  try {
    const user = await db.user.findUnique({
      where: {
        id: id
      },
      include: {
        userSettings: true
      }
    })
    return user
  } catch (error) {
    handlePrismaError(error)
  }
}

export const findUserByEmail = async (email: string) => {
  try {
    const user = await db.user.findUnique({
      where: {
        email: email
      }
    })
    return user
  } catch (error) {
    handlePrismaError(error)
  }
}

export const updateUser = async ({
  id,
  name
}: {
  id: string
  name: string
}) => {
  try {
    const user = await db.user.update({
      where: {
        id: id
      },
      data: {
        name: name
      }
    })
    return user
  } catch (error) {
    handlePrismaError(error)
  }
}

export const registerNewUser = async ({
  email,
  password,
  name,
  teamId
}: {
  email: string
  password: string
  name: string
  teamId?: string
}) => {
  try {
    const salt = bcrypt.genSaltSync(10)
    const passwordHash = bcrypt.hashSync(password, salt)

    const user = await db.user.create({
      data: {
        name: name,
        email: email,
        passwordHash: passwordHash,
        teamId: teamId
      }
    })
    return user
  } catch (error) {
    handlePrismaError(error)
  }
}

export const validateUser = async ({
  email,
  password
}: {
  email: string
  password: string
}) => {
  try {
    const user = await db.user.findUnique({
      where: { email: email }
    })

    if (!user) {
      return 'User not found'
    }

    if (
      user &&
      user.passwordHash &&
      bcrypt.compareSync(password, user.passwordHash)
    ) {
      return {
        id: user.id,
        name: user.name,
        email: user.email,
        image: user.image,
        teamId: user.teamId,
        userType: user.userType
      }
    }
    return 'Invalid password'
  } catch (error: any) {
    handlePrismaError(error)
    return null
  }
}

export async function createDefaultUserSettings({
  userId
}: {
  userId: string
}) {
  try {
    const response = await db.userSettings.create({
      data: {
        userId,
        questionAssessment: true
      }
    })

    return response
  } catch (error: any) {
    console.error(error)
  }
}
