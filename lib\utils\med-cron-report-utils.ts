// Utility functions for medical chronology report generation
// Extracted from medcron pro for reuse in basic version

function extractProvider(description: string): string | null {
  // Simple pattern matching for provider names
  const patterns = [
    /Dr\.\s+([A-Z][a-z]+\s+[A-Z][a-z]+)/,
    /([A-Z][a-z]+\s+Hospital)/,
    /([A-Z][a-z]+\s+Medical\s+Center)/,
    /([A-Z][a-z]+\s+Clinic)/
  ]

  for (const pattern of patterns) {
    const match = description.match(pattern)
    if (match) return match[1]
  }

  return null
}

function mapEventTypeToDepartment(eventType: string): string {
  const mapping: Record<string, string> = {
    emergency_visit: 'Emergency Medicine',
    medical_treatment: 'Medical Treatment',
    diagnostic_test: 'Diagnostic Imaging',
    surgery: 'Surgical Services',
    therapy: 'Rehabilitation Services',
    medication: 'Pharmacy/Pain Management',
    expert_opinion: 'Medical Expert Opinion',
    legal_consultation: 'Legal Services',
    disability_assessment: 'Disability Evaluation',
    work_restriction: 'Occupational Medicine',
    pain_documentation: 'Pain Management'
  }
  return mapping[eventType] || 'Medical Treatment'
}

function getDepartmentEmoji(department: string): string {
  const emojis: Record<string, string> = {
    'Emergency Medicine': '🟩',
    'Medical Treatment': '🟨',
    'Diagnostic Imaging': '🟦',
    'Surgical Services': '🔴',
    'Rehabilitation Services': '🟪',
    'Pharmacy/Pain Management': '🟫',
    'Medical Expert Opinion': '⭐',
    'Legal Services': '⚖️',
    'Disability Evaluation': '📋',
    'Occupational Medicine': '🏢',
    'Pain Management': '🔥'
  }
  return emojis[department] || '⚪'
}

function formatDateRange(start: Date, end: Date): string {
  const startStr = new Date(start).toLocaleDateString()
  const endStr = new Date(end).toLocaleDateString()
  return startStr === endStr ? startStr : `${startStr} - ${endStr}`
}

// Generate treatment calendar table from events
export function generateTreatmentCalendar(
  events: {
    id: number
    binderId: string
    documentId: number | string
    pageRange: string
    event: string
    eventType: string
    eventDescription: string
    timestamp: Date
    estimatedTime: boolean
    rawExtractedData: string
    processed: boolean
    createdAt: Date
    updatedAt: Date
  }[]
): string {
  type TreatmentGroup = {
    provider: string
    department: string
    visits: (typeof events)[0][]
    firstVisit: Date
    lastVisit: Date
    documentPageRefs: Set<string>
  }

  const extractPageFromReference = (pageRef: string): string => {
    const match = pageRef.match(/^(\d+)$|^(\d+)-(\d+)$/)
    return match ? pageRef : '1'
  }

  const extractDocumentId = (pageRef: string): string => {
    const parts = pageRef.split(':')
    return parts.length > 1 ? parts[0] : pageRef
  }

  const treatmentGroups = events
    .filter((e) => !['police_report', 'injury_report'].includes(e.eventType))
    .reduce<Record<string, TreatmentGroup>>((groups, event) => {
      const provider =
        extractProvider(event.eventDescription) || 'Unknown Provider'
      const department = mapEventTypeToDepartment(event.eventType)
      const key = `${provider}-${department}`

      const documentId = extractDocumentId(event.pageRange)
      const page = extractPageFromReference(
        event.pageRange.split(':').pop() || ''
      )
      const documentRef = `${documentId}:${page}`

      if (!groups[key]) {
        groups[key] = {
          provider,
          department,
          visits: [],
          firstVisit: event.timestamp,
          lastVisit: event.timestamp,
          documentPageRefs: new Set()
        }
      }

      groups[key].visits.push(event)
      groups[key].documentPageRefs.add(documentRef)
      if (new Date(event.timestamp) < new Date(groups[key].firstVisit)) {
        groups[key].firstVisit = event.timestamp
      }
      if (new Date(event.timestamp) > new Date(groups[key].lastVisit)) {
        groups[key].lastVisit = event.timestamp
      }

      return groups
    }, {})

  let markdown = '## Treatment Calendar\n\n'
  markdown +=
    '| Medical Provider | Department | Treatment Period | Number of Visits | Reference Document |\n'
  markdown +=
    '|------------------|------------|------------------|------------------|--------------------|\n'

  Object.values(treatmentGroups).forEach((group: TreatmentGroup) => {
    const emoji = getDepartmentEmoji(group.department)
    const period = formatDateRange(group.firstVisit, group.lastVisit)
    const docRefs = Array.from(group.documentPageRefs)
      .map((ref) => `[${ref}]`)
      .join(', ')

    markdown += `| ${group.provider} | ${emoji} ${group.department} | ${period} | ${group.visits.length} | ${docRefs} |\n`
  })

  return markdown
}

// Generate timeline markdown from events
export function generateTimelineMarkdown(
  events: {
    timestamp: string
    event: string
    eventType: string
    eventDescription: string
    documentId: number | string
    pageRange: string
  }[]
): string {
  let markdown = '## Medical Timeline\n\n'

  // Add table headers
  markdown += '| Date | Event | Type | Description | Source |\n'
  markdown += '|------|-------|------|-------------|--------|\n'

  events.forEach((event) => {
    const date = new Date(event.timestamp).toLocaleDateString()
    const eventEmoji = getEventEmoji(event.eventType)

    // Escape pipe characters in content and limit description to 100 chars
    const escapedEvent = event.event.replace(/\|/g, '\\|')
    const escapedDescription =
      event.eventDescription.replace(/\|/g, '\\|').substring(0, 100) +
      (event.eventDescription.length > 100 ? '...' : '')

    markdown += `| ${date} | ${eventEmoji} ${escapedEvent} | ${event.eventType.replace(/_/g, ' ')} | ${escapedDescription} | [${event.documentId}:${event.pageRange}] |\n`
  })

  return markdown
}

function getEventEmoji(eventType: string): string {
  const emojis: Record<string, string> = {
    medical_treatment: '🏥',
    emergency_visit: '🚨',
    diagnostic_test: '🔬',
    injury_report: '📋',
    police_report: '👮',
    medication: '💊',
    surgery: '⚕️',
    therapy: '🤲'
  }
  return emojis[eventType] || '📄'
}
