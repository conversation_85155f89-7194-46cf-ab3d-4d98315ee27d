'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { toast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { createBinder } from '@/lib/actions/binder'
import { useState } from 'react'
import { useRouter } from 'next/navigation'

// Define validation schema
const NewBinderFormSchema = z.object({
  plaintiffName: z.string().min(1, {
    message: 'Plaintiff’s name is required.'
  }),
  attorneyName: z.string().min(1, {
    message: 'Attorney’s name is required.'
  }),
  caseNumber: z.string().optional()
})

export function NewBinderForm({ onSuccess }: { onSuccess: () => void }) {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const form = useForm<z.infer<typeof NewBinderFormSchema>>({
    resolver: zodResolver(NewBinderFormSchema),
    defaultValues: {
      plaintiffName: '',
      attorneyName: '',
      caseNumber: ''
    }
  })

  async function onSubmit(data: z.infer<typeof NewBinderFormSchema>) {
    setIsLoading(true)
    try {
      await createBinder({
        name: data.plaintiffName,
        data: {
          attorney: data.attorneyName,
          caseNumber: data.caseNumber
        }
      })
      toast({
        title: 'Case created successfully',
        description: `The case for "${data.plaintiffName}" has been created.`
      })
      onSuccess()
      router.refresh()
    } catch (error) {
      console.error('Error creating case:', error)
      toast({
        title: 'Failed to create case',
        description: 'An error occurred while creating the case.',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Plaintiff’s Name Field */}
        <FormField
          control={form.control}
          name="plaintiffName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Plaintiff’s Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter plaintiff’s name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Attorney’s Name Combobox */}
        <FormField
          control={form.control}
          name="attorneyName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Attorney’s Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter Attorney’s name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Case Number Field (Optional) */}
        <FormField
          control={form.control}
          name="caseNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Case Number (Optional)</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter case number (if available)"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isLoading || !form.formState.isValid}>
          {isLoading ? 'Creating...' : 'Create Case File'}
        </Button>
      </form>
    </Form>
  )
}
