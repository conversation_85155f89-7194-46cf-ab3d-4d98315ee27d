'use client'

import * as React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'

export function CardWithInformation({
  title,
  mainValue
}: {
  title: string
  mainValue: string
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-4xl font-bold text-right">{mainValue}</div>
      </CardContent>
    </Card>
  )
}

export function LinkWithInformation({
  title,
  mainValue,
  path
}: {
  title: string
  mainValue: string
  path: string
}) {
  return (
    <Link href={path}>
      <Card className="hover:bg-ring duration-200">
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-4xl font-bold text-right">{mainValue}</div>
        </CardContent>
      </Card>
    </Link>
  )
}

export function LinkWithInformationMini({
  title,
  mainValue,
  path
}: {
  title: string
  mainValue: string
  path: string
}) {
  return (
    <Link href={path}>
      <Card className="hover:bg-ring duration-200">
        <CardHeader>
          <CardTitle className="text-lg">{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-right">{mainValue}</div>
        </CardContent>
      </Card>
    </Link>
  )
}
