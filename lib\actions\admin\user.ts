'use server'

import { db } from '@/lib/db'

export async function deleteUserWithAllRecords(userId: string): Promise<{
  success: boolean
  message: string
  counts?: string
  summary?: any
}> {
  try {
    // First, collect counts of all records that will be deleted
    const counts = {
      accounts: 0,
      sessions: 0,
      resetPasswordTokens: 0,
      userSettings: 0,
      newsletters: 0,
      researchStores: 0,
      responseFeedbacks: 0,
      binders: 0,
      caseFiles: 0,
      documentEvents: 0,
      processedChronologies: 0,
      datasets: 0,
      caseFilesCreated: 0
    }

    // Get the user first to check if they exist
    const user = await db.user.findUnique({
      where: { id: userId },
      include: { team: true }
    })

    if (!user) {
      return {
        success: false,
        message: 'User not found'
      }
    }

    // Count user's accounts
    counts.accounts = await db.account.count({
      where: { userId }
    })

    // Count user's sessions
    counts.sessions = await db.session.count({
      where: { userId }
    })

    // Count user's password reset tokens
    counts.resetPasswordTokens = await db.resetPasswordToken.count({
      where: { userId }
    })

    // Count user's settings
    counts.userSettings = await db.userSettings.count({
      where: { userId }
    })

    // Count user's newsletters
    counts.newsletters = await db.newsletter.count({
      where: { userId }
    })

    // Count user's research stores
    const userResearches = await db.researchStore.findMany({
      where: { userId }
    })
    counts.researchStores = userResearches.length

    // Count response feedback for user's research
    for (const research of userResearches) {
      const feedbackCount = await db.responseFeedback.count({
        where: { researchId: research.id }
      })
      counts.responseFeedbacks += feedbackCount
    }

    // Count binders created by the user
    const userBinders = await db.binder.findMany({
      where: { createdBy: userId }
    })
    counts.binders = userBinders.length

    for (const binder of userBinders) {
      counts.caseFiles += await db.caseFile.count({
        where: { binderId: binder.id }
      })
      counts.documentEvents += await db.documentEvent.count({
        where: { binderId: binder.id }
      })
      counts.processedChronologies += await db.processedChronology.count({
        where: { binderId: binder.id }
      })
      counts.datasets += await db.dataset.count({
        where: { binderId: binder.id }
      })
    }

    // Count case files created by the user
    counts.caseFilesCreated = await db.caseFile.count({
      where: { creatorId: userId }
    })

    // Start a transaction to ensure all deletions happen together
    await db.$transaction(async (tx) => {
      // Delete user's accounts (OAuth/authentication records)
      await tx.account.deleteMany({
        where: {
          userId
        }
      })

      // Delete user's sessions
      await tx.session.deleteMany({
        where: {
          userId
        }
      })

      // Delete user's password reset tokens
      await tx.resetPasswordToken.deleteMany({
        where: {
          userId
        }
      })

      // Delete user's settings
      await tx.userSettings.deleteMany({
        where: {
          userId
        }
      })

      // Delete user's newsletters
      await tx.newsletter.deleteMany({
        where: {
          userId
        }
      })

      // Delete user's research stores
      const userResearches = await tx.researchStore.findMany({
        where: { userId }
      })

      // Delete response feedback for user's research
      for (const research of userResearches) {
        await tx.responseFeedback.deleteMany({
          where: {
            researchId: research.id
          }
        })
      }

      // Delete user's research stores
      await tx.researchStore.deleteMany({
        where: {
          userId
        }
      })

      // Delete binders created by the user
      const userBinders = await tx.binder.findMany({
        where: { createdBy: userId }
      })

      for (const binder of userBinders) {
        // Delete case files associated with user's binders
        await tx.caseFile.deleteMany({
          where: {
            binderId: binder.id
          }
        })

        // Delete document events associated with user's binders
        await tx.documentEvent.deleteMany({
          where: {
            binderId: binder.id
          }
        })

        // Delete processed chronologies associated with user's binders
        await tx.processedChronology.deleteMany({
          where: {
            binderId: binder.id
          }
        })

        // Delete datasets associated with user's binders
        await tx.dataset.deleteMany({
          where: {
            binderId: binder.id
          }
        })
      }

      // Delete binders created by the user
      await tx.binder.deleteMany({
        where: {
          createdBy: userId
        }
      })

      // Delete case files created by the user
      await tx.caseFile.deleteMany({
        where: {
          creatorId: userId
        }
      })

      // Finally, delete the user itself
      await tx.user.delete({
        where: {
          id: userId
        }
      })
    })

    const totalRecords = Object.values(counts).reduce(
      (sum, count) => sum + count,
      0
    )

    return {
      success: true,
      message: `User deleted successfully! Removed ${totalRecords} records total.`,
      counts: JSON.stringify(counts),
      summary: {
        total: totalRecords,
        binders: counts.binders,
        researchStores: counts.researchStores,
        newsletters: counts.newsletters,
        caseFiles: counts.caseFiles + counts.caseFilesCreated,
        accounts: counts.accounts,
        sessions: counts.sessions
      }
    }
  } catch (error: any) {
    console.error('Error deleting user:', error)
    return {
      success: false,
      message: error.message || 'An error occurred while deleting the user'
    }
  }
}
