'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useRef } from 'react'

export function RedactionFileSelector({
  onFileSelected
}: {
  onFileSelected: (file: File | null) => void
}) {
  const fileInputRef = useRef<HTMLInputElement | null>(null)

  function handleFileChange(event: React.ChangeEvent<HTMLInputElement>) {
    const file = event.target.files?.[0] || null
    onFileSelected(file)
  }

  function triggerFileInput() {
    fileInputRef.current?.click()
  }

  return (
    <>
      <Button
        onClick={triggerFileInput}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Select New File
      </Button>
      <input
        type="file"
        accept="application/pdf"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
      />
    </>
  )
}
