import { db, handlePrismaError } from './db'

export async function verifyCurrentUserHasAccessToNewsletter(
  newsletterId: string,
  userId: string
) {
  try {
    const count = await db.newsletter.count({
      where: {
        id: newsletterId,
        userId: userId
      }
    })
    return count > 0
  } catch (error) {
    return handlePrismaError(error)
  }
}

export async function findNewslettersWithUserId({
  userId
}: {
  userId: string
}) {
  try {
    const newsletter = await db.newsletter.findMany({
      select: {
        id: true,
        title: true,
        published: true,
        createdAt: true
      },
      where: {
        userId
      }
    })
    return newsletter
  } catch (error) {
    return handlePrismaError(error)
  }
}

export async function createNewNewsletter({
  title,
  content,
  userId
}: {
  title: string
  content: string
  userId: string
}) {
  try {
    const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-')
    const newsletter = await db.newsletter.create({
      data: {
        title,
        slug,
        content,
        userId
      },
      select: {
        id: true
      }
    })
    return newsletter
  } catch (error) {
    return handlePrismaError(error)
  }
}

export async function updateNewsletterById({
  id,
  title,
  content
}: {
  id: string
  title: string
  content: string
}) {
  try {
    const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-')
    const newsletter = await db.newsletter.update({
      where: {
        id
      },
      data: {
        title,
        slug,
        content
      }
    })
    return newsletter
  } catch (error) {
    return handlePrismaError(error)
  }
}

export async function deleteNewsletterById(id: string) {
  try {
    await db.newsletter.delete({
      where: {
        id
      }
    })
  } catch (error) {
    return handlePrismaError(error)
  }
}
