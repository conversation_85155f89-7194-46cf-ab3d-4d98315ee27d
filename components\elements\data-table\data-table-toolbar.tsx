'use client'

import { X } from 'lucide-react'
import { Table } from '@tanstack/react-table'
import { Button } from '../../ui/button'
import { Input } from '../../ui/input'
import { DataTableViewOptions } from './data-table-view-options'
import { DataTableFacetedFilter } from './data-table-faceted-filter'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  statuses?: {
    value: string
    label: string
    icon?: React.ComponentType
    color?: string
  }[]
  bulkAction?: (selectedRows: number[]) => React.ReactNode
}

export function DataTableToolbar<TData>({
  table,
  statuses,
  bulkAction
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const selectedRows = table
    .getSelectedRowModel()
    .rows.map((row) => (row.original as { id: number }).id)

  return (
    <div className="flex items-center justify-between border p-3 rounded-lg">
      <div className="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Search ..."
          value={(table.getColumn('title')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('title')?.setFilterValue(event.target.value)
          }
          className="border-none w-[150px] lg:w-[250px]"
        />
        {statuses && table.getColumn('status') && (
          <DataTableFacetedFilter
            column={table.getColumn('status')}
            title="Status"
            options={statuses}
          />
        )}
        {/* {table.getColumn("priority") && (
          <DataTableFacetedFilter
            column={table.getColumn("priority")}
            title="Priority"
            options={priorities}
          />
        )} */}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex flex-1 items-center space-x-2 justify-end">
        {bulkAction && selectedRows.length > 0 && bulkAction(selectedRows)}
        <DataTableViewOptions table={table} />
      </div>
    </div>
  )
}
