'use client'

import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'

export function TextHighlighter() {
  const [marks, setMarks] = useState<HTMLParagraphElement[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)

  function generateTextHighlight() {
    // start timer
    const starttime = new Date().getTime()
    const fetchTextToHighlight = localStorage.getItem('emphasis-lines')

    if (fetchTextToHighlight && fetchTextToHighlight.length > 3) {
      const textToHighlight: string = JSON.parse(fetchTextToHighlight).join(' ')
      // console.log(textToHighlight)

      const words = textToHighlight
        .split(' ')
        .filter((word) => word.trim().length > 0)
      const wordGroups: string[] = []
      let matchCountCheker = 3
      for (let i = 0; i < words.length - 2; i++) {
        wordGroups.push(words.slice(i, i + 3).join(' '))
      }

      const paragraphs = document.querySelectorAll('p')
      const markPositions: HTMLParagraphElement[] = []
      paragraphs.forEach((paragraph) => {
        let innerHTML = paragraph.innerHTML
        let matchCount = 0

        wordGroups.forEach((group) => {
          const escapedGroup = group.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // Escape special characters for regex
          const regex = new RegExp(`(${escapedGroup})`, 'gi')
          if (regex.test(innerHTML)) {
            matchCount++
          }
        })

        switch (true) {
          case words.length > 100:
            matchCountCheker = 30
            break

          case words.length > 50:
            matchCountCheker = 15
            break

          case words.length > 20:
            matchCountCheker = 5
            break

          default:
            break
        }

        // If the paragraph contains at least three word groups, highlight the entire paragraph
        if (matchCount >= matchCountCheker) {
          paragraph.innerHTML = `<mark>${innerHTML}</mark>`
          markPositions.push(paragraph)
        }
      })
      const endtime = new Date().getTime()
      console.log(
        `Words: ${words.length}, Word Groups: ${wordGroups.length}, Match Count: ${matchCountCheker}, Time taken: ${endtime - starttime}ms`
      )
      setMarks(markPositions)
    }
  }

  useEffect(() => {
    generateTextHighlight()
    document.body.style.width = '100%'
    document.body.style.marginLeft = '0'
  }, [])

  function scrollToMark(index: number) {
    if (marks[index]) {
      marks[index].scrollIntoView({ behavior: 'smooth', block: 'center' })
      setCurrentIndex(index)
    }
  }

  return (
    <div className="flex gap-3 items-end">
      {marks.length > 1 && (
        <>
          <Button
            onClick={() =>
              scrollToMark(
                currentIndex > 0 ? currentIndex - 1 : marks.length - 1
              )
            }
            className="w-max"
            disabled={marks.length === 0 || currentIndex === 0}
            size="sm"
          >
            Previous
          </Button>
          <Button
            onClick={() => scrollToMark((currentIndex + 1) % marks.length)}
            size="sm"
            className="w-max"
          >
            Next{' '}
            <span className="ml-2 text-xs">
              {currentIndex + 1} / {marks.length}
            </span>
          </Button>
        </>
      )}
    </div>
  )
}
