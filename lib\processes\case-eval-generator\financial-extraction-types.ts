export interface ExtractedMedicalExpense {
  type: 'medical'
  category: string // e.g., 'emergency', 'surgery', 'therapy', 'diagnostic', 'medication', 'future'
  provider?: string
  date?: string
  amount: number
  description: string
  documentId: number
  pageReference?: string
  isFutureExpense: boolean
}

export interface ExtractedWageLoss {
  type: 'wage'
  startDate?: string
  endDate?: string
  hourlyRate?: number
  hoursLost?: number
  salaryLost?: number
  totalAmount: number
  description: string
  documentId: number
  pageReference?: string
  isFutureWageLoss: boolean
}

export interface ExtractedPropertyDamage {
  type: 'property'
  itemDescription: string
  replacementCost?: number
  repairCost?: number
  totalAmount: number
  documentId: number
  pageReference?: string
}

export interface ExtractedOtherExpense {
  type: 'other'
  category: string // e.g., 'transportation', 'home assistance', 'child care'
  amount: number
  description: string
  documentId: number
  pageReference?: string
  isFutureExpense: boolean
}

// Union type for all extracted financial data
export type ExtractedFinancialItem =
  | ExtractedMedicalExpense
  | ExtractedWageLoss
  | ExtractedPropertyDamage
  | ExtractedOtherExpense

// Complete extracted financial data
export interface ExtractedFinancialData {
  medicalExpenses: ExtractedMedicalExpense[]
  wageLosses: ExtractedWageLoss[]
  propertyDamages: ExtractedPropertyDamage[]
  householdServiceLosses: ExtractedHouseholdServiceLoss[]
  otherExpenses: ExtractedOtherExpense[]
}

// Non-economic damages metadata (not for calculation, just for references)
export interface NonEconomicDamagesMetadata {
  painAndSuffering: {
    severity: 'Mild' | 'Moderate' | 'Severe' | 'Catastrophic'
    duration: 'Temporary' | 'Long-term' | 'Permanent'
    impactDetails: string
    documentEvidence: Array<{
      documentId: number
      pageReference?: string
      excerpt: string
    }>
  }
  emotionalDistress?: {
    description: string
    professionallyDiagnosed: boolean
    treatmentRequired: boolean
    documentEvidence: Array<{
      documentId: number
      pageReference?: string
      excerpt: string
    }>
  }
  lossOfEnjoyment?: {
    affectedActivities: string[]
    documentEvidence: Array<{
      documentId: number
      pageReference?: string
      excerpt: string
    }>
  }
  disfigurement?: {
    description: string
    visibility: 'Visible' | 'Not visible in normal attire'
    permanence: 'Temporary' | 'Permanent'
    documentEvidence: Array<{
      documentId: number
      pageReference?: string
      excerpt: string
    }>
  }
}

export interface ExtractedHouseholdServiceLoss {
  type: 'household'
  startDate: string
  endDate: string
  hourlyRate: number
  hoursPerDay: number
  percentImpaired: number
  netLoss: number
  description: string
  documentId: number
  pageReference?: string
}

// Punitive damages metadata (not for calculation, just for references)
export interface PunitiveDamagesMetadata {
  eligibilityFactors: string[]
  defendantConductEvidence: Array<{
    description: string
    documentId: number
    pageReference?: string
  }>
  defendantPriorHistory?: Array<{
    description: string
    documentId: number
    pageReference?: string
  }>
}

// Complete extraction result (financial + supporting metadata)
export interface ExtractedCaseFinancialData {
  financialData: ExtractedFinancialData
  nonEconomicMetadata: NonEconomicDamagesMetadata
  punitiveDamagesMetadata?: PunitiveDamagesMetadata
  liabilityDistribution?: {
    plaintiffPercentage: number
    defendantPercentage: number
    otherPercentage?: number
    otherPartyDescription?: string
  }
  sourceDocuments: Array<{
    id: number
    title: string
    documentType: string
  }>
}
