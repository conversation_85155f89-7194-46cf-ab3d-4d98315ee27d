'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { deleteUserWithAllRecords } from '@/lib/actions/admin/user'
import { useRouter } from 'next/navigation'
import { Trash2 } from 'lucide-react'

interface DeleteUserButtonProps {
  userId: string
  userName: string
  userEmail: string
}

export function DeleteUserButton({
  userId,
  userName,
  userEmail
}: DeleteUserButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleDelete = async () => {
    setIsLoading(true)

    try {
      const result = await deleteUserWithAllRecords(userId)

      if (result.success) {
        const summary = result.summary

        toast({
          title: 'User Deleted Successfully',
          description: (
            <div className="space-y-2">
              <p>{result.message}</p>
              <div className="text-sm">
                <p className="font-medium mb-1">Deleted Records:</p>
                <ul className="list-disc list-inside space-y-0.5 text-xs">
                  <li>Total Records: {summary?.total || 0}</li>
                  <li>Binders: {summary?.binders || 0}</li>
                  <li>Research Stores: {summary?.researchStores || 0}</li>
                  <li>Newsletters: {summary?.newsletters || 0}</li>
                  <li>Case Files: {summary?.caseFiles || 0}</li>
                  <li>Accounts: {summary?.accounts || 0}</li>
                  <li>Sessions: {summary?.sessions || 0}</li>
                </ul>
              </div>
            </div>
          ),
          duration: 8000 // Show for 8 seconds for readability
        })
        setIsOpen(false)
        router.refresh()
      } else {
        toast({
          title: 'Error',
          description: result.message,
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred while deleting the user',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="destructive" size="sm">
          <Trash2 className="h-4 w-4 mr-2" />
          Delete User
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete User</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the user &quot;{userName}&quot; (
            {userEmail})? This action will permanently delete:
            <ul className="mt-2 list-disc list-inside space-y-1">
              <li>The user account and profile</li>
              <li>All authentication records and sessions</li>
              <li>All user settings and preferences</li>
              <li>All newsletters created by this user</li>
              <li>All research stores and response feedback</li>
              <li>All binders and case files created by this user</li>
              <li>All associated datasets and document events</li>
            </ul>
            <strong className="text-red-600 block mt-2">
              This action cannot be undone.
            </strong>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading}
          >
            {isLoading ? 'Deleting...' : 'Delete User'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
