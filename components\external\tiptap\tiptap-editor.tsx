'use client'

import { useEffect } from 'react'
import Highlight from '@tiptap/extension-highlight'
import TextAlign from '@tiptap/extension-text-align'
import HorizontalRule from '@tiptap/extension-horizontal-rule'
import { EditorContent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Typography from '@tiptap/extension-typography'
import { Color } from '@tiptap/extension-color'
import TextStyle from '@tiptap/extension-text-style'
import { cva, VariantProps } from 'class-variance-authority'

import {
  StyledOrderedList,
  StyledBulletList,
  StyledSubscript,
  StyledSuperscript,
  StyledUnderline,
  StyledParagraph,
  StyledHeading,
  StyledLink
} from './tiptap-paragraph'
import { TableExtensions } from './tiptap-table'
import { TipTapMenuBar } from './tiptap-menubar'
import { CitationModalExtension } from './custom-extensions/citation-modal-extension'
import { preprocessTiptapContent } from './tiptap-utils'

import { cn } from '@/lib/utils'
import { EditorContextProvider } from '@/lib/context/tiptapContext'
import { DocumentTitle } from '@/types/case'

const tipTapVariants = cva('', {
  variants: {
    size: {
      default: 'min-h-48',
      sm: 'min-h-12',
      md: 'min-h-48',
      lg: 'min-h-64'
    }
  }
})
export interface TipTapEditorProps extends VariantProps<typeof tipTapVariants> {
  isEditable?: boolean
  content: string
  setContent: (content: string) => void
  refDocuments: DocumentTitle[]
  submitAction?: () => void
}

export const TipTapEditor = ({
  isEditable = true,
  content,
  setContent,
  refDocuments,
  submitAction,
  size = 'md'
}: TipTapEditorProps) => {
  const editor = useEditor(
    {
      extensions: [
        StarterKit,
        Typography,
        StyledOrderedList,
        StyledBulletList,
        StyledSubscript,
        StyledSuperscript,
        StyledUnderline,
        StyledParagraph,
        StyledHeading,
        StyledLink,
        CitationModalExtension,
        TextAlign.configure({
          types: ['heading', 'paragraph']
        }),
        TextStyle,
        Color,
        Highlight,
        HorizontalRule,
        ...TableExtensions
      ],
      content: preprocessTiptapContent(content),
      editable: isEditable,
      onUpdate({ editor }) {
        setContent(editor.getHTML())
      }
    },
    [isEditable]
  )

  useEffect(() => {
    if (editor?.getHTML() !== content) {
      editor?.commands.setContent(preprocessTiptapContent(content))
    }
  }, [content])

  useEffect(() => {
    if (!editor) return

    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key == 'Enter') {
        event.preventDefault()
        submitAction?.()
      }
    }

    // Attach the event listener
    editor.view.dom.addEventListener('keydown', handleKeyDown)

    // Cleanup the listener on unmount
    return () => {
      editor.view.dom.removeEventListener('keydown', handleKeyDown)
    }
  }, [editor, submitAction])

  return (
    <EditorContextProvider
      content={content}
      setContent={setContent}
      refDocuments={refDocuments}
    >
      {isEditable && <TipTapMenuBar editor={editor} />}
      <div
        className="border border-gray-300 bg-white p-10 rounded-md my-4 cursor-text text-black dark:invert overflow-x-auto"
        onClick={() => {
          if (!editor) return
          if (content.trim() === '' || content.trim() === '<p></p>') {
            editor.commands.setContent(' ')
          }
          editor.chain().focus().run()
        }}
      >
        <EditorContent
          editor={editor}
          className={cn(tipTapVariants({ size }))}
        />
      </div>
    </EditorContextProvider>
  )
}
