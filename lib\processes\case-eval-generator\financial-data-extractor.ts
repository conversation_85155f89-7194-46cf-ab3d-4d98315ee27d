// lib/processes/case-eval-generator/financial-data-extractor.ts

import pLimit from 'p-limit'
import {
  ExtractedCaseFinancialData,
  ExtractedFinancialData,
  ExtractedHouseholdServiceLoss,
  ExtractedMedicalExpense,
  NonEconomicDamagesMetadata,
  PunitiveDamagesMetadata
} from './financial-extraction-types'
import { DocumentContent, IncidentDetails, PlaintiffInfo } from '@/types/case'
// import { case_evaluation_prompts } from './case-evaluation-prompts';
import { createCompletion } from '@/lib/services/openai-service'
import { db } from '@/lib/db'

// Logger utility (reused from other modules)
const logger = {
  start: (functionName: string, ...args: any[]) => {
    console.time(`⏱️ ${functionName}`)
    console.log(`🚀 Starting ${functionName}`, ...(args.length ? args : []))
  },
  end: (functionName: string, ...args: any[]) => {
    console.timeEnd(`⏱️ ${functionName}`)
    console.log(`✅ Completed ${functionName}`, ...(args.length ? args : []))
  },
  error: (functionName: string, error: any) => {
    console.error(`❌ Error in ${functionName}:`, error)
  },
  info: (message: string, ...args: any[]) => {
    console.log(`ℹ️ - ${message}`, ...(args.length ? args : []))
  }
}

// Maximum number of concurrent API requests
const MAX_CONCURRENT_REQUESTS = 4

/**
 * Extract financial data from documents
 */
export async function structureMetadata(
  extractedData: Record<string, any>
): Promise<{ plaintiffInfo: PlaintiffInfo; incidentDetails: IncidentDetails }> {
  logger.start('structureMetadata')

  try {
    // Prepare a consolidated view of the extracted data for OpenAI
    const consolidatedData = JSON.stringify(extractedData)

    const structureMetadataPrompt = await db.prompt.findFirst({
      where: {
        source: 'AI_MEDICAL_CHRONOLOGY_STRUCTURE_METADATA'
      }
    })

    const prompt =
      structureMetadataPrompt!.prompt.replace(
        '{{consolidatedData}}',
        consolidatedData
      )! + structureMetadataPrompt!.expectedOutput!

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    })

    // Parse the structured metadata from the response
    const metadata = JSON.parse(response)

    // Ensure the returned data has the expected structure
    const plaintiffInfo: PlaintiffInfo = {
      fullName: metadata.plaintiffInfo.fullName || 'Unknown',
      dateOfBirth: metadata.plaintiffInfo.dateOfBirth || 'Unknown',
      preExistingConditions: metadata.plaintiffInfo.preExistingConditions || []
    }

    const incidentDetails: IncidentDetails = {
      date: metadata.incidentDetails.date || 'Unknown',
      description: metadata.incidentDetails.description || 'Unknown',
      primaryInjuries: metadata.incidentDetails.primaryInjuries || [],
      treatmentHistory: metadata.incidentDetails.treatmentHistory || ''
    }

    logger.end('structureMetadata', {
      plaintiff: plaintiffInfo.fullName,
      incidentDate: incidentDetails.date
    })

    return { plaintiffInfo, incidentDetails }
  } catch (error: any) {
    logger.error('structureMetadata', error)
    throw new Error(`Failed to structure metadata: ${error.message}`)
  }
}

export async function extractFinancialData(
  documents: Record<string, DocumentContent[]>
): Promise<ExtractedCaseFinancialData> {
  logger.start('extractFinancialData', {
    documentCount: Object.values(documents).flat().length
  })

  try {
    // Step 1: Extract raw data from documents
    const extractedRawData = await extractRawDataFromDocuments(documents)

    // Step 2: Extract liability information
    const liabilityDistribution = await extractLiabilityDistribution(
      extractedRawData,
      documents
    )

    // Step 3: Extract financial items
    const financialData = await extractFinancialItems(
      extractedRawData,
      documents
    )

    const futureMedicalExpenses = await extractFutureMedicalExpenses(
      extractedRawData,
      documents
    )
    financialData.medicalExpenses.push(...futureMedicalExpenses)

    console.log('futureMedicalExpenses: ', futureMedicalExpenses)

    const householdServiceLosses = await extractLossOfHouseholdServices(
      extractedRawData,
      documents
    )
    financialData.householdServiceLosses = householdServiceLosses

    console.log('householdesrvices losses: ', householdServiceLosses)

    // Step 4: Extract non-economic damages metadata
    const nonEconomicMetadata = await extractNonEconomicMetadata(
      extractedRawData,
      documents
    )

    // Step 5: Extract punitive damages metadata if applicable
    const punitiveDamagesMetadata = await extractPunitiveDamagesMetadata(
      extractedRawData,
      documents
    )

    // Create source documents list
    const sourceDocuments = Object.entries(documents).flatMap(
      ([docType, docs]) =>
        docs.map((doc) => ({
          id: doc.id,
          title: doc.title,
          documentType: docType
        }))
    )

    // Combine all extracted data
    const result: ExtractedCaseFinancialData = {
      financialData,
      nonEconomicMetadata,
      liabilityDistribution,
      sourceDocuments
    }

    // Add punitive damages metadata if it exists
    if (punitiveDamagesMetadata.eligibilityFactors.length > 0) {
      result.punitiveDamagesMetadata = punitiveDamagesMetadata
    }

    logger.end('extractFinancialData')
    return result
  } catch (error: any) {
    logger.error('extractFinancialData', error)
    throw new Error(`Failed to extract financial data: ${error.message}`)
  }
}

/**
 * Extract raw data from documents - this pulls all relevant information without categorizing
 */
async function extractRawDataFromDocuments(
  documents: Record<string, DocumentContent[]>
): Promise<Record<string, any>> {
  logger.start('extractRawDataFromDocuments')

  try {
    const extractedData: Record<string, any> = {}
    const limit = pLimit(MAX_CONCURRENT_REQUESTS)
    const extractionPromises: Promise<void>[] = []

    // Setup the structure for each document type
    for (const docType of Object.keys(documents)) {
      extractedData[docType] = []
    }

    // Create a flat list of all extraction tasks
    for (const [docType, docs] of Object.entries(documents)) {
      for (const doc of docs) {
        extractionPromises.push(
          limit(async () => {
            logger.info(
              `Extracting data from ${docType} document: "${doc.title}" (ID: ${doc.id})`
            )

            // Call OpenAI to extract structured data from the document
            const prompt = `
              Extract key financial and damages information from the following ${docType} document titled "${doc.title}".
              Focus specifically on:
              1. Medical expenses with amounts, providers, and dates
              2. Wage loss information including periods, salary/rates, and totals
              3. Property damage details with repair/replacement costs
              4. Other financial losses with descriptions and amounts
              5. Pain and suffering indicators, severity, and impact on life
              6. Any information about disfigurement or permanent impairment
              7. Any information related to emotional distress or mental health impact
              8. Any evidence of defendant misconduct that might justify punitive damages
              9. Any information related to comparative fault or liability
              
              Document content:
              ${doc.content}
              
              Return the data in JSON format with all monetary values as numbers (without $ or commas).
              Be specific about whether medical expenses or wage losses are past or future.
              Do NOT calculate totals or perform any math - just extract the raw data from the document.
            `

            try {
              const response = await createCompletion({
                messages: [{ role: 'user', content: prompt }],
                temperature: 0.1
              })

              // Parse the structured data from the response
              try {
                const extractedDocData = JSON.parse(response)
                extractedDocData.sourceDocumentId = doc.id
                extractedDocData.docType = docType
                extractedData[docType].push(extractedDocData)
                logger.info(
                  `Successfully extracted data from ${docType} document: "${doc.title}" (ID: ${doc.id})`
                )
              } catch (parseError) {
                logger.error(
                  `Error parsing OpenAI response for document ${doc.id}`,
                  response
                )
                // Handle gracefully by adding partial data
                extractedData[docType].push({
                  sourceDocumentId: doc.id,
                  docType,
                  error: 'Failed to parse structured data',
                  rawContent: response
                })
              }
            } catch (apiError: any) {
              logger.error(`API error for document ${doc.id}`, apiError)
              // Handle API errors gracefully
              extractedData[docType].push({
                sourceDocumentId: doc.id,
                docType,
                error: `API error: ${apiError.message}`,
                partialContent: doc.content.substring(0, 100) + '...'
              })
            }
          })
        )
      }
    }

    // Wait for all extraction tasks to complete
    await Promise.all(extractionPromises)

    logger.end('extractRawDataFromDocuments', {
      documentTypes: Object.keys(extractedData).length,
      totalDocuments: Object.values(extractedData).flat().length
    })

    return extractedData
  } catch (error: any) {
    logger.error('extractRawDataFromDocuments', error)
    throw new Error(
      `Failed to extract raw data from documents: ${error.message}`
    )
  }
}

/**
 * Extract liability distribution information
 */
async function extractLiabilityDistribution(
  extractedRawData: Record<string, any>,
  documents: Record<string, DocumentContent[]>
): Promise<{
  plaintiffPercentage: number
  defendantPercentage: number
  otherPercentage?: number
}> {
  logger.start('extractLiabilityDistribution')

  try {
    const prompt = `
      Based on the following extracted data, determine the distribution of liability between plaintiff, defendant, and any other parties.
      
      Extracted Data:
      ${JSON.stringify(extractedRawData)}
      
      Please analyze all evidence related to liability including:
      - Police reports
      - Witness statements
      - Expert opinions
      - Any contributory/comparative negligence evidence
      
      Return a JSON object with liability percentages that total 100%:
      {
        "plaintiffPercentage": number,
        "defendantPercentage": number,
        "otherPercentage": number (optional),
        "reasoning": "brief explanation of reasoning"
      }
      
      Ensure that you only extract information without making any judgment calls.
      If there is insufficient information, provide your best estimate based on the available evidence.
    `

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    })

    // Parse the response
    const liability = JSON.parse(response)

    logger.end('extractLiabilityDistribution')

    return {
      plaintiffPercentage: liability.plaintiffPercentage,
      defendantPercentage: liability.defendantPercentage,
      otherPercentage: liability.otherPercentage
    }
  } catch (error: any) {
    logger.error('extractLiabilityDistribution', error)
    // Default to 100% defendant liability if extraction fails
    return {
      plaintiffPercentage: 0,
      defendantPercentage: 100
    }
  }
}

/**
 * Extract financial items from raw data
 */
async function extractFinancialItems(
  extractedRawData: Record<string, any>,
  documents: Record<string, DocumentContent[]>
): Promise<ExtractedFinancialData> {
  logger.start('extractFinancialItems')

  try {
    const prompt = `
      Based on the following extracted data, identify all financial items related to damages.
      Organize them into the following categories:
      1. Medical expenses
      2. Wage losses
      3. Property damages
      4. Other expenses

      Extracted Data:
      ${JSON.stringify(extractedRawData)}

      For each item, provide:
      - Specific amounts (as numbers without $ or commas)
      - Source document ID
      - Page reference if available
      - Detailed description
      - Whether it's a past or future expense
      - Any other relevant metadata

      For medical expenses, be sure to include:
      - Past bills and treatment costs
      - Estimated future care costs from treatment plans, life care plans, or medical expert reports
      - Future physical therapy, surgeries, long-term medication, and assistive devices
      - Use "isFutureExpense": true for these estimates
      - Mention source documents (e.g., care plan PDF, expert opinion)

      Return a JSON object that follows this structure:
      {
        "medicalExpenses": [
          {
            "type": "medical",
            "category": "emergency",
            "provider": "Hospital Name",
            "date": "YYYY-MM-DD",
            "amount": 1000,
            "description": "Emergency room visit",
            "documentId": 123,
            "pageReference": "p.5",
            "isFutureExpense": false
          }
        ],
        "wageLosses": [
          {
            "type": 'wage'
            "startDate": "YYYY-MM-DD"
            "endDate": "YYYY-MM-DD"
            "hourlyRate": 200
            "hoursLost": 70
            "salaryLost": 14000
            "totalAmount": 14000
            "description": "Full leave due to injury"
            "documentId": 123
            "pageReference": "p.7"
            "isFutureWageLoss": true
          }
        ],
        "propertyDamages": [...],
        "otherExpenses": [...]
      }

      Only include items that have specific monetary values mentioned in the documents.
      Do NOT calculate totals or perform any math - just extract and organize the raw data.
    `

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    })

    // Parse the response
    const financialItems: ExtractedFinancialData = JSON.parse(response)

    // Ensure all required arrays exist (even if empty)
    if (!financialItems.medicalExpenses) financialItems.medicalExpenses = []
    if (!financialItems.wageLosses) financialItems.wageLosses = []
    if (!financialItems.propertyDamages) financialItems.propertyDamages = []
    if (!financialItems.otherExpenses) financialItems.otherExpenses = []

    logger.end('extractFinancialItems', {
      medicalExpensesCount: financialItems.medicalExpenses.length,
      wageLossesCount: financialItems.wageLosses.length,
      propertyDamagesCount: financialItems.propertyDamages.length,
      otherExpensesCount: financialItems.otherExpenses.length
    })

    return financialItems
  } catch (error: any) {
    logger.error('extractFinancialItems', error)
    // Return empty structure if extraction fails
    return {
      medicalExpenses: [],
      wageLosses: [],
      propertyDamages: [],
      householdServiceLosses: [],
      otherExpenses: []
    }
  }
}

export async function extractFutureMedicalExpenses(
  extractedRawData: Record<string, any>,
  documents: Record<string, DocumentContent[]>
): Promise<ExtractedMedicalExpense[]> {
  logger.start('extractFutureMedicalExpenses')

  const prompt = `
You are analyzing legal and medical documents for projected or recommended **future medical expenses**.

Extracted Data:
${JSON.stringify(extractedRawData)}

Please identify and return all **anticipated future treatments or services** mentioned in the documents, such as:
- Follow-up appointments with specialists (orthopedic, neurology, pain management)
- Physical therapy or rehabilitation sessions
- Diagnostic imaging (MRIs, X-rays, CT scans)
- Injections or pain management procedures
- Surgical interventions
- Medication costs
- Assistive devices or home modifications
- Chiropractic care or alternative treatments

Focus ONLY on **future** or **recommended** procedures (e.g., "will need", "is expected to undergo", "doctor recommends"). Ignore any treatments that have already occurred.

Your task is to return a JSON object in this format:

{
  "futureMedicalExpenses": [
    {
      "type": "medical",
      "category": "string",                // One of the categories above
      "provider": "string or null",        // Provider name if known
      "date": null,
      "amount": number,                    // Estimated total cost (calculated if frequency × unit cost × years is available)
      "description": "string",             // Describe what's recommended, e.g., "6 sessions of PT recommended over 3 months"
      "documentId": number,                // ID of document that mentions this expense
      "pageReference": "p.X",              // Page number from the document
      "isFutureExpense": true
    }
  ]
}


Calculation tips:
- If cost per treatment and frequency are mentioned (e.g., "$200 per session × 12 sessions"), compute the total and return in the \`amount\` field.
- If the cost is not explicitly stated, you may infer a **reasonable estimate** based on typical values (e.g., $150/session for physical therapy).
- Round amounts to the nearest dollar. Use approximate values if necessary, but never return null or 0 unless explicitly stated.
- Ensure all projected treatments are based on physician recommendations documented in the medical records. The calculations should be mathematically correct.
Respond ONLY with the JSON object shown above.
`

  try {
    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    })

    const parsed = JSON.parse(response)

    const expenses = parsed?.futureMedicalExpenses
    if (!Array.isArray(expenses)) {
      throw new Error('Missing or invalid futureMedicalExpenses array')
    }

    logger.end('extractFutureMedicalExpenses', { count: expenses.length })
    return expenses as ExtractedMedicalExpense[]
  } catch (parseError) {
    logger.error(
      'extractFutureMedicalExpenses: Failed to parse JSON',
      parseError
    )
    return []
  }
}

export async function extractLossOfHouseholdServices(
  extractedRawData: Record<string, any>,
  documents: Record<string, DocumentContent[]>
): Promise<ExtractedHouseholdServiceLoss[]> {
  logger.start('extractLossOfHouseholdServices')

  const prompt = `
You are analyzing legal and medical documents for **loss of household services** damages.

Extracted Data:
${JSON.stringify(extractedRawData)}

Identify all periods where the plaintiff was impaired in providing household services, and for each period, extract:
- Start and end date
- Hourly rate (if not stated, use a reasonable local estimate, e.g., $15-25/hr)
- Hours per day impaired
- Percent impairment (0-100%)
- Net loss (hourly rate × hours per day × days in period × % impaired)
- Description of the basis for the calculation
- Source document and page

Return a JSON object in this format:
{
  "householdServiceLosses": [
    {
      "type": "household",
      "startDate": "YYYY-MM-DD",
      "endDate": "YYYY-MM-DD",
      "hourlyRate": 20,
      "hoursPerDay": 2.5,
      "percentImpaired": 80,
      "netLoss": 4000,
      "description": "Unable to perform most household tasks for 3 months after surgery.",
      "documentId": 123,
      "pageReference": "p.8"
    }
  ]
}

- If multiple periods with different impairment levels exist, include each as a separate entry.
- If no explicit value is stated, estimate based on context and explain in the description.
- Never return null or 0 unless explicitly stated in the documents.
Respond ONLY with the JSON object shown above.
`

  try {
    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    })

    const parsed = JSON.parse(response)
    const losses = parsed?.householdServiceLosses
    if (!Array.isArray(losses)) {
      throw new Error('Missing or invalid householdServiceLosses array')
    }

    logger.end('extractLossOfHouseholdServices', { count: losses.length })
    return losses as ExtractedHouseholdServiceLoss[]
  } catch (parseError) {
    logger.error(
      'extractLossOfHouseholdServices: Failed to parse JSON',
      parseError
    )
    return []
  }
}

/**
 * Extract non-economic damages metadata
 */
async function extractNonEconomicMetadata(
  extractedRawData: Record<string, any>,
  documents: Record<string, DocumentContent[]>
): Promise<NonEconomicDamagesMetadata> {
  logger.start('extractNonEconomicMetadata')

  try {
    const prompt = `
      Based on the following extracted data, identify all information related to non-economic damages.
      Focus on:
      1. Pain and suffering
      2. Emotional distress
      3. Loss of enjoyment of life
      4. Disfigurement

      Extracted Data:
      ${JSON.stringify(extractedRawData)}

      Use only the allowed values for the following fields:
      - painAndSuffering.severity: "Mild", "Moderate", "Severe", "Catastrophic"
      - painAndSuffering.duration: "Temporary", "Long-term", "Permanent"
      - disfigurement.visibility: "Visible", "Not visible in normal attire"
      - disfigurement.permanence: "Temporary", "Permanent"

      Return a JSON object with detailed metadata for each category of non-economic damages.
      Include specific references to evidence in the documents and categorize severity levels.
      {
        "painAndSuffering": {
          "severity": "Mild",
          "duration": "Temporary",
          "impactDetails": "Mild pain reported affecting daily mobility and sleep quality.",
          "documentEvidence": [
            {
              "documentId": 121,
              "pageReference": "p.3",
              "excerpt": "Patient reports ongoing mild pain in lower back for over 6 months."
            }
          ]
        },
        "emotionalDistress": {
          "description": "Symptoms of anxiety following the incident.",
          "professionallyDiagnosed": true,
          "treatmentRequired": true,
          "documentEvidence": [
            {
              "documentId": 122,
              "pageReference": "p.5",
              "excerpt": "Clinical psychologist diagnosis confirms anxiety and recommends therapy."
            }
          ]
        },
        "lossOfEnjoyment": {
          "affectedActivities": ["Running", "Gardening", "Traveling"],
          "documentEvidence": [
            {
              "documentId": 123,
              "pageReference": "p.6",
              "excerpt": "Plaintiff states inability to enjoy hobbies like gardening and traveling due to injury."
            }
          ]
        },
        "disfigurement": {
          "description": "Facial scarring from accident-related surgery.",
          "visibility": "Visible",
          "permanence": "Permanent",
          "documentEvidence": [
            {
              "documentId": 123,
              "pageReference": "p.3",
              "excerpt": "Surgical report describes permanent scarring on left cheek."
            }
          ]
        }
      }

      Do NOT attempt to calculate or estimate monetary values - only extract the supporting information.
    `

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    })

    // Parse the response
    let nonEconomicMetadata: NonEconomicDamagesMetadata = JSON.parse(response)

    // Ensure the required structure exists
    if (!nonEconomicMetadata.painAndSuffering) {
      nonEconomicMetadata.painAndSuffering = {
        severity: 'Moderate',
        duration: 'Temporary',
        impactDetails: 'Unknown - insufficient data in documents',
        documentEvidence: []
      }
    }

    logger.end('extractNonEconomicMetadata')

    return nonEconomicMetadata
  } catch (error: any) {
    logger.error('extractNonEconomicMetadata', error)
    // Return default structure if extraction fails
    return {
      painAndSuffering: {
        severity: 'Moderate',
        duration: 'Temporary',
        impactDetails: 'Unknown - extraction error',
        documentEvidence: []
      }
    }
  }
}

/**
 * Extract punitive damages metadata
 */
async function extractPunitiveDamagesMetadata(
  extractedRawData: Record<string, any>,
  documents: Record<string, DocumentContent[]>
): Promise<PunitiveDamagesMetadata> {
  logger.start('extractPunitiveDamagesMetadata')

  try {
    const prompt = `
      Based on the following extracted data, identify any information that might support punitive damages.
      Focus on:
      1. Evidence of intentional misconduct
      2. Reckless or grossly negligent behavior
      3. Prior similar incidents or pattern of behavior
      4. Malicious intent
      5. Violation of safety rules or regulations
      
      Extracted Data:
      ${JSON.stringify(extractedRawData)}
      
      Return a JSON object with:
      1. A list of eligibility factors supporting punitive damages
      2. Specific evidence of defendant conduct with document references
      3. Any information about defendant's prior history
      
      Be conservative in your assessment - only include clear factors that would typically support punitive damages.
      Do NOT recommend or calculate any monetary values.
    `

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1
    })

    // Parse the response
    const punitiveDamagesMetadata: PunitiveDamagesMetadata =
      JSON.parse(response)

    // Ensure the required arrays exist
    if (!punitiveDamagesMetadata.eligibilityFactors)
      punitiveDamagesMetadata.eligibilityFactors = []
    if (!punitiveDamagesMetadata.defendantConductEvidence)
      punitiveDamagesMetadata.defendantConductEvidence = []

    logger.end('extractPunitiveDamagesMetadata', {
      factorsCount: punitiveDamagesMetadata.eligibilityFactors.length,
      evidenceCount: punitiveDamagesMetadata.defendantConductEvidence.length
    })

    return punitiveDamagesMetadata
  } catch (error: any) {
    logger.error('extractPunitiveDamagesMetadata', error)
    // Return empty structure if extraction fails
    return {
      eligibilityFactors: [],
      defendantConductEvidence: []
    }
  }
}
