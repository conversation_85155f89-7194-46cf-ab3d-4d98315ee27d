'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { deleteTeamWithAllRecords } from '@/lib/actions/admin/team'
import { useRouter } from 'next/navigation'
import { Trash2 } from 'lucide-react'

interface DeleteTeamButtonProps {
  teamId: string
  teamName: string
}

export function DeleteTeamButton({ teamId, teamName }: DeleteTeamButtonProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleDelete = async () => {
    setIsLoading(true)

    try {
      const result = await deleteTeamWithAllRecords(teamId)

      if (result.success) {
        const summary = result.summary

        toast({
          title: 'Team Deleted Successfully',
          description: (
            <div className="space-y-2">
              <p>{result.message}</p>
              <div className="text-sm">
                <p className="font-medium mb-1">Deleted Records:</p>
                <ul className="list-disc list-inside space-y-0.5 text-xs">
                  <li>Total Records: {summary?.total || 0}</li>
                  <li>Users: {summary?.users || 0}</li>
                  <li>Binders: {summary?.binders || 0}</li>
                  <li>Documents: {summary?.documents || 0}</li>
                  <li>Case Files: {summary?.caseFiles || 0}</li>
                  <li>Research Stores: {summary?.researchStores || 0}</li>
                </ul>
              </div>
            </div>
          ),
          duration: 8000 // Show for 8 seconds for readability
        })
        setIsOpen(false)
        router.refresh()
      } else {
        toast({
          title: 'Error',
          description: result.message,
          variant: 'destructive'
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred while deleting the team',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="destructive" size="xs">
          Delete Team
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Team</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the team &quot;{teamName}&quot;?
            This action will permanently delete:
            <ul className="mt-2 list-disc list-inside space-y-1">
              <li>The team and all its settings</li>
              <li>All team documents and chunks</li>
              <li>All team credits (available and used)</li>
              <li>All binders and case files</li>
              <li>All show cause notices</li>
              <li>Remove all users from this team</li>
            </ul>
            <strong className="text-red-600 block mt-2">
              This action cannot be undone.
            </strong>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isLoading}
          >
            {isLoading ? 'Deleting...' : 'Delete Team'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
