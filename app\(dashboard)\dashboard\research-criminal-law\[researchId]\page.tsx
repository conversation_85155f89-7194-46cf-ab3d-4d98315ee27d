import { notFound, redirect } from 'next/navigation'

import { db } from '@/lib/db'
import { authOptions } from '@/lib/auth'
import { getCurrentUser, getFeatureUsageStats } from '@/lib/session'
import { features } from '@/config/dashboard'
import { Card } from '@/components/ui/card'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { CopyHrefButton } from '@/components/elements/buttons/button-copy-href'
import { ChatWindow } from '@/components/elements/chat/chat-window'
import { CreditType, ResearchType } from '@prisma/client'

import type { ResearchStoreContent } from '@/types'

export const metadata = features['research']

interface ResearchPageProps {
  params: { researchId: string }
}

export default async function ResearchPage({ params }: ResearchPageProps) {
  const user = await getCurrentUser()

  if (!user) {
    redirect(authOptions?.pages?.signIn || '/login')
  }

  const usageStats = await getFeatureUsageStats({
    feature: CreditType.research
  })

  const research = await db.researchStore.findFirst({
    where: {
      id: params.researchId
    }
  })

  if (!research) {
    return notFound()
  }

  const researchProps = research.content as unknown as ResearchStoreContent
  researchProps.researchId = research.id

  return (
    <DashboardShell>
      <div className="flex justify-between items-center">
        <DashboardHeader heading={metadata.title} text={metadata.description} />
        <CopyHrefButton />
      </div>
      <div className="grid gap-10">
        <Card>
          <ChatWindow
            user={user}
            stats={usageStats}
            researchType={ResearchType.case}
            researchProps={researchProps}
            showIngestForm={true}
            placeholder="Ask a question..."
            namespace="new-criminal-law<>old-criminal-law"
            showFilters={false}
          />
        </Card>
      </div>
    </DashboardShell>
  )
}
