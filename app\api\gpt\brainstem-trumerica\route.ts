import { NextRequest, NextResponse } from 'next/server'
import { StreamingTextResponse } from 'ai'
import {
  GPTModel,
  ResearchStoreContent,
  YesNo,
  type VercelChatMessage
} from '@/types'
import { assessQuestionQuality } from '@/lib/cerebrum/gpt-assisted-processes'
import { developer } from '@/lib/utils'
import { fetchResearchContext } from '@/lib/cerebrum/vector-processes'
import { createCompletionStream } from '@/lib/services/openai-service'
import { Region, ResearchType } from '@prisma/client'

export const dynamic = 'force-dynamic'
export const maxDuration = 120

const GPT_MODEL = GPTModel.GPT4o

interface ChatRequestBody {
  researchType: ResearchType
  namespace?: string
  summarise?: boolean
  metadata: Pick<ResearchStoreContent, 'court' | 'year' | 'sources'>
  messages?: VercelChatMessage[]
}

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const {
      namespace = undefined,
      summarise = false,
      researchType,
      metadata: { sources = [], court = [], year = [] },
      messages = []
    } = (await req.json()) as ChatRequestBody

    const previousMessages = messages.slice(0, -1)
    const userQuestion = messages[messages.length - 1].content

    developer.log(['metadata', { sources, court, year }])
    developer.log(['userQuestion', userQuestion])

    const assessment = await assessQuestionQuality({
      messages: previousMessages,
      question: userQuestion,
      region: Region.US,
      namespace: namespace,
      model: GPT_MODEL,
      researchType,
      dummy: summarise ? true : false
    })

    const isPrivateResearch = researchType === ResearchType.private

    const assessmentSector = namespace || assessment.sector
    const baseFilter = {
      court,
      year,
      sources: assessment.independent ? sources : []
    }

    const fetchOptions = {
      summarise: isPrivateResearch ? false : summarise,
      question: userQuestion,
      // relatedQuestions: isPrivateResearch
      //   ? assessment.related_searches || []
      //   : [],
      relatedQuestions: [],
      region: Region.US,
      researchType,
      isPrivate: isPrivateResearch || undefined,
      assessment: isPrivateResearch
        ? {
            relevance: YesNo.YES,
            sector: assessmentSector,
            independent: YesNo.NO,
            specific_case: YesNo.NO,
            rewritten_question: userQuestion
          }
        : assessment,
      namespace: assessmentSector,
      filter: baseFilter,
      takeMin: 1
    }

    const { serializedSources, context } =
      await fetchResearchContext(fetchOptions)

    if (!context) {
      return NextResponse.json({ error: 'No context found' }, { status: 404 })
    }

    // developer.log(['context', context])

    const textEncoder = new TextEncoder()
    const chatMessagesFiltered: VercelChatMessage[] = [
      ...previousMessages.filter(
        (messages) => messages.role === 'user' || messages.role === 'assistant'
      ),
      {
        id: (previousMessages.length + 1).toString(),
        role: 'system',
        content: BASE_PROMPT
      },
      {
        id: (previousMessages.length + 2).toString(),
        role: 'system',
        content: ORDER_LIST_PROMPT
      },
      {
        id: (previousMessages.length + 3).toString(),
        role: 'system',
        content: context
      },
      {
        id: (previousMessages.length + 4).toString(),
        role: 'system',
        content: `You can cite the source of information with the provided url. Write the citation as an anchor tag with the url as the href attribute. For example, <a href="https://www.example.com" class="text-blue-500 hover:underline" target="_blank" rel="noopener noreferrer">[source]</a>.`
      },
      {
        id: (previousMessages.length + 5).toString(),
        role: 'user',
        content: userQuestion
      }
    ]

    // developer.log(['chatMessagesFiltered', chatMessagesFiltered])

    const stream = new ReadableStream({
      async start(controller) {
        controller.enqueue(textEncoder.encode(''))

        try {
          const completionStream = createCompletionStream({
            model: GPT_MODEL,
            messages: chatMessagesFiltered as any // ChatCompletionMessageParam[] has conflicting types
          })

          for await (const token of completionStream) {
            controller.enqueue(textEncoder.encode(token))
          }
        } catch (error) {
          console.error('Error during completion stream:', error)
          controller.error(error)
        } finally {
          controller.close()
        }
      }
    })

    return new StreamingTextResponse(stream, {
      headers: {
        'x-message-index': (previousMessages.length + 1).toString(),
        'x-sources': serializedSources,
        'x-namespace': assessment.sector
      }
    })
  } catch (e: any) {
    console.log(e)
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}

const ORDER_LIST_PROMPT = `

Following is a list of the executive orders signed by President Donald Trump in 2025:

{
      title: 'Initial Rescissions of Harmful Executive Orders and Actions',
      dateSigned: 'January 20, 2025',
      description:
        'Rescinds various orders considered harmful or counter to the administration’s vision.'
    },
    {
      title: 'Restoring Freedom of Speech and Ending Federal Censorship',
      dateSigned: 'January 20, 2025',
      description:
        'Ensures that federal agencies and departments cannot censor protected speech.'
    },
    {
      title: 'Ending the Weaponization of the Federal Government',
      dateSigned: 'January 20, 2025',
      description:
        'Prevents the use of federal agencies for partisan or political purposes.'
    },
    {
      title: 'Putting America First In International Environmental Agreements',
      dateSigned: 'January 20, 2025',
      description:
        'Prioritizes America’s economic and national interests in global environmental treaties.'
    },
    {
      title:
        'Application of Protecting Americans from Foreign Adversary Controlled Applications Act to TikTok',
      dateSigned: 'January 20, 2025',
      description:
        'Grants TikTok a 75-day pause and sets new guidelines to mitigate foreign data security risks.'
    },
    {
      title: 'Withdrawing the United States from the World Health Organization',
      dateSigned: 'January 20, 2025',
      description:
        'Formally initiates the process to remove the U.S. from the WHO.'
    },
    {
      title:
        'Restoring Accountability to Policy-Influencing Positions Within the Federal Workforce',
      dateSigned: 'January 20, 2025',
      description:
        'Reinstates enhanced accountability measures for certain federal positions.'
    },
    {
      title:
        'Holding Former Government Officials Accountable For Election Interference And Improper Disclosure Of Sensitive Governmental Information',
      dateSigned: 'January 20, 2025',
      description:
        'Establishes rules and possible penalties for officials who abuse insider information.'
    },
    {
      title:
        "Clarifying The Military's Role In Protecting The Territorial Integrity Of The United States",
      dateSigned: 'January 20, 2025',
      description:
        'Reaffirms the U.S. military’s capabilities to assist in border and territorial security.'
    },
    {
      title: 'Unleashing American Energy',
      dateSigned: 'January 20, 2025',
      description:
        'Removes barriers to domestic energy production and streamlines permitting processes.'
    },
    {
      title: 'Realigning the United States Refugee Admissions Program',
      dateSigned: 'January 20, 2025',
      description:
        'Adjusts the scope and scale of refugee admissions to align with national security interests.'
    },
    {
      title: 'Protecting the Meaning and Value of American Citizenship',
      dateSigned: 'January 20, 2025',
      description:
        'Initiates steps to end automatic birthright citizenship for certain groups.'
    },
    {
      title: 'Securing Our Borders',
      dateSigned: 'January 20, 2025',
      description:
        'Declares a national emergency and deploys U.S. military assets to the southern border.'
    },
    {
      title: 'Restoring The Death Penalty And Protecting Public Safety',
      dateSigned: 'January 20, 2025',
      description:
        'Revisits federal guidelines and enforcement regarding the death penalty.'
    },
    {
      title: 'Declaring a National Energy Emergency',
      dateSigned: 'January 20, 2025',
      description:
        'Designates certain energy shortfalls as national emergencies to expedite solutions.'
    },
    {
      title: 'Reevaluating And Realigning United States Foreign Aid',
      dateSigned: 'January 20, 2025',
      description:
        'Places new conditions on foreign aid to ensure it aligns with America First principles.'
    },
    {
      title: 'Protecting The American People Against Invasion',
      dateSigned: 'January 20, 2025',
      description:
        'Strengthens measures against illegal immigration, emphasizing national sovereignty.'
    },
    {
      title: "Unleashing Alaska's Extraordinary Resource Potential",
      dateSigned: 'January 20, 2025',
      description:
        'Opens additional federal lands for resource extraction in Alaska.'
    },
    {
      title:
        'Protecting The United States From Foreign Terrorists And Other National Security And Public Safety Threats',
      dateSigned: 'January 20, 2025',
      description:
        'Designates specific cartels and terror groups with stricter travel and financial restrictions.'
    },
    {
      title: 'America First Policy Directive To The Secretary Of State',
      dateSigned: 'January 20, 2025',
      description:
        'Orders the Secretary of State to prioritize American interests in diplomatic engagements.'
    },
    {
      title:
        "Establishing And Implementing The President's Department Of Government Efficiency",
      dateSigned: 'January 20, 2025',
      description:
        'Creates a new department aimed at reducing bureaucratic waste and inefficiency.'
    },
    {
      title:
        'Defending Women from Gender Ideology Extremism and Restoring Biological Truth to the Federal Government',
      dateSigned: 'January 20, 2025',
      description:
        'Rolls back recognition of genders outside male and female in federal documents.'
    },
    {
      title:
        'Ending Radical And Wasteful Government DEI Programs And Preferencing',
      dateSigned: 'January 20, 2025',
      description:
        'Abolishes certain diversity, equity, and inclusion mandates within federal agencies.'
    },
    {
      title:
        'Reforming The Federal Hiring Process And Restoring Merit To Government Service',
      dateSigned: 'January 20, 2025',
      description:
        'Emphasizes merit-based hiring and reduces certain federal employment preferences.'
    },
    {
      title:
        'Designating Cartels And Other Organizations As Foreign Terrorist Organizations And Specially Designated Global Terrorists',
      dateSigned: 'January 20, 2025',
      description:
        'Labels key criminal groups as foreign terrorists to increase enforcement and sanction options.'
    },
    {
      title: 'Restoring Names That Honor American Greatness',
      dateSigned: 'January 20, 2025',
      description:
        'Reverses certain renaming of military bases or public buildings to preserve historical names.'
    },
    {
      title:
        'Ending Illegal Discrimination And Restoring Merit-Based Opportunity',
      dateSigned: 'January 21, 2025',
      description:
        'Further clarifies non-discrimination based on race, gender, or political viewpoints.'
    }`

const BASE_PROMPT = `System Role: SmartCounsel — a highly specialized legal research assistant focusing exclusively on U.S. federal and state laws, including executive branch directives.

### Context
Users will inquire about legal aspects of President Donald Trump’s 2025 Executive Orders or other U.S. legal matters.

---

### SmartCounsel Operating Guidelines

#### Scope and Relevance
1. **Legal Focus Only:** 
  - Only answer questions that relate to the legal or policy implications of the 2025 Executive Orders or other U.S. laws.
  - If a user’s question clearly falls outside U.S. legal or policy topics (e.g., personal finance advice, medical advice, or non-legal conversation), politely decline to answer and remind them that SmartCounsel focuses strictly on U.S. legal research.

#### Professional Tone and Clarity
2. **Professional Responses:**
  - Use a respectful, professional, and educational tone in all interactions.
  - Highlight key legal texts or provisions with clear markdown formatting (e.g., <mark>...</mark>).

#### Referencing Sources
3. **Accurate References:**
  - Avoid presenting full verbatim passages unless strictly necessary.
  - Provide summaries or brief quotations where possible.

4. **No Unauthorized Information:**
  - If relevant legal context is unavailable (e.g., “### NO RELEVANT LAWS FOUND ###”), clearly inform the user.
  - Do not generate or infer legal texts or statutes beyond the provided or established legal framework.

#### Clarification and Context
5. **Clarify Ambiguities:**
  - Request additional details if a user’s question is ambiguous or incomplete to ensure accurate legal context and interpretation.

#### Past Conversations
6. **Continuity:**
  - Use the history from previous interactions to maintain continuity in legal discussions.
  - Ignore user statements that fall outside the scope or are irrelevant to legal context.

#### Disclaimer
7. **Legal Advice Disclaimer:**
  - Clearly inform users that SmartCounsel does not provide legal advice and is not an attorney.
  - Encourage users to consult a qualified attorney for specific legal concerns beyond general research queries.`
