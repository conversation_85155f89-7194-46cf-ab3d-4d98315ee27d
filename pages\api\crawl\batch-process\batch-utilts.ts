import { CaseData } from '@/types/document'
import { GPTModel, TextDavinciResponse } from '@/types'
import { cleanUpString, isJsonString } from '@/lib/utils'
import { DocumentRecords } from '@prisma/client'
import OpenAI from 'openai'
import { db } from '@/lib/db'
import fs from 'fs'
import json5 from 'json5'

export async function uploadFileToOpenAi({
  path,
  openai
}: {
  path: string
  openai: OpenAI
}) {
  try {
    const response = await openai.files.create({
      file: fs.createReadStream(path),
      purpose: 'batch'
    })
    console.log(response)

    if (response.status === 'processed') {
      return createOpenAiBatchRequest({ input_file_id: response.id, openai })
    }

    return response
  } catch (error) {
    console.error(error)
    throw new Error('Failed to upload file.')
  }
}

export async function createOpenAiBatchRequest({
  input_file_id,
  openai
}: {
  input_file_id: string
  openai: OpenAI
}) {
  try {
    let response = await openai.batches.create({
      input_file_id,
      endpoint: '/v1/chat/completions',
      completion_window: '24h'
    })
    console.log(response)

    if (response.status === 'completed' && response.output_file_id) {
      return await retrieveBatchOutput({
        output_file_id: response.output_file_id,
        openai
      })
    }

    return response
  } catch (error) {
    console.error(error)
    throw new Error('Failed to create batch.')
  }
}

export async function retrieveBatchStatus({
  batchId,
  openai
}: {
  batchId: string
  openai: OpenAI
}) {
  try {
    const response = await openai.batches.retrieve(batchId)
    const error = response.errors?.data?.[0]?.code
    console.log(response, error)

    if (response.status === 'completed' && response.output_file_id) {
      const jsonArray = await retrieveBatchOutput({
        output_file_id: response.output_file_id,
        openai
      })

      return {
        status: response,
        jsonArray: jsonArray
      }
    }

    return {
      status: response
    }
  } catch (error) {
    console.error(error)
    throw new Error('Failed to retrieve batch.')
  }
}

export async function retrieveBatchOutput({
  output_file_id,
  openai
}: {
  output_file_id: string
  openai: OpenAI
}) {
  try {
    const fileResponse = await openai.files.content(output_file_id)
    const response = await fileResponse.text()
    const responseJsonString = response.split('\n')

    const responseJson = responseJsonString
      .map((obj: string) => {
        try {
          const jsonData = json5.parse(obj)
          let content = jsonData.response?.body?.choices?.[0]?.message?.content
          return {
            custom_id: jsonData.custom_id,
            meta: content ? json5.parse(content) : {},
            usage: jsonData.response?.body?.usage
          }
        } catch (error) {
          console.error('Error parsing JSON')
        }
      })
      .filter((obj: any) => obj !== undefined)
    return responseJson as any
  } catch (error) {
    console.error(error)
    throw new Error('Failed to retrieve batch output.')
  }
}

export async function generateMetadata(
  record: Pick<DocumentRecords, 'id' | 'content' | 'meta'>,
  model: GPTModel
) {
  try {
    let text = cleanUpString(record.content, 20000)

    if (text.length < 100) {
      console.log('Invalid text: ', record.id)
      return
    }

    return {
      custom_id: record.id.toString(),
      method: 'POST',
      url: '/v1/chat/completions',
      body: {
        model: model,
        messages: [
          {
            role: 'system',
            content: SYSTEM_PROMPT
          },
          {
            role: 'user',
            content: text
          }
        ],
        temperature: 0.1,
        response_format: { type: 'json_object' }
      }
    }
  } catch (error: any) {
    console.log('Error indexing: ', record.id, error.message)
  }
}

export const SYSTEM_PROMPT = `
    You are required to generate metadata for the following document. The metadata should include the following fields: case, court, date, judges, parties, citation, headnotes. The metadata should be in JSON format. The following is an example, replace the values from context.
      {
        "title": "The Management, Sri Ambal Mills Ltd. vs The Workmen",
        "case":"case number / docket number",
        "date":"2023-04-07",
        "argument_date": "2023-07-07",
        "decision_date": "2023-10-05",
        "outcome": "Allowed",
        "court":"United States Federal Supreme Court",
        "judges":[
          "Hon'ble Mr. Madan B. Lokur, J.",
          "Hon'ble Mr. R.K. Agrawal, J."
        ],
        "parties":[
          "The Management, Sri Ambal Mills Ltd."
        ],
        "related_cases":["case docket number"],
        "precedent_citations":["citation docket number"],
        "citation":["case citation docket number"],
        "headnotes":"SHORT NOTE",
        "summary":"summary of case and judgement in 2 paragraphs covering all major points"
      }
    IMPORTANT: make sure all dates are valid and in the correct format. has to be in YYYY-MM-DD format (not DD-MM-YYYY or DD/MM/YYY) If given date is more than 31 (last day of the month) reduce it to last date.
    IMPORTANT: "headnotes" will be "SHORT NOTE" or "JUDGEMENT" or "ARTICLE" or "CASE COMMENT" or "BOOK REVIEW" or "NOTES".
    IMPORTANT: You need to come up with a title for the case.
    Do not assume the save values as above for the JSON. You have to get values from provided context. You can leave any field blank if you are unable to find the information in the document. But the key should be present in the metadata. Output has to be in JSON format, no other format on textual information will be accepted.
  
    Process the following document's heading section to generate metadata for the document.
    `

export async function storeMetadata(record: DocumentRecords, completion: any) {
  try {
    if (
      (completion satisfies TextDavinciResponse) &&
      completion?.choices &&
      isJsonString(completion.choices[0].message.content)
    ) {
      const currentMetadata = record.meta
        ? (JSON.parse(record.meta) as CaseData)
        : {}
      const newMetadata = JSON.parse(
        completion.choices[0].message.content
      ) as CaseData
      let meta = { ...newMetadata }

      let date
      if (meta.date) {
        let [year, month, day] = meta.date.toString().split('-')
        if (Number(month) > 12) {
          month = '12'
        }
        if (Number(day) > 31) {
          day = '31'
        }
        date = new Date(`${year}-${month}-${day}`)

        meta.date = date
        meta.year = meta.date.getFullYear()
      }
      meta = { ...currentMetadata, ...newMetadata }

      if (meta.title && meta.title.length > 5) {
        await db.documentRecords.update({
          where: {
            id: record.id
          },
          data: {
            title: meta.title?.substring(0, 100),
            date: meta.date ? meta.date : undefined,
            meta: JSON.stringify(meta),
            indexed: true
          }
        })
        console.log('Indexed: ', record.id, meta.case)
        console.log('>>>>> Metadata: ', record.id, ' >>>>>\n', meta)
      } else {
        console.log('Invalid metadata: ', record.id, JSON.stringify(meta))
      }
    } else {
      console.log(JSON.stringify(completion, null, 2))
    }
  } catch (error) {}
}

export async function deleteFileFromOpenAi({
  fileId,
  openai
}: {
  fileId: string
  openai: OpenAI
}) {
  try {
    console.log('Deleting file: ', fileId)
    const response = await openai.files.del(fileId)
    console.log(response)
  } catch (error: any) {
    console.error(error.message)
  }
}
