datasource vectordb {
    provider = "postgresql"
    url      = env("POSTGRESQL_DATABASE_URL")
}

generator vectordb_client {
    provider = "prisma-client-js"
    output   = "../generated/postgres"
}

model USVectorStore {
    id        Int                         @id @default(autoincrement())
    namespace String
    docId     Int
    year      Int?
    court     String?
    metadata  Json?
    chunkText String                      @vectordb.Text
    embedding Unsupported("vector(1536)")

    @@index([namespace])
    @@index([year])
    @@index([court])
}

model INVectorStore {
    id        Int                         @id @default(autoincrement())
    namespace String
    docId     Int
    year      Int?
    court     String?
    metadata  Json?
    chunkText String                      @vectordb.Text
    embedding Unsupported("vector(1536)")

    @@index([namespace])
    @@index([year])
    @@index([court])
}

model UserVectorStore {
    id        Int    @id @default(autoincrement())
    teamid    String
    docId     Int
    metadata  Json?
    chunkText String @vectordb.Text
    embedding Bytes  @vectordb.ByteA

    @@index([teamid, docId])
}
