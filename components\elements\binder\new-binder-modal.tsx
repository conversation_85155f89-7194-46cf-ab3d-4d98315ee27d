'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { buttonVariants } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'
import { NewBinderForm } from './new-binder-form'

export const NewBinderModal = ({ allow }: { allow: boolean }) => {
  const [isOpen, setIsOpen] = useState(false)

  const handleSuccess = () => {
    toast({
      title: 'Case created successfully',
      description: 'The case has been created successfully.'
    })
    setIsOpen(false) // Close dialog on success
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger
        className={buttonVariants({
          variant: 'default',
          size: 'sm'
        })}
        onClick={() => setIsOpen(allow && true)}
        disabled={!allow}
      >
        {allow ? 'Create New Case' : 'Upgrade to create case'}
      </DialogTrigger>
      <DialogContent className="max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Create New Case</DialogTitle>
        </DialogHeader>
        <NewBinderForm onSuccess={handleSuccess} />
      </DialogContent>
    </Dialog>
  )
}
