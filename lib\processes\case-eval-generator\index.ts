// lib/processes/case-eval-generator.ts

import { db } from '@/lib/db'
import {
  extractFinancialData,
  structureMetadata
} from './financial-data-extractor'
import { processAndCalculateDamages } from './damage-calculator'
import { ExtractedCaseFinancialData } from './financial-extraction-types'
import { createCompletion } from '@/lib/services/openai-service'
import { AttorneyInsightsFormData } from '@/components/elements/doc-selector/attorney-strategy-form'
import { attorneyInsightsSectionMapping } from './config'
import {
  CaseEvaluation,
  CaseEvaluationOptions,
  ContextData,
  DamagesCalculation,
  DefensePerspective,
  DocumentContent,
  LiabilityAssessment,
  LitigationStrategy,
  RiskAssessment
} from '@/types/case'
import { AuthUser } from 'next-auth'
import {
  logger,
  generateSectionsInBatches,
  sectionNeedsExtension,
  extendSectionContent,
  createSectionPrompt
} from '../utils-llm'
import { caseEvalDamageDetailPrompts } from './case-evaluation-prompts'
import { CaseFileType } from '@prisma/client'

// Maximum number of continuations per section
const MAX_CONTINUATIONS = 3

// Define sections that are likely to need extension due to complexity
const sectionsNeedingExtension = [
  'risk_assessment',
  'litigation_strategy',
  'defense_perspective',
  'final_recommendations',
  'revised_report'
]

// Main function to orchestrate the entire process
export async function generateCaseEvaluation(
  caseId: string,
  documents: Record<string, DocumentContent[]>,
  user: AuthUser,
  attorneyInsights?: AttorneyInsightsFormData | null,
  options: CaseEvaluationOptions = {}
): Promise<CaseEvaluation> {
  logger.start('generateCaseEvaluation', {
    documentCount: Object.values(documents).flat().length
  })

  const {
    includeDefensePerspective = true,
    nonEconomicMultiplierOverride,
    punitiveMultiplierOverride
  } = options

  try {
    // Step 1: Extract financial data from documents
    logger.info('Starting financial data extraction from documents')
    const extractedFinancialData = await extractFinancialData(documents)
    const metadataResult = await structureMetadata(extractedFinancialData)

    const { plaintiffInfo, incidentDetails } = metadataResult

    // Step 2: Process attorney insights if provided
    logger.info('Processing attorney insights and strategic considerations')
    const processedInsights = attorneyInsights
      ? await processAttorneyInsights(attorneyInsights, user)
      : null

    // Step 3: Assess liability
    logger.info('Assessing liability based on document evidence')
    const liabilityAssessment = await assessLiability(
      documents,
      extractedFinancialData.liabilityDistribution,
      processedInsights,
      user
    )

    // Step 4: Calculate damages
    logger.info('Calculating damages based on extracted financial data')
    const {
      economicDamages,
      nonEconomicDamages,
      punitiveDamagesData,
      damagesCalculation
    } = processAndCalculateDamages(
      extractedFinancialData,
      nonEconomicMultiplierOverride,
      punitiveMultiplierOverride
    )

    // Step 5: Identify case strengths and weaknesses
    logger.info('Identifying case strengths and weaknesses')
    const riskAssessment = await identifyCaseRisks(
      documents,
      extractedFinancialData,
      liabilityAssessment,
      damagesCalculation,
      processedInsights,
      user
    )

    // Step 6: Develop litigation strategy
    logger.info('Developing litigation strategy')
    const litigationStrategy = await developLitigationStrategy(
      liabilityAssessment,
      damagesCalculation,
      riskAssessment,
      processedInsights,
      user
    )

    // Step 7: Generate initial markdown report
    logger.info('Generating initial case evaluation report')
    const initialMarkdownReport = await generateCaseEvaluationReport(
      {
        plaintiffInfo,
        incidentDetails,
        liabilityAssessment,
        economicDamages,
        nonEconomicDamages,
        punitiveDamagesData,
        damagesCalculation,
        riskAssessment,
        litigationStrategy,
        attorneyInsights: processedInsights
      },
      caseId,
      extractedFinancialData
    )
    logger.info('Generated initial report', initialMarkdownReport)

    // Create the initial case evaluation result
    let result: CaseEvaluation = {
      plaintiffInfo,
      incidentDetails,
      liabilityAssessment,
      economicDamages,
      nonEconomicDamages,
      punitiveDamagesData,
      damagesCalculation,
      riskAssessment,
      litigationStrategy,
      initialMarkdownReport,
      extractedFinancialData
    }

    // If defense perspective analysis is requested
    if (includeDefensePerspective) {
      // Step 8: Analyze from defense perspective
      logger.info('Analyzing case from defense perspective')
      const defensePerspective = await analyzeDefensePerspective(result, user)
      result.defensePerspective = defensePerspective

      // Step 9: Revise report based on defense perspective
      logger.info(
        'Revising case evaluation report based on defense perspective'
      )
      const revisedMarkdownReport = await generateRevisedReport(
        result,
        defensePerspective,
        user
      )
      logger.info('Generated revised report', revisedMarkdownReport)
      result.revisedMarkdownReport = revisedMarkdownReport
    }

    logger.end('generateCaseEvaluation')
    return result
  } catch (error: any) {
    logger.error('generateCaseEvaluation', error)
    throw new Error(`Failed to generate case evaluation: ${error.message}`)
  }
}

// Process attorney insights form
async function processAttorneyInsights(
  insightsForm: AttorneyInsightsFormData,
  user: AuthUser
): Promise<any> {
  logger.start('processAttorneyInsights')

  const process_attorney_insights_prompt = await db.prompt.findFirst({
    where: {
      source: 'AI_CASE_EVALUATION_PROCESS_ATTORNEY_INSIGHTS'
    }
  })

  try {
    // Convert raw form data into structured insights that can be used by other functions
    const prompt =
      process_attorney_insights_prompt!.prompt
        .replace('{{localCourtDynamics}}', insightsForm.courtDynamics || '')
        .replace('{{defendantInfo}}', insightsForm.defenseBehavior || '')
        .replace(
          '{{clientCircumstances}}',
          insightsForm.clientCircumstances || ''
        )
        .replace('{{preTrialStrategy}}', insightsForm.pretrialStrategy || '')
        .replace('{{trialReadiness}}', insightsForm.trialReadiness || '')
        .replace('{{potentialWeaknesses}}', insightsForm.weakSpots || '')
        .replace('{{comparableCases}}', insightsForm.comparableCases || '')
        .replace('{{attorneyGutFeel}}', insightsForm.gutFeel || '')
        .replace(
          '{{miscellaneousObservations}}',
          insightsForm.miscObservations || ''
        ) + process_attorney_insights_prompt!.expectedOutput

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1,
      teamId: user.teamId,
      purpose: 'case-eval',
      activity: 'attorney-insights-processing'
    })

    const structuredInsights = JSON.parse(response)

    // Store the original form data for section-specific filtering
    structuredInsights.rawFormData = insightsForm

    logger.end('processAttorneyInsights')
    return structuredInsights
  } catch (error: any) {
    logger.error('processAttorneyInsights', error)
    throw new Error(`Failed to process attorney insights: ${error.message}`)
  }
}

// Assess liability based on document data and extracted liability distribution
async function assessLiability(
  documents: Record<string, DocumentContent[]>,
  liabilityDistribution?: {
    plaintiffPercentage: number
    defendantPercentage: number
    otherPercentage?: number
  },
  attorneyInsights?: any,
  user?: AuthUser
): Promise<LiabilityAssessment> {
  logger.start('assessLiability')

  const assess_liability_prompt = await db.prompt.findFirst({
    where: {
      source: 'AI_CASE_EVALUATION_ASSESS_LIABILITY'
    }
  })

  try {
    // Filter attorney insights for liability assessment
    const filteredInsights = filterAttorneyInsightsForSection(
      attorneyInsights,
      'liability_assessment'
    )

    // Prepare consolidated data for the AI
    const dataForAnalysis = {
      documents,
      liabilityDistribution,
      attorneyInsights: filteredInsights
    }

    const prompt =
      assess_liability_prompt!.prompt.replace(
        '{{context}}',
        JSON.stringify(dataForAnalysis)
      ) + assess_liability_prompt!.expectedOutput

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1,
      teamId: user?.teamId,
      purpose: 'case-eval',
      activity: 'liability-assessment'
    })

    const liabilityAssessment: LiabilityAssessment = JSON.parse(response)

    // Override the fault distribution with the extracted/calculated values if available
    if (liabilityDistribution) {
      liabilityAssessment.faultDistribution = {
        plaintiff: liabilityDistribution.plaintiffPercentage,
        defendant: liabilityDistribution.defendantPercentage,
        other: liabilityDistribution.otherPercentage
      }
    }

    logger.end('assessLiability')
    return liabilityAssessment
  } catch (error: any) {
    logger.error('assessLiability', error)
    throw new Error(`Failed to assess liability: ${error.message}`)
  }
}

// Identify case risks, strengths, and weaknesses
async function identifyCaseRisks(
  documents: Record<string, DocumentContent[]>,
  extractedFinancialData: ExtractedCaseFinancialData,
  liabilityAssessment: LiabilityAssessment,
  damagesCalculation: DamagesCalculation,
  attorneyInsights?: any,
  user?: AuthUser
): Promise<RiskAssessment> {
  logger.start('identifyCaseRisks')

  const identify_case_risks_prompt = await db.prompt.findFirst({
    where: {
      source: 'AI_CASE_EVALUATION_IDENTIFY_CASE_RISKS'
    }
  })

  try {
    // Filter attorney insights for risk assessment
    const filteredInsights = filterAttorneyInsightsForSection(
      attorneyInsights,
      'risk_analysis'
    )

    const dataForAnalysis = {
      documents,
      extractedFinancialData,
      liabilityAssessment,
      damagesCalculation,
      attorneyInsights: filteredInsights
    }

    const prompt =
      identify_case_risks_prompt!.prompt.replace(
        '{{context}}',
        JSON.stringify(dataForAnalysis)
      ) + identify_case_risks_prompt!.expectedOutput

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.2,
      teamId: user?.teamId,
      purpose: 'case-eval',
      activity: 'risk-assessment'
    })

    const riskAssessment: RiskAssessment = JSON.parse(response)

    logger.end('identifyCaseRisks')
    return riskAssessment
  } catch (error: any) {
    logger.error('identifyCaseRisks', error)
    throw new Error(`Failed to identify case risks: ${error.message}`)
  }
}

// Develop litigation strategy
async function developLitigationStrategy(
  liabilityAssessment: LiabilityAssessment,
  damagesCalculation: DamagesCalculation,
  riskAssessment: RiskAssessment,
  attorneyInsights?: any,
  user?: AuthUser
): Promise<LitigationStrategy> {
  logger.start('developLitigationStrategy')

  const develop_litigation_strategy_prompt = await db.prompt.findFirst({
    where: {
      source: 'AI_CASE_EVALUATION_DEVELOP_LITIGATION_STRATEGY'
    }
  })

  try {
    // Filter attorney insights for litigation strategy
    const filteredInsights = filterAttorneyInsightsForSection(
      attorneyInsights,
      'litigation_strategy'
    )

    const dataForAnalysis = {
      liabilityAssessment,
      damagesCalculation,
      riskAssessment,
      attorneyInsights: filteredInsights
    }

    const prompt =
      develop_litigation_strategy_prompt!.prompt.replace(
        '{{context}}',
        JSON.stringify(dataForAnalysis)
      ) + develop_litigation_strategy_prompt!.expectedOutput

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
      teamId: user?.teamId,
      purpose: 'case-eval',
      activity: 'litigation-strategy'
    })

    const litigationStrategy: LitigationStrategy = JSON.parse(response)

    logger.end('developLitigationStrategy')
    return litigationStrategy
  } catch (error: any) {
    logger.error('developLitigationStrategy', error)
    throw new Error(`Failed to develop litigation strategy: ${error.message}`)
  }
}

// Generate initial markdown report
async function generateCaseEvaluationReport(
  caseEvaluation: Omit<
    CaseEvaluation,
    | 'initialMarkdownReport'
    | 'defensePerspective'
    | 'revisedMarkdownReport'
    | 'extractedFinancialData'
    | 'initialMarkdownReport'
    | 'defensePerspective'
    | 'revisedMarkdownReport'
    | 'extractedFinancialData'
  >,
  caseId: string,
  extractedFinancialData: ExtractedCaseFinancialData
): Promise<string> {
  logger.start('generateCaseEvaluationReport')
  const sections = await db.prompt.findMany({
    where: {
      source: {
        in: [
          'AI_CASE_EVALUATION_CASE_OVERVIEW',
          'AI_CASE_EVALUATION_LIABILITY_ASSESSMENT',
          'AI_CASE_EVALUATION_DAMAGES_CALCULATION',
          'AI_CASE_EVALUATION_PAST_MEDICAL_EXPENSES',
          'AI_CASE_EVALUATION_FUTURE_MEDICAL_EXPENSES',
          'AI_CASE_EVALUATION_RISK_ANALYSIS',
          'AI_CASE_EVALUATION_LITIGATION_STRATEGY',
          'AI_CASE_EVALUATION_ATTORNEY_INSIGHTS',
          'AI_CASE_EVALUATION_FINAL_RECOMMENDATION'
        ]
      }
    }
  })

  const promptMap = sections.reduce<Record<string, (typeof sections)[number]>>(
    (acc, sec) => {
      acc[sec.source] = sec
      return acc
    },
    {}
  )

  // Check if required prompts exist
  const requiredPrompts = [
    'AI_CASE_EVALUATION_PAST_MEDICAL_EXPENSES',
    'AI_CASE_EVALUATION_FUTURE_MEDICAL_EXPENSES'
  ]

  for (const promptSource of requiredPrompts) {
    if (!promptMap[promptSource]) {
      logger.error(
        'generateCaseEvaluationReport',
        `Missing required prompt: ${promptSource}`
      )
      throw new Error(`Missing required prompt: ${promptSource}`)
    }
  }

  // Get medical chronology for damage detail sections
  const medcron = await db.caseFile.findFirst({
    where: {
      binderId: caseId,
      fileType: CaseFileType.MEDICAL_CHRONOLOGY
    }
  })

  // Create comprehensive section context for damage detail sections
  let sectionContext = {
    PLAINTIFF_INFO: caseEvaluation.plaintiffInfo,
    INCIDENT_DETAILS: caseEvaluation.incidentDetails,
    MEDICAL_CHRONOLOGY: medcron?.content,
    EXTRACTED_FINANCIAL_DATA: extractedFinancialData,
    LIABILITY_ASSESSMENT: caseEvaluation.liabilityAssessment,
    ECONOMIC_DAMAGES: caseEvaluation.economicDamages,
    NON_ECONOMIC_DAMAGES: caseEvaluation.nonEconomicDamages,
    PUNITIVE_DAMAGES: caseEvaluation.punitiveDamagesData,
    DAMAGES_CALCULATION: caseEvaluation.damagesCalculation
    // CASE_EVALUATION: caseEvaluation
  }
  const generatedCaseEvalDamagePrompts: Record<string, string> = {}

  caseEvalDamageDetailPrompts.map((p, i) => {
    const initialPrompt = createSectionPrompt({
      section: p,
      context: sectionContext,
      documentList: []
    })

    generatedCaseEvalDamagePrompts[p.id] = initialPrompt
  })

  try {
    // Create prompts for each section using the prompt templates with filtered insights
    const sectionPrompts: Record<string, string> = {
      case_overview:
        promptMap['AI_CASE_EVALUATION_CASE_OVERVIEW'].prompt.replace(
          '{{context}}',
          JSON.stringify({
            ...caseEvaluation,
            attorneyInsights: filterAttorneyInsightsForSection(
              caseEvaluation.attorneyInsights,
              'case_overview'
            )
          })
        ) + promptMap['AI_CASE_EVALUATION_CASE_OVERVIEW'].expectedOutput,

      liability_assessment:
        promptMap['AI_CASE_EVALUATION_LIABILITY_ASSESSMENT'].prompt.replace(
          '{{context}}',
          JSON.stringify(caseEvaluation.liabilityAssessment)
        ) + promptMap['AI_CASE_EVALUATION_LIABILITY_ASSESSMENT'].expectedOutput,

      damages_calculation:
        promptMap['AI_CASE_EVALUATION_DAMAGES_CALCULATION'].prompt
          .replace(
            '{{economicDamages}}',
            JSON.stringify(caseEvaluation.economicDamages)
          )
          .replace(
            '{{nonEconomicDamages}}',
            JSON.stringify(caseEvaluation.nonEconomicDamages)
          )
          .replace(
            '{{punitiveDamages}}',
            JSON.stringify(caseEvaluation.punitiveDamagesData)
          )
          .replace(
            '{{damagesCalculation}}',
            JSON.stringify(caseEvaluation.damagesCalculation)
          ) +
        promptMap['AI_CASE_EVALUATION_DAMAGES_CALCULATION'].expectedOutput,

      // Add damage detail sections using the same pattern
      past_medical_expenses:
        promptMap['AI_CASE_EVALUATION_PAST_MEDICAL_EXPENSES'].prompt.replace(
          '{{context}}',
          JSON.stringify(sectionContext)
        ) +
        promptMap['AI_CASE_EVALUATION_PAST_MEDICAL_EXPENSES'].expectedOutput,

      future_medical_expenses:
        promptMap['AI_CASE_EVALUATION_FUTURE_MEDICAL_EXPENSES'].prompt.replace(
          '{{context}}',
          JSON.stringify(sectionContext)
        ) +
        promptMap['AI_CASE_EVALUATION_FUTURE_MEDICAL_EXPENSES'].expectedOutput,

      risk_analysis:
        promptMap['AI_CASE_EVALUATION_RISK_ANALYSIS'].prompt.replace(
          '{{context}}',
          JSON.stringify({
            ...caseEvaluation.riskAssessment,
            _attorneyInsights: filterAttorneyInsightsForSection(
              caseEvaluation.attorneyInsights,
              'risk_analysis'
            )
          })
        ) + promptMap['AI_CASE_EVALUATION_RISK_ANALYSIS'].expectedOutput,

      litigation_strategy:
        promptMap['AI_CASE_EVALUATION_LITIGATION_STRATEGY'].prompt.replace(
          '{{context}}',
          JSON.stringify({
            ...caseEvaluation.litigationStrategy,
            _attorneyInsights: filterAttorneyInsightsForSection(
              caseEvaluation.attorneyInsights,
              'litigation_strategy'
            )
          })
        ) + promptMap['AI_CASE_EVALUATION_LITIGATION_STRATEGY'].expectedOutput,

      attorney_insights:
        promptMap['AI_CASE_EVALUATION_ATTORNEY_INSIGHTS'].prompt.replace(
          '{{context}}',
          JSON.stringify({
            ...caseEvaluation,
            attorneyInsights: filterAttorneyInsightsForSection(
              caseEvaluation.attorneyInsights,
              'attorney_insights'
            )
          })
        ) + promptMap['AI_CASE_EVALUATION_ATTORNEY_INSIGHTS'].expectedOutput,

      final_recommendations:
        promptMap['AI_CASE_EVALUATION_FINAL_RECOMMENDATION'].prompt.replace(
          '{{context}}',
          JSON.stringify({
            ...caseEvaluation,
            attorneyInsights: filterAttorneyInsightsForSection(
              caseEvaluation.attorneyInsights,
              'final_recommendations'
            )
          })
        ) + promptMap['AI_CASE_EVALUATION_FINAL_RECOMMENDATION'].expectedOutput
    }

    // Define the section order for the final report
    const sectionOrder = [
      'case_overview',
      'liability_assessment',
      'damages_calculation',
      'past_medical_expenses',
      'future_medical_expenses',
      'risk_analysis',
      'litigation_strategy',
      'attorney_insights',
      'final_recommendations'
    ]

    // Generate all sections in parallel
    const sectionContents = await generateReportSectionsInParallel(
      caseEvaluation,
      sectionPrompts
    )
    logger.info('Generated section details', sectionContents)

    // Log specific sections for debugging
    logger.info('Past medical expenses section:', {
      exists: !!sectionContents.past_medical_expenses,
      length: sectionContents.past_medical_expenses?.length || 0,
      preview:
        sectionContents.past_medical_expenses?.substring(0, 200) || 'NOT FOUND'
    })
    logger.info('Future medical expenses section:', {
      exists: !!sectionContents.future_medical_expenses,
      length: sectionContents.future_medical_expenses?.length || 0,
      preview:
        sectionContents.future_medical_expenses?.substring(0, 200) ||
        'NOT FOUND'
    })

    // Combine all sections in the correct order
    const combinedReport = sectionOrder
      .filter((id) => sectionContents[id]) // Only include sections that were generated
      .map((id) => {
        const content = sectionContents[id]
        // Add section heading if not already included
        if (
          !content.includes(
            `# ${id
              .split('_')
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(' ')}`
          )
        ) {
          return `# ${id
            .split('_')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ')}\n\n${content}`
        }
        return content
      })
      .join('\n\n')
      .replace(/markdown/g, '')
      .replace(/```/g, '')

    logger.end('generateCaseEvaluationReport', {
      reportLength: combinedReport.length,
      sections: combinedReport.split('#').length - 1
    })

    return combinedReport
  } catch (error: any) {
    logger.error('generateCaseEvaluationReport', error)
    throw new Error(
      `Failed to generate case evaluation report: ${error.message}`
    )
  }
}

// Analyze from defense perspective
async function analyzeDefensePerspective(
  caseEvaluation: Omit<
    CaseEvaluation,
    'defensePerspective' | 'revisedMarkdownReport'
  >,
  user?: AuthUser
): Promise<DefensePerspective> {
  logger.start('analyzeDefensePerspective')

  const analyze_defense_perspective_prompt = await db.prompt.findFirst({
    where: {
      source: 'AI_CASE_EVALUATION_ANALYZE_DEFENSE_PERSPECTIVE'
    }
  })

  try {
    // Filter attorney insights for defense perspective analysis
    const modifiedCaseEvaluation = {
      ...caseEvaluation,
      attorneyInsights: filterAttorneyInsightsForSection(
        caseEvaluation.attorneyInsights,
        'analyze_defense_perspective'
      )
    }

    const prompt =
      analyze_defense_perspective_prompt!.prompt
        .replace('{{context}}', JSON.stringify(modifiedCaseEvaluation))
        .replace('{{initialReport}}', caseEvaluation.initialMarkdownReport) +
      analyze_defense_perspective_prompt!.expectedOutput

    const response = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
      teamId: user?.teamId,
      purpose: 'case-eval',
      activity: 'defense-perspective-analysis'
    })

    const defensePerspective: DefensePerspective = JSON.parse(response)

    logger.end('analyzeDefensePerspective')
    return defensePerspective
  } catch (error: any) {
    logger.error('analyzeDefensePerspective', error)
    throw new Error(`Failed to analyze defense perspective: ${error.message}`)
  }
}

// Generate revised report based on defense perspective
async function generateRevisedReport(
  caseEvaluation: Omit<CaseEvaluation, 'revisedMarkdownReport'>,
  defensePerspective: DefensePerspective,
  user?: AuthUser
): Promise<string> {
  logger.start('generateRevisedReport')

  const generate_revised_report_prompt = await db.prompt.findFirst({
    where: {
      source: 'AI_CASE_EVALUATION_GENERATE_REVISED_REPORT'
    }
  })

  try {
    // Filter attorney insights for the revised report
    const modifiedCaseEvaluation = {
      ...caseEvaluation,
      attorneyInsights: filterAttorneyInsightsForSection(
        caseEvaluation.attorneyInsights,
        'generate_revised_report'
      )
    }

    const prompt =
      generate_revised_report_prompt!.prompt
        .replace('{{caseEvaluation}}', JSON.stringify(modifiedCaseEvaluation))
        .replace('{{defensePerspective}}', JSON.stringify(defensePerspective))
        .replace('{{initialReport}}', caseEvaluation.initialMarkdownReport) +
      generate_revised_report_prompt!.expectedOutput

    // Log the initial report content for debugging
    logger.info('Initial report content for revision:', {
      length: caseEvaluation.initialMarkdownReport.length,
      hasPastMedical: caseEvaluation.initialMarkdownReport.includes(
        'Past Medical Expenses'
      ),
      hasFutureMedical: caseEvaluation.initialMarkdownReport.includes(
        'Future Medical Expenses'
      ),
      sections: caseEvaluation.initialMarkdownReport.split('#').length - 1
    })

    // Check if this lengthy section might need continuation
    if (sectionsNeedingExtension.includes('revised_report')) {
      return await generateReportSection(prompt, 'revised_report', user)
    } else {
      // Regular generation if continuation not needed
      const response = await createCompletion({
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
        json: false,
        teamId: user?.teamId,
        purpose: 'case-eval',
        activity: 'revised-report-generation'
      })

      // Log the revised report content for debugging
      logger.info('Revised report content:', {
        length: response.length,
        hasPastMedical: response.includes('Past Medical Expenses'),
        hasFutureMedical: response.includes('Future Medical Expenses'),
        sections: response.split('#').length - 1
      })

      logger.end('generateRevisedReport')
      return response
    }
  } catch (error: any) {
    logger.error('generateRevisedReport', error)
    throw new Error(`Failed to generate revised report: ${error.message}`)
  }
}

function filterAttorneyInsightsForSection(
  attorneyInsights: any,
  sectionId: string
): any {
  if (!attorneyInsights || !attorneyInsights.rawFormData) {
    return attorneyInsights
  }

  // Map from section identifier to user-facing section name
  const sectionToNameMapping: Record<string, string[]> = {
    case_overview: ['Case Overview'],
    liability_assessment: ['Liability Assessment'],
    damages_calculation: ['Damages Calculation'],
    risk_analysis: ['Risk Analysis', 'Case strengths', 'Case weaknesses'],
    litigation_strategy: ['Litigation Strategy', 'Trial Strategy'],
    attorney_insights: [
      'Attorney Insights & Local Considerations',
      'Jurisdiction-Specific Considerations'
    ],
    final_recommendations: ['Final Recommendations'],
    // Add defense perspective sections
    analyze_defense_perspective: ['Defense Perspective', 'Case weaknesses'],
    generate_revised_report: ['Revised Report', 'Defense Perspective']
  }

  // Get the user-facing section names for this section ID
  const sectionNames = sectionToNameMapping[sectionId] || []

  // Find all fields that are relevant to any of these section names
  const relevantFields = Object.entries(attorneyInsightsSectionMapping)
    .filter(([_, sections]) =>
      (sections as string[]).some((s) => sectionNames.includes(s))
    )
    .map(([field]) => field)

  // Filter the raw data to include only the relevant fields
  const filteredRawData: Partial<AttorneyInsightsFormData> = {}
  relevantFields.forEach((field) => {
    const key = field as keyof AttorneyInsightsFormData
    if (attorneyInsights.rawFormData && attorneyInsights.rawFormData[key]) {
      filteredRawData[key] = attorneyInsights.rawFormData[key]
    }
  })

  // Create a filtered version of the structured insights
  const filteredInsights = { ...attorneyInsights }

  // Add the filtered raw data
  filteredInsights.relevantRawData = filteredRawData

  return filteredInsights
}

/**
 * Generate case evaluation sections in parallel batches
 * @param caseEvaluation - Case evaluation data structure
 * @param sectionPrompts - Prompts for each section
 * @returns Object mapping section IDs to their content
 */
async function generateReportSectionsInParallel(
  caseEvaluation: Omit<
    CaseEvaluation,
    'initialMarkdownReport' | 'defensePerspective' | 'revisedMarkdownReport'
  >,
  sectionPrompts: Record<string, string>
): Promise<Record<string, string>> {
  logger.start('generateReportSectionsInParallel')

  try {
    // Create section objects for batch processing
    const sectionObjects = Object.entries(sectionPrompts).map(
      ([id, prompt]) => ({
        id,
        prompt,
        title: id
          .split('_')
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' '),
        contextNeeded: [] // No additional context needed
      })
    )

    // Generate sections in parallel batches
    const sectionContents = await generateSectionsInBatches({
      contextData: {}, // Not needed as context is already in prompts
      prompts: sectionObjects,
      maxContinuations: MAX_CONTINUATIONS,
      sectionsNeedingExtension,
      documentList: [] // TODO: Pass the document list if needed
    })

    logger.end('generateReportSectionsInParallel', {
      generatedSections: Object.keys(sectionContents).length
    })

    return sectionContents
  } catch (error: any) {
    logger.error('generateReportSectionsInParallel', error)
    throw new Error(
      `Failed to generate report sections in parallel: ${error.message}`
    )
  }
}

/**
 * Generate a single case evaluation section with continuation support
 * @param prompt - The prompt for generating this section
 * @param sectionId - The ID of the section
 * @returns The generated section content
 */
async function generateReportSection(
  prompt: string,
  sectionId: string,
  user?: AuthUser
): Promise<string> {
  logger.start('generateReportSection', { sectionId })

  try {
    // Generate the initial content
    let content = await createCompletion({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
      json: false,
      teamId: user?.teamId,
      purpose: 'case-eval',
      activity: 'report-section-generation'
    })

    // Check if the section needs extension
    if (sectionNeedsExtension(sectionId, content, sectionsNeedingExtension)) {
      logger.info(
        `Section ${sectionId} needs extension, current length: ${content.length} characters`
      )

      // Start extension process
      content = await extendSectionContent({
        section: { id: sectionId, prompt, title: sectionId, contextNeeded: [] },
        context: {},
        currentContent: content,
        continuationCount: 0,
        maxContinuations: MAX_CONTINUATIONS,
        sectionsNeedingExtension
      })
    }

    logger.end('generateReportSection', {
      sectionId,
      contentLength: content.length
    })

    return content
  } catch (error: any) {
    logger.error(`generateReportSection:${sectionId}`, error)
    throw new Error(`Failed to generate section ${sectionId}: ${error.message}`)
  }
}
