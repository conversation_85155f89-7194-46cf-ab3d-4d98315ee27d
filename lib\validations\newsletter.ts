import * as z from 'zod'

export const newsletterPatchSchema = z.object({
  title: z.string().min(3).max(128),
  content: z.any()
})

export const newsletterPostSchema = z.object({
  title: z.string().min(3).max(128),
  theme: z.string(),
  content: z.string(),
  targetAudience: z.string(),
  keyLegalUpdates: z.string(),
  editorialTone: z.string()
})

export const dbSearchSchema = z.object({
  query: z.string(),
  filter: z
    .object({
      court: z.string().optional(),
      year: z.string().optional(),
      documentRecordsId: z
        .object({
          $nin: z.array(z.number()).optional()
        })
        .optional()
    })
    .optional()
})
