import Link from 'next/link'
import { Newsletter } from '@prisma/client'

import { formatDate } from '@/lib/utils'
import { Skeleton } from '@/components/ui/skeleton'
import { NewsletterOperations } from '@/components/elements/newsletter/newsletter-operations'

interface NewsletterItemProps {
  newsletter: Pick<
    Newsletter,
    'id' | 'title' | 'published' | 'slug' | 'createdAt'
  >
}

export function NewsletterItem({ newsletter }: NewsletterItemProps) {
  return (
    <div className="flex items-center justify-between p-4">
      <div className="grid gap-1">
        <Link
          href={`/admin/newsletter/${newsletter.id}`}
          className="font-semibold hover:underline"
        >
          {newsletter.title}
        </Link>
        <div>
          <p className="text-sm text-muted-foreground">
            {formatDate(newsletter.createdAt?.toDateString())}
          </p>
        </div>
      </div>
      <NewsletterOperations
        newsletter={{ id: newsletter.id, title: newsletter.title }}
      />
    </div>
  )
}

NewsletterItem.Skeleton = function NewsletterItemSkeleton() {
  return (
    <div className="p-4">
      <div className="space-y-3">
        <Skeleton className="h-5 w-2/5" />
        <Skeleton className="h-4 w-4/5" />
      </div>
    </div>
  )
}
