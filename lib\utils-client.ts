import { toast } from '@/components/ui/use-toast'
import type EditorJS from '@editorjs/editorjs'
import { convertToFormattedText } from './utils'

export async function copyText(editor: EditorJS) {
  try {
    const data = await editor.save()
    const formattedText = convertToFormattedText(data)
    const blob = new Blob([formattedText], { type: 'text/html' })
    const clipboardItem = new ClipboardItem({ 'text/html': blob })
    await navigator.clipboard.write([clipboardItem])

    return toast({
      title: 'Copied to clipboard',
      description: 'You can now paste the content anywhere'
    })
  } catch (e) {
    console.error(e)
    return toast({
      title: 'Error',
      description: 'Something went wrong while copying to clipboard'
    })
  }
}

export function copyStringToClipboard(str: string) {
  const el = document.createElement('textarea')
  el.value = str
  document.body.appendChild(el)
  el.select()
  document.execCommand('copy')
  document.body.removeChild(el)

  return toast({
    title: 'Copied to clipboard',
    description: 'You can now paste the link anywhere'
  })
}

export function splitByLine(str?: string) {
  if (!str || !str.trim()) return []
  return str
    .split('\n')
    .map((s) => s.trim().replace(/,$/, '').trim())
    .filter((s) => s.length > 0)
}
