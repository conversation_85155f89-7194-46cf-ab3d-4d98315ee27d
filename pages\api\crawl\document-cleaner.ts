import type { NextApiRequest, NextApiResponse } from 'next'
import * as cheerio from 'cheerio'
import { db } from '@/lib/db'
import { env } from '@/env.mjs'
import { textToHTML } from '@/lib/utils'
import { DocumentRecords, Region } from '@prisma/client'
import axios from 'axios'
import pdf from 'pdf-parse'
import { s3UrlPut } from '@/lib/services/s3-service'
import { sendMsgOnSlack } from '@/lib/services/slack-service'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (env.NODE_ENV !== 'development') {
      throw new Error('Unauthorized')
    }

    const CHUNK_SIZE = 50
    const BATCH_SIZE = 1000
    let lastId = req.query.lastId ? parseInt(req.query.lastId as string) : 0
    let endId = req.query.endId ? parseInt(req.query.endId as string) : 3000000

    const teams = await db.team.findMany({
      select: {
        id: true
      }
    })

    try {
      for (let j = 0; j < 3000000; j += BATCH_SIZE) {
        const records = await db.documentRecords.findMany({
          select: {
            id: true,
            source: true,
            html: true
          },
          where: {
            id: {
              gt: lastId,
              lt: endId
            },
            source: {
              notIn: teams.map((team) => team.id)
            },
            region: Region.US
          },
          take: BATCH_SIZE,
          orderBy: {
            id: 'asc'
          }
        })

        if (records.length === 0) {
          break
        }

        lastId = records[records.length - 1].id
        console.log('Total records: ', records.length, ' in batch: ', j)
        const size = records.length

        for (let i = 0; i < records.length; i += CHUNK_SIZE) {
          const startTimestamp = new Date().getTime()
          console.log('Processing chunk: ', i, ' to ', i + CHUNK_SIZE, ' of ', size) // prettier-ignore
          const chunk = records.slice(i, i + CHUNK_SIZE)
          const promises = chunk.map((record) =>
            updateCleanedHtmlAndContent(record)
          )
          await Promise.allSettled(promises)
          const endTimestamp = new Date().getTime()
          const duration = (endTimestamp - startTimestamp) / 1000
          const chunkBatchesRemaining = Math.ceil((size - i) / CHUNK_SIZE)
          const estimatedTimeRemainingMins = (
            (chunkBatchesRemaining * duration) /
            60
          ).toFixed(2)
          const estimatedTimeRemainingHrs = (
            Number(estimatedTimeRemainingMins) / 60
          ).toFixed(2)
          console.info(`Chunk processed in ${duration} seconds. Estimated time remaining: ${estimatedTimeRemainingMins} mins or (${estimatedTimeRemainingHrs} hrs) of batch: ${j} to ${j + BATCH_SIZE}`) // prettier-ignore
        }
      }
    } catch (error: any) {
      console.log('error: ', error.message)
    }
    console.log('\n\n<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>\n')
    console.log('FINISHED')
    console.log('\n<<<<<<<<<<<<<<<<<<<<<>>>>>>>>>>>>>>>>>>>>>>>>>>\n')

    res.status(200).json({ message: 'Data saved successfully!' })
  } catch (error) {
    res.status(500).json({ error: 'Failed to index.', errorcontent: error })
  }
}

async function updateCleanedHtmlAndContent(
  record: Pick<DocumentRecords, 'id' | 'source' | 'html'>
) {
  try {
    const $ = cheerio.load(record.html)

    // Existing HTML manipulations
    const tables = $('table')
    tables.each((_, table) => {
      const titleCell = $(table).find('td.headingBIG')
      if ($(table).text().trim() === '') {
        $(table).remove()
      } else if (
        titleCell.length &&
        titleCell.text().trim() === 'Case Tracker'
      ) {
        const parentTable = titleCell.closest('table')
        if (parentTable.length) {
          parentTable.remove()
        }
      }
    })

    $('td').addClass('p-3')

    $('b:contains("DISCLAIMER")').each(function () {
      const parentFont = $(this).closest('font')
      if (parentFont.length) {
        const text = parentFont.text()
        const modifiedText = text.replace(/Taxindiaonline/g, 'SmartCounsel AI')
        parentFont.text(modifiedText)
      }
    })

    $('a').each(function () {
      const trimmedText = $(this).text().trim().toLowerCase()
      if (
        ['download pdf', 'print this document', 'read more'].includes(
          trimmedText
        )
      ) {
        $(this).remove()
      }
    })

    $('.hidden').removeClass('hidden')
    $('p').removeAttr('style')
    $('table').removeAttr('style')
    $('strong').removeAttr('style').addClass('font-bold')
    $('blockquote')
      .removeAttr('style')
      .addClass('border-l-4 border-gray-300 pl-4 italic my-4')
    $('font').removeAttr('color').removeAttr('face')

    // Regular expressions to match specific patterns
    const regexPattern1 = /\d{4} LLR \d{3}/g
    const regexPattern2 = /\d{4}-TIOL-[^\s]*/g

    // Remove the "size" attribute from all elements
    $('*').each(function () {
      if ($(this).attr('size')) {
        $(this).removeAttr('size')
      }
    })

    // Replace matched patterns in text nodes
    $('*')
      .contents()
      .each(function () {
        if (this.type === 'text') {
          let text = $(this).text()
          let modifiedText = text.replace(/Taxindiaonline/g, 'SmartCounsel AI')
          modifiedText = modifiedText.replace(/Justia/g, 'SmartCounsel AI')
          modifiedText = modifiedText.replace(regexPattern1, '')
          modifiedText = modifiedText.replace(regexPattern2, '')
          $(this).replaceWith(modifiedText)
        }
      })

    // Process the PDF iframe if it exists
    const iframe = $('iframe.pdf-iframe')

    if (iframe.length > 0) {
      const pdfUrl = iframe.attr('src')
      const directPdfUrl = extractAndFormatLink(pdfUrl)

      if (directPdfUrl) {
        console.log(`Processing PDF for record ${record.id}`)

        // Download the PDF and extract text
        const pdfTextPromise = downloadAndExtractPdfText(directPdfUrl)
        const pdfUploadPromise = uploadPdfToS3(record, directPdfUrl)

        const [pdfText, pdfUpload] = await Promise.all([
          pdfTextPromise,
          pdfUploadPromise
        ])

        // Append the PDF text to the existing content
        const wrapper = $('.wrapper.jcard')
        if (wrapper.length > 0) {
          wrapper.append(textToHTML(pdfText))
        } else {
          $('body').append(textToHTML(pdfText))
        }
      } else {
        console.log('Direct PDF URL not found for record', record.id)
      }
    }

    // Assign the modified HTML and content
    const modifiedHtml = $.html()
    const content = $('body').text()

    await db.documentRecords.update({
      where: {
        id: record.id
      },
      data: {
        html: modifiedHtml,
        content: content,
        html_cleaned: true
      }
    })
    console.log('Updated html and content: ', record.id)
  } catch (error: any) {
    console.log('Error updating html and content: ', record.id, error.message)
    await sendMsgOnSlack({
      textMsg: `Error updating html and content: ${record.id}, ${error.message}`
    })
  }
}

function extractAndFormatLink(iframeSrc?: string): string | null {
  if (iframeSrc) {
    // Add 'https:' at the start if needed
    let formattedLink = iframeSrc.startsWith('//')
      ? 'https:' + iframeSrc
      : iframeSrc

    // Parse the URL and extract the 'file' parameter
    const urlObj = new URL(formattedLink)
    const fileParam = urlObj.searchParams.get('file')

    if (fileParam) {
      // Build the direct PDF URL
      const directPdfUrl = `${urlObj.origin}${fileParam}`
      return directPdfUrl
    }
    return null
  }

  // Return null if no iframeSrc provided
  return null
}

async function downloadAndExtractPdfText(
  directPdfUrl: string
): Promise<string> {
  try {
    // Download the PDF
    const response = await axios.get(directPdfUrl, {
      responseType: 'arraybuffer'
    })
    const buffer = Buffer.from(response.data)

    // Optional: check if the file starts with %PDF-
    if (buffer.toString('utf8', 0, 5) !== '%PDF-') {
      throw new Error('Invalid PDF structure.')
    }

    // Extract text from PDF buffer
    const pdfData = await pdf(buffer)

    return pdfData.text
  } catch (error: any) {
    console.log('Error downloading and extracting PDF text:', error.message)
    await sendMsgOnSlack({
      textMsg: `Error downloading and extracting PDF text: ${error.message}`
    })
    return ''
  }
}

async function uploadPdfToS3(
  record: Pick<DocumentRecords, 'id' | 'source' | 'html'>,
  directPdfUrl: string
) {
  try {
    // Download the PDF
    const response = await axios.get(directPdfUrl, {
      responseType: 'arraybuffer'
    })
    const buffer = Buffer.from(response.data)

    const s3Key = `${record.source}/document-${record.id}.pdf`

    // Get presigned URL for uploading the PDF
    const uploadUrl = await s3UrlPut(s3Key, 'pdf')

    // Upload the file to S3
    await axios.put(uploadUrl, buffer, {
      headers: {
        'Content-Type': 'application/pdf'
      }
    })

    const existingDocument = await db.documentResource.findFirst({
      where: { documentRecordId: record.id }
    })

    if (existingDocument) {
      await db.documentResource.update({
        where: { id: existingDocument.id },
        data: {
          s3Key: s3Key,
          fileUrl: uploadUrl
        }
      })
    } else {
      await db.documentResource.create({
        data: {
          s3Key: s3Key,
          fileUrl: uploadUrl,
          documentRecordId: record.id
        }
      })
    }

    console.log(`PDF processed and uploaded for record ${record.id}`)
  } catch (error: any) {
    console.log('Error uploading PDF to S3:', error.message)
    await sendMsgOnSlack({
      textMsg: `Error uploading PDF to S3: ${error.message}`
    })
  }
}
