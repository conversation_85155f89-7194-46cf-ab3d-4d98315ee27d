import { Metadata } from 'next'
import Link from 'next/link'

import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import { Icons } from '@/components/elements/icons'
import { UserResetPasswordForm } from '@/components/elements/forms/user-reset-password-form'
import { db } from '@/lib/db'

export const metadata: Metadata = {
  title: 'Reset Password',
  description: 'Reset Password page for Smart Counsel'
}

interface ResetPasswordProps {
  params: { resetToken: string }
}

export default async function ResetPasswordPage({
  params
}: ResetPasswordProps) {
  let status = 1
  const validateToken = await db.resetPasswordToken.findFirst({
    where: {
      token: params.resetToken
    }
  })

  if (!validateToken) {
    status = 0
  }

  if (
    validateToken &&
    (validateToken?.used || new Date(validateToken.expires) < new Date())
  ) {
    status = -1
  }

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <Link
        href="/login"
        className={cn(
          buttonVariants({ variant: 'ghost' }),
          'absolute left-4 top-4 md:left-8 md:top-8'
        )}
      >
        <>
          <Icons.chevronLeft className="mr-2 h-4 w-4" />
          Back
        </>
      </Link>
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <Icons.logo className="mx-auto h-22" />
          {status === 1 ? (
            <>
              <h1 className="text-2xl font-semibold tracking-tight">
                Reset Password
              </h1>
              <p className="text-sm text-muted-foreground">Enter</p>
            </>
          ) : status === 0 ? (
            <>
              <h1 className="text-2xl font-semibold tracking-tight">
                Invalid Link
              </h1>
              <Link
                href="/forgot"
                className={cn(
                  buttonVariants({ variant: 'ghost' }),
                  'absolute left-4 top-4 md:left-8 md:top-8'
                )}
              >
                <>
                  <Icons.chevronLeft className="mr-2 h-4 w-4" />
                  Back
                </>
              </Link>
            </>
          ) : (
            <>
              <h1 className="text-2xl font-semibold tracking-tight">
                Link expired, please try again
              </h1>
              <Link
                href="/forgot"
                className={cn(
                  buttonVariants({ variant: 'ghost' }),
                  'absolute left-4 top-4 md:left-8 md:top-8'
                )}
              >
                <>
                  <Icons.chevronLeft className="mr-2 h-4 w-4" />
                  Back
                </>
              </Link>
            </>
          )}
        </div>
        {validateToken && (
          <UserResetPasswordForm userId={validateToken.userId} />
        )}
        <p className="px-8 text-center text-sm text-muted-foreground">
          <Link
            href="/register"
            className="hover:text-brand underline underline-offset-4"
          >
            Don&apos;t have an account? Sign Up
          </Link>
        </p>
      </div>
    </div>
  )
}
