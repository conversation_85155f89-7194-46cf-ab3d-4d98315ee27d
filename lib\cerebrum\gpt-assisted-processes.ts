'use server'

import { Region, ResearchType } from '@prisma/client'
import {
  cleanQueryAssessmentResponse,
  cleanQueryClarityCheckResponse,
  dummyAssessmentResponse,
  fetchDocumentMetadata,
  formatVercelMessages
} from '../retriever-utils'
import { createCompletion } from '../services/openai-service'
import {
  documentRelevancePromptTemplateProvider,
  GENERAL_PROMPT_TYPES,
  generalPromptTemplateProvider,
  REGIONAL_PROMPT_TYPES,
  regionalPromptTemplateProvider,
  templateEngineer
} from './prompt-template'
import { Document } from 'langchain/document'
import { developer } from '../utils'
import {
  type VercelChatMessage,
  QueryClarityCheckResponse,
  GPTModel,
  QueryAssessmentResponse,
  ResearchStoreContent
} from '@/types'

interface AssessQuestionParams {
  messages: VercelChatMessage[]
  question: string
  region: Region
  researchType: ResearchType
  namespace?: string
  model?: GPTModel
  dummy?: boolean
  metadata?: Pick<ResearchStoreContent, 'court' | 'year'>
}

export async function assessPrivateQuestionQuality({
  messages,
  question,
  region,
  namespace,
  metadata = { court: ['any'], year: ['any'] },
  model = GPTModel.GPT4Turbo,
  dummy = false
}: AssessQuestionParams): Promise<QueryAssessmentResponse> {
  if (dummy && namespace) {
    return dummyAssessmentResponse({ question, namespace })
  }
  const chatHistory = formatVercelMessages(messages, true)

  const prompt = templateEngineer({
    template: generalPromptTemplateProvider({
      type: GENERAL_PROMPT_TYPES.ASSESS
    }),
    replacements: {
      chatHistory,
      question
    }
  })

  const rawAssessment = await createCompletion({
    model: model,
    messages: [{ role: 'system', content: prompt }],
    json: true
  })

  const assessment = cleanQueryAssessmentResponse({
    rawAssessment: JSON.parse(rawAssessment),
    question,
    region,
    namespace
  })

  developer.log(['assessment', region, assessment])

  return assessment
}

export async function assessQuestionQuality({
  messages,
  question,
  region,
  researchType,
  namespace,
  metadata = { court: ['any'], year: ['any'] },
  model = GPTModel.GPT4Turbo,
  dummy = false
}: AssessQuestionParams): Promise<QueryAssessmentResponse> {
  if (dummy && namespace) {
    return dummyAssessmentResponse({ question, namespace })
  }
  const chatHistory = formatVercelMessages(messages, true)

  const prompt = templateEngineer({
    template: regionalPromptTemplateProvider({
      promptType: REGIONAL_PROMPT_TYPES.ASSESS,
      researchType: researchType,
      region
    }),
    replacements: {
      chatHistory,
      question,
      year: metadata.year.join(', '),
      court: metadata.court.join(', ')
    }
  })

  const rawAssessment = await createCompletion({
    model: model,
    messages: [{ role: 'system', content: prompt }],
    json: true
  })

  const assessment = cleanQueryAssessmentResponse({
    rawAssessment: JSON.parse(rawAssessment),
    question,
    region,
    namespace
  })

  developer.log(['assessment', region, assessment])

  return assessment
}

interface RefineQuestionParams {
  messages: VercelChatMessage[]
  question: string
  promptType: 'general' | 'regional'
  region: Region
  researchType: ResearchType
  namespace?: string
  model?: GPTModel
  dummy?: boolean
  metadata?: Pick<ResearchStoreContent, 'court' | 'year'>
}

export async function refineQuestion({
  messages,
  question,
  promptType,
  region,
  researchType,
  namespace,
  metadata = { court: ['any'], year: ['any'] },
  model = GPTModel.GPT4Turbo
}: RefineQuestionParams): Promise<QueryClarityCheckResponse> {
  const chatHistory = formatVercelMessages(messages, true)

  const prompt = templateEngineer({
    template:
      promptType === 'general'
        ? generalPromptTemplateProvider({
            type: GENERAL_PROMPT_TYPES.REFINE
          })
        : regionalPromptTemplateProvider({
            promptType: REGIONAL_PROMPT_TYPES.REFINE,
            researchType: researchType,
            region
          }),
    replacements: {
      chatHistory,
      question,
      year: metadata.year.join(', '),
      court: metadata.court.join(', ')
    }
  })

  const rawAssessment = await createCompletion({
    model: model,
    messages: [{ role: 'system', content: prompt }],
    json: true
  })

  let assessment = cleanQueryClarityCheckResponse({
    rawAssessment: JSON.parse(rawAssessment),
    question
  })

  developer.log(['refine', region, promptType, assessment])

  return assessment
}

export async function assessDocumentRelevance({
  questions,
  documents,
  model = GPTModel.GPT4o,
  takeMin
}: {
  questions: string[]
  documents: Document[]
  model?: GPTModel
  takeMin?: number
}) {
  const documentMetadata = await fetchDocumentMetadata(
    documents.map((doc) => doc.metadata.documentRecordsId)
  )

  const prompt = documentRelevancePromptTemplateProvider({
    questions,
    documentMetadata,
    documents
  })

  const assessment = JSON.parse(
    await createCompletion({
      model: model,
      messages: [{ role: 'system', content: prompt }],
      json: true
    })
  ) as
    | {
        very_relevant_documents: number[]
        less_relevant_documents: number[]
        irrelevant_documents: number[]
      }
    | undefined

  developer.log(['Assessing document relevance:', documents.length, assessment])

  if (
    // if very_relevant_document > 4, return documents after filtering those indexes
    assessment?.very_relevant_documents &&
    assessment.very_relevant_documents.length >= (takeMin || 4)
  ) {
    const filteredDocuments = documents.filter((_, index) =>
      assessment.very_relevant_documents.includes(index)
    )

    developer.log(['Filtering very relevant documents:', assessment, [' from ', documents.length, ' to ', filteredDocuments.length]]) // prettier-ignore

    return filteredDocuments
  } else if (
    // if less_relevant_document > 0, return documents after filtering those indexes
    assessment?.less_relevant_documents &&
    assessment.less_relevant_documents.length > 0
  ) {
    const filteredDocuments = documents.filter(
      (_, index) =>
        assessment.very_relevant_documents.includes(index) ||
        assessment.less_relevant_documents.includes(index)
    )

    developer.log(['Filtering less relevant documents:', assessment, [' from ', documents.length, ' to ', filteredDocuments.length]]) // prettier-ignore
    return filteredDocuments
  }

  console.log('No relevant documents found, dumping all documents')

  return documents
}
