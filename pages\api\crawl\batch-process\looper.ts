import type { NextApiRequest, NextApiResponse } from 'next'
import { request } from 'undici'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const source = req.query.source as string
    let lastId = 68580
    let size = 1
    while (size && size > 0) {
      const response = await request(
        `http://localhost:3011/api/crawl/batch-process/document-metadata?source=${source}&lastId=${lastId}`,
        {
          headersTimeout: 1000 * 60 * 60
        }
      )
      if (!response.statusCode || response.statusCode >= 400) {
        throw new Error(
          `Server responded with status code ${response.statusCode}`
        )
      }

      const data = (await response.body.json()) as any
      lastId = data.lastId
      size = data.size
    }

    res.status(200).json({ message: 'Batch process completed.' })
  } catch (error) {
    console.error(error)
    res.status(500).json({ error: 'Failed to index.' })
  }
}
