import { db } from '@/lib/db'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import Link from 'next/link'
import { DocumentShowcasePanel } from '@/components/elements/document/document-showcase'
import { buttonVariants } from '@/components/ui/button'

export const metadata = {
  title: 'Document View Page'
}

interface DocumentViewPagePage {
  params: { source: string }
  searchParams: { [key: string]: string | string[] | undefined }
}

export default async function DocumentViewPagePage({
  params,
  searchParams
}: DocumentViewPagePage) {
  const skip = parseInt(searchParams.skip as string) || 0
  const documents = await db.documentRecords.findMany({
    select: {
      id: true,
      source: true,
      ref: true,
      title: true,
      createdAt: true,
      updatedAt: true
    },
    where: {
      source: params.source
    },
    take: 100,
    skip
  })

  return (
    <DashboardShell>
      <DashboardHeader heading={'Reference Document'} text={params.source}>
        <div className="flex gap-2">
          {skip > 0 && (
            <Link
              className={buttonVariants()}
              href={`/admin/documents/${params.source}?skip=${skip - 100}`}
            >
              Previous 100
            </Link>
          )}

          <Link
            className={buttonVariants()}
            href={`/admin/documents/${params.source}?skip=${skip + 100}`}
          >
            Next 100
          </Link>
        </div>
      </DashboardHeader>
      <DocumentShowcasePanel documents={documents} skipCount={skip} />
    </DashboardShell>
  )
}
