'use server'

import { db } from '@/lib/db'

export async function fetchDocuments(binderId: string) {
  const dataSets = await db.dataset.findMany({
    where: {
      OR: [
        {
          createdBy: binderId
        },
        {
          binderId: binderId
        }
      ]
    },
    select: {
      DocumentRecordDatasetMap: {
        select: {
          DocumentRecords: {
            select: {
              id: true,
              title: true,
              indexed: true
            }
          }
        }
      }
    }
  })

  const documents = dataSets
    .map((dataSet) =>
      dataSet.DocumentRecordDatasetMap.map((doc) => doc.DocumentRecords)
    )
    .flat()

  const allDocuments = documents
    .map((doc) => ({
      id: doc.id,
      title: doc.title,
      indexed: doc.indexed
    }))
    .sort((a, b) => a.title.localeCompare(b.title))

  return allDocuments
}
