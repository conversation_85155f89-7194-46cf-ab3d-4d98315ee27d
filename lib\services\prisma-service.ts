import { PrismaClient as PostgreSQLClient } from '@/generated/postgres'

const pgPrismaLogged = new PostgreSQLClient({
  log: [
    {
      emit: 'event',
      level: 'query'
    },
    {
      emit: 'stdout',
      level: 'error'
    },
    {
      emit: 'stdout',
      level: 'info'
    },
    {
      emit: 'stdout',
      level: 'warn'
    }
  ]
})

pgPrismaLogged.$on('query', (e: any) => {
  let queryString = e.query
  JSON.parse(e.params).forEach((param: string, index: number) => {
    queryString = queryString.replace(
      `$${index + 1}`,
      typeof param === 'string' ? `'${param}'` : param
    )
  })

  console.log(queryString)
})

export default pgPrismaLogged
