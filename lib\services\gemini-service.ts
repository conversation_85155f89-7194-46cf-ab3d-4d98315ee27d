import { env } from '@/env.mjs'
import { GeminiChatMessage, GeminiModel, VercelChatMessage } from '@/types'
import { trackTokenUsage } from './llm-token-usage'
import { GoogleGenAI } from '@google/genai'
import { LLMProvider } from '@prisma/client'

export function transformChatMessages(
  chatMessagesFiltered: VercelChatMessage[]
): any[] {
  return chatMessagesFiltered.map(({ role, content }) => ({
    role: role === 'system' ? 'model' : 'user',
    parts: [{ text: content }]
  }))
}

export async function* createCompletionStream({
  modelName = 'gemini-2.0-flash',
  history = [],
  message,
  systemInstruction
}: {
  modelName?: string
  history?: GeminiChatMessage[]
  message: string
  systemInstruction: string
}): AsyncIterableIterator<string> {
  const ai = new GoogleGenAI({ apiKey: env.GEMINI_API_KEY })

  // Create the contents array with history and current message
  const contents: GeminiChatMessage[] = [
    ...history,
    { role: 'model', parts: [{ text: systemInstruction }] },
    { role: 'user', parts: [{ text: message }] }
  ]

  // Generate streaming content
  const responseStream = await ai.models.generateContentStream({
    model: modelName,
    contents
  })

  // Yield each chunk as it arrives
  for await (const chunk of responseStream) {
    yield chunk.text || ''
  }
}

export async function createGeminiCompletion({
  modelName = GeminiModel.Gemini25Flash,
  history = [],
  message,
  systemInstruction,
  config,
  teamId,
  purpose = 'completion',
  activity = 'chat'
}: {
  modelName?: GeminiModel
  history?: GeminiChatMessage[]
  message: string
  systemInstruction: string
  config?: Record<string, any>
  teamId?: string
  purpose?: string
  activity?: string
}): Promise<string | object> {
  const ai = new GoogleGenAI({ apiKey: env.GEMINI_API_KEY })

  // Create the contents array with history and current message
  const contents = [
    ...history,
    { role: 'model', parts: [{ text: systemInstruction }] },
    { role: 'user', parts: [{ text: message }] }
  ]

  let retryCount = 0
  const maxRetries = 3

  while (retryCount <= maxRetries) {
    try {
      // Generate non-streaming content
      const response = await ai.models.generateContent({
        model: modelName,
        contents,
        config
      })

      if (!response.text) {
        throw new Error('No response text found')
      }

      // Track token usage if teamId is provided and usage metadata exists
      if (teamId && response.usageMetadata) {
        await trackTokenUsage({
          teamId,
          provider: LLMProvider.GEMINI,
          model: response.modelVersion || modelName,
          purpose,
          activity,
          requestId: response.responseId,
          modelVersion: response.modelVersion,
          usage: response.usageMetadata
        }).catch((error) => {
          console.error('Failed to track Gemini token usage:', error)
        })
      }

      if (
        (config?.responseMimeType === 'application/json' ||
          config?.response_mime_type === 'application/json') &&
        typeof response.text === 'string'
      ) {
        const clearString = cleanGeminiResponse(response.text)
        try {
          return JSON.parse(clearString)
        } catch (error) {
          console.error('Failed to parse Gemini response as JSON:', error)
          throw new Error('Invalid JSON response from Gemini')
        }
      }

      return response.text
    } catch (error: any) {
      const isRateLimit =
        error.message?.includes('rate limit') ||
        error.message?.includes('429') ||
        error.status === 429 ||
        error.code === 'RATE_LIMIT_EXCEEDED'

      if (isRateLimit && retryCount < maxRetries) {
        // Exponential backoff: 60s, 120s, 240s
        const waitTime = 60000 * Math.pow(2, retryCount)
        console.log(
          `Rate limit hit, waiting ${waitTime / 1000} seconds before retry ${retryCount + 1}/${maxRetries}`
        )
        await new Promise((resolve) => setTimeout(resolve, waitTime))
        retryCount++
        continue
      }

      // If it's not a rate limit error or we've exhausted retries, throw the error
      throw error
    }
  }

  // This should never be reached, but TypeScript requires it
  throw new Error('Maximum retries exceeded')
}

function cleanGeminiResponse(response: string): string {
  // Remove markdown code block formatting
  return response
    .replace(/^```json\s*/i, '')
    .replace(/^```\s*/i, '')
    .replace(/\s*```$/i, '')
    .trim()
}
