'use client'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { toast } from '@/components/ui/use-toast'

const attorneyInsightsFormShema = z.object({
  courtDynamics: z.string().optional(),
  defenseBehavior: z.string().optional(),
  clientCircumstances: z.string().optional(),
  pretrialStrategy: z.string().optional(),
  trialReadiness: z.string().optional(),
  weakSpots: z.string().optional(),
  comparableCases: z.string().optional(),
  gutFeel: z.string().optional(),
  miscObservations: z.string().optional()
})

export type AttorneyInsightsFormData = z.infer<typeof attorneyInsightsFormShema>

interface AttorneyInsightsFormProps {
  caseId: string
  defaultValues?: Partial<AttorneyInsightsFormData> | null
  setValues: (values: Partial<AttorneyInsightsFormData>) => void
}

export function AttorneyInsightsForm({
  caseId,
  defaultValues,
  setValues
}: AttorneyInsightsFormProps) {
  const form = useForm<AttorneyInsightsFormData>({
    resolver: zodResolver(attorneyInsightsFormShema),
    defaultValues: defaultValues || {
      courtDynamics: '',
      defenseBehavior: '',
      clientCircumstances: '',
      pretrialStrategy: '',
      trialReadiness: '',
      weakSpots: '',
      comparableCases: '',
      gutFeel: '',
      miscObservations: ''
    }
  })

  function onSubmit(data: AttorneyInsightsFormData) {
    setValues(data)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Attorney Insights & Strategy</CardTitle>
        <CardDescription>
          Share your insights and strategy for this case for case evaluation.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className=" space-y-6">
            <FormField
              control={form.control}
              name="courtDynamics"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Local Court/Community Dynamics</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., Judge Smith usually orders mediation early. Our jurisdiction's jurors are conservative on non-economic damages."
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Are there any local court tendencies, judge preferences, or
                    community biases that might affect pre-trial motions,
                    settlement discussions, or trial outcomes?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="defenseBehavior"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Defendant & Defense Counsel Behavior</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., Defense counsel has a reputation for low initial offers, but often settles before trial. If forced to trial, they typically focus on undermining medical expert testimony."
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    What do you know about the defendant&apos;s or defense
                    counsel&apos;s negotiation style and potential trial
                    tactics?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="clientCircumstances"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Client&apos;s Circumstances & Preferences
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., Client needs a faster resolution due to medical bills. Comfortable testifying but very anxious about cross-examination. Prefers settlement but open to trial if the offer is too low."
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Highlight your client&apos;s personal factors affecting
                    pre-trial settlements or decision to go to trial.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pretrialStrategy"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pre-Trial Strategy Considerations</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., Plan to file a Motion in Limine to exclude prior medical history. Discovery requests will focus on defendant's repeated safety violations. We'll propose mediation after initial depositions."
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    List any planned or potential pre-trial motions, discovery
                    strategies, or settlement negotiation tactics.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="trialReadiness"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Trial Readiness & Key Tactics</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., Plan to emphasize client's continuous therapy and defendant's prior DUI. Will rely on strong medical expert. Potential challenge: jury might be skeptical of soft-tissue injuries."
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    If this proceeds to trial, what are your main arguments,
                    witnesses, or evidentiary highlights?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="weakSpots"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Potential Weak Spots or Grey Areas</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., Client's timeline has a 2-week gap in care. Social media photos might contradict claims of severe mobility issues. Need to handle these carefully at trial."
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Any subtle weaknesses, contradictory evidence, or procedural
                    hurdles you anticipate?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="comparableCases"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Comparable Local Cases & Precedents</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., Last year, a case with similar injuries settled for $60k in the same county. The same defense firm was involved."
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Any specific local cases or precedents you believe strongly
                    influence settlement or trial strategy?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gutFeel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Attorney&apos;s Gut Feel & Strategy</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., An aggressive posture might push them to settle quickly. However, the client is open to trial if they can't get a fair offer."
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Share your instinctive or experience-based view on the
                    optimal route (aggressive settlement vs. full trial prep).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="miscObservations"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Miscellaneous Observations</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., Defense counsel knows our expert personally—could be a conflict. The judge is rumored to disfavor lengthy jury trials. We might shorten witness lists to keep things concise."
                      className="resize-none min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Add any additional subjective insights or specialized
                    knowledge not covered above.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
