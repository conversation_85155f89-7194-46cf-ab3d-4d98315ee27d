import { db } from '@/lib/db'
import { EmptyPlaceholder } from '@/components/elements/custom-components/empty-placeholder'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { DataTablePagination } from '@/components/elements/data-table-pagination'
import { ShowCauseOrderCreateButton } from '@/components/elements/buttons/showcause-order-create-button'
import { ShowCauseItem } from '@/components/elements/showcause-item'

export const metadata = {
  title: 'Show Cause Orders List'
}

export default async function NewslettersListPage({
  searchParams
}: {
  searchParams: { [key: string]: string | string[] }
}) {
  let pageLimiter = searchParams.limit ? Number(searchParams.limit) : 20
  const page = searchParams.page ? Number(searchParams.page) : 1

  const showCauseOrders = await db.showCauseNotice.findMany({
    orderBy: {
      createdAt: 'desc'
    },
    skip: (page - 1) * pageLimiter,
    take: pageLimiter
  })

  const totalNewsletter = await db.newsletter.count()
  const totalPage = Math.ceil(totalNewsletter / pageLimiter)

  const startIndex = totalNewsletter - (page - 1) * pageLimiter

  const showCauseOrdersWithIndex = showCauseOrders?.map(
    (newsletter, index) => ({
      index: startIndex - index,
      ...newsletter
    })
  )

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Show Cause Orders List"
        text="List of all Show Cause Orders generated."
      >
        <ShowCauseOrderCreateButton />
      </DashboardHeader>
      <div>
        {showCauseOrdersWithIndex?.length ? (
          <div className="divide-y divide-border rounded-md border bg-white dark:bg-slate-950">
            {showCauseOrdersWithIndex.map((showCauseOrder) => (
              <ShowCauseItem
                key={showCauseOrder.id}
                showCauseOrder={showCauseOrder}
              />
            ))}
            <DataTablePagination
              currentPage={page}
              totalPage={totalPage}
              searchParams={searchParams}
            />
          </div>
        ) : (
          <EmptyPlaceholder>
            <EmptyPlaceholder.Icon name="post" />
            <EmptyPlaceholder.Title>
              No Judgements generated
            </EmptyPlaceholder.Title>
            <EmptyPlaceholder.Description>
              Create a new newsletter to get started
            </EmptyPlaceholder.Description>
          </EmptyPlaceholder>
        )}
      </div>
    </DashboardShell>
  )
}
