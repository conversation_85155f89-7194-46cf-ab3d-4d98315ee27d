'use client'

import { useRouter } from 'next/navigation'
import EditorJ<PERSON> from '@editorjs/editorjs'
import { zodResolver } from '@hookform/resolvers/zod'
import { Newsletter } from '@prisma/client'
import { useForm } from 'react-hook-form'
import TextareaAutosize from 'react-textarea-autosize'
import * as z from 'zod'

import '@/styles/editor.css'
import { cn } from '@/lib/utils'
import { copyStringToClipboard } from '@/lib/utils-client'
import { buttonVariants } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'
import { Icons } from '@/components/elements/icons'
import { ShellGeneratorSkeleton } from '../layout/shell-generator-skeleton'
import { useRef, useState, useCallback, useEffect } from 'react'
import { newsletterPatchSchema } from '@/lib/validations/newsletter'
import { NewsletterGeneratorForm } from '../newsletter/newsletter-generator-form'

interface EditorProps {
  newsletter: Pick<
    Newsletter,
    'id' | 'title' | 'content' | 'slug' | 'published'
  >
}

type FormData = z.infer<typeof newsletterPatchSchema>

export function EditorNewsletter({ newsletter }: EditorProps) {
  const { setValue, register, watch, handleSubmit } = useForm<FormData>({
    resolver: zodResolver(newsletterPatchSchema)
  })
  const ref = useRef<EditorJS>()
  const router = useRouter()
  const [isSaving, setIsSaving] = useState<boolean>(false)
  const [isMounted, setIsMounted] = useState<boolean>(false)
  const [title, setTitle] = useState<string>(newsletter.title)
  const titleWatch = watch('title')

  const initializeEditor = useCallback(async () => {
    const EditorJS = (await import('@editorjs/editorjs')).default
    const Header = (await import('@editorjs/header')).default
    const Embed = (await import('@editorjs/embed')).default
    const Table = (await import('@editorjs/table')).default
    const List = (await import('@editorjs/list')).default
    const Code = (await import('@editorjs/code')).default
    const LinkTool = (await import('@editorjs/link')).default
    const InlineCode = (await import('@editorjs/inline-code')).default

    const body = newsletterPatchSchema.parse(newsletter)

    if (!ref.current) {
      const editor = new EditorJS({
        holder: 'editor',
        onReady() {
          ref.current = editor
        },
        placeholder: 'Type here to write your newsletter...',
        inlineToolbar: true,
        data: body.content,
        tools: {
          header: Header,
          linkTool: LinkTool,
          list: List,
          code: Code,
          inlineCode: InlineCode,
          table: Table,
          embed: Embed
        }
      })
    }
  }, [newsletter])

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsMounted(true)
    }
  }, [])

  useEffect(() => {
    if (isMounted) {
      initializeEditor()

      return () => {
        ref.current?.destroy()
        ref.current = undefined
      }
    }
  }, [isMounted, initializeEditor])

  useEffect(() => {
    setValue('title', title)
  }, [title, setValue])

  useEffect(() => {
    setTitle(titleWatch)
  }, [titleWatch, setTitle])

  async function onSubmit(data: FormData) {
    setIsSaving(true)

    const blocks = await ref.current?.save()

    const response = await fetch(`/api/newsletter/${newsletter.id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: data.title,
        content: blocks
      })
    })

    setIsSaving(false)

    if (!response?.ok) {
      return toast({
        title: 'Something went wrong.',
        description: 'Your newsletter was not saved. Please try again.',
        variant: 'destructive'
      })
    }

    router.refresh()

    return toast({
      description: 'Your newsletter has been saved.'
    })
  }

  if (!isMounted) {
    return null
  }

  return (
    <section>
      <div className="grid w-full gap-10">
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center space-x-10">
            <p className="text-sm text-muted-foreground">
              {newsletter.published ? 'Published' : 'Draft'}
            </p>
          </div>
          <div className="flex gap-2">
            <button
              className={cn(buttonVariants())}
              onClick={() =>
                copyStringToClipboard(
                  `${window.location.origin}/newsletter/${newsletter.slug}`
                )
              }
            >
              Copy Share URL
            </button>
            <button
              onClick={handleSubmit(onSubmit)}
              type="submit"
              className={cn(buttonVariants())}
            >
              {isSaving && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              <span>Save</span>
            </button>
          </div>
        </div>
        <div className="lg:grid lg:grid-cols-4 gap-10">
          <div className="lg:col-span-1 mb-16 prose prose-stone mx-auto dark:prose-invert">
            <NewsletterGeneratorForm
              newsletterId={newsletter.id}
              title={title}
              setTitle={setTitle}
              isSaving={isSaving}
              setIsSaving={setIsSaving}
            />
          </div>
          <div className="lg:col-span-3 max-w-none lg:max-w-lg xl:max-w-none prose prose-stone mx-auto dark:prose-invert">
            <form onSubmit={handleSubmit(onSubmit)}>
              <TextareaAutosize
                autoFocus
                id="title"
                defaultValue={title}
                placeholder="Newsletter title"
                className="w-full resize-none appearance-none overflow-hidden bg-transparent text-5xl font-bold focus:outline-none"
                {...register('title')}
              />
              {isSaving && <ShellGeneratorSkeleton />}
              <div
                id="editor"
                style={{
                  display: isSaving ? 'none' : 'block'
                }}
                className="min-h-[500px]"
              />
            </form>
          </div>
        </div>
      </div>
    </section>
  )
}
