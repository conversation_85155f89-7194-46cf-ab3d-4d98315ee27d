import OpenAI from 'openai'
import { PrismaClient as PostgreSQLClient } from '../generated/postgres'
import { Region } from '@prisma/client'
import { GPTEmbeddingModel } from '@/types'
import { Document } from 'langchain/document'
import { developer } from './utils'

const postgres = new PostgreSQLClient()
const openai = new OpenAI()

export async function storeInternalLegalTextEmbedding({
  inputText,
  region,
  namespace,
  docId,
  metadata,
  year,
  court
}: {
  inputText: string
  region: Region
  namespace: string
  docId: number
  metadata: object
  year?: number
  court?: string
}) {
  try {
    // Generate the embedding
    const response = await openai.embeddings.create({
      model: GPTEmbeddingModel.TEsmall,
      input: inputText,
      encoding_format: 'float'
    })

    const embedding = response.data[0].embedding
    let result = null
    // Build the SQL query using tagged template literals
    if (region === 'US') {
      result = await postgres.$executeRaw`
        INSERT INTO "USVectorStore" ("namespace","docId","year","court","metadata","chunkText","embedding") 
        VALUES (${namespace},${docId},${year || null},${court || null},${metadata || null},${inputText},${embedding})`
    } else {
      result = await postgres.$executeRaw`
        INSERT INTO "INVectorStore" ("namespace","docId","year","court","metadata","chunkText","embedding") 
        VALUES (${namespace},${docId},${year || null},${court || null},${metadata || null},${inputText},${embedding})`
    }

    return result
  } catch (error) {
    console.error('Error in storeInternalLegalTextEmbedding:', error)
    // Rethrow the error to avoid swallowing it
    throw error
  }
}

export async function searchNearestVectors({
  queryText,
  options
}: {
  queryText: string
  options: {
    region: Region
    documentRecordsIds?: number[]
    namespace?: string
    year?: number[]
    court?: string[]
    threshold?: number
    limit?: number
  }
}): Promise<Document[]> {
  const {
    documentRecordsIds = null,
    namespace = null,
    year = null,
    court = null,
    threshold = 0.5,
    limit = 10
  } = options || {}

  // Generate Embedding for Query Text
  const response = await openai.embeddings.create({
    model: GPTEmbeddingModel.TEsmall,
    input: queryText
  })

  const queryEmbedding = response.data[0].embedding

  // Build Dynamic WHERE Clause
  const conditions = []
  const params: (string | string[] | number | number[])[] = [
    queryEmbedding,
    threshold
  ]
  let paramIndex = 3

  if (court && court.length > 0) {
    conditions.push(`"namespace" = ANY($${paramIndex++}::text[])`)
    params.push(court)
  } else if (namespace) {
    conditions.push(`"namespace" = $${paramIndex++}`)
    params.push(namespace)
  }
  if (year && year.length > 0) {
    conditions.push(`"year" = ANY($${paramIndex++}::int[])`)
    params.push(year)
  }
  if (documentRecordsIds && documentRecordsIds.length > 0) {
    conditions.push(`"docId" = ANY($${paramIndex++}::int[])`)
    params.push(documentRecordsIds)
  }

  developer.log([options])

  const whereClause =
    conditions.length > 0 ? 'AND ' + conditions.join(' AND ') : ''

  const query =
    options.region === 'US'
      ? `
    SELECT "id", "namespace", "docId", "year", "court", "metadata", "chunkText",
      1 - ("embedding" <#> $1::vector) AS similarity
    FROM "USVectorStore"
    WHERE
      (1 - ("embedding" <#> $1::vector)) >= $2
      ${whereClause}
    ORDER BY similarity DESC
    LIMIT $${paramIndex}`
      : `
    SELECT "id", "namespace", "docId", "year", "court", "metadata", "chunkText",
      1 - ("embedding" <#> $1::vector) AS similarity
    FROM "INVectorStore"
    WHERE
      (1 - ("embedding" <#> $1::vector)) >= $2
      ${whereClause}
    ORDER BY similarity DESC
    LIMIT $${paramIndex}`

  // Log the final query string and parameters
  // developer.log(["Query: ", query])
  // developer.log(["Limit: ", limit])

  // Execute the query
  const results = (await postgres.$queryRawUnsafe(
    query,
    ...params,
    limit
  )) as any[]

  // developer.log(["Query Results: ", results]);

  const documents = results?.map((result: any) => {
    return new Document({
      id: result.id,
      pageContent: result.chunkText,
      metadata: {
        namespace: result.namespace,
        documentRecordsId: result.docId,
        year: result.year,
        court: result.court,
        ...result.metadata
      }
    })
  })

  return (documents || []) as Document[]
}

export async function storePrivateTextEmbedding({
  inputText,
  namespace,
  docId,
  metadata
}: {
  inputText: string
  namespace: string
  docId: number
  metadata: object
}) {
  try {
    // Generate the embedding
    const response = await openai.embeddings.create({
      model: GPTEmbeddingModel.TEsmall,
      input: inputText,
      encoding_format: 'float'
    })

    const embedding = response.data[0].embedding
    let result = null
    // Build the SQL query using tagged template literals

    result = await postgres.$executeRaw`
        INSERT INTO "UserVectorStore" ("teamid","docId","metadata","chunkText","embedding") 
        VALUES (${namespace},${docId},${metadata || null},${inputText},${embedding})`

    return result
  } catch (error) {
    console.error('Error in storeInternalLegalTextEmbedding:', error)
    // Rethrow the error to avoid swallowing it
    throw error
  }
}

export async function searchNearestPrivateVectors({
  queryText,
  options
}: {
  queryText: string
  options: {
    region: Region
    namespace: string
    documentRecordsIds?: number[]
    threshold?: number
    limit?: number
  }
}): Promise<Document[]> {
  const {
    documentRecordsIds = null,
    namespace,
    threshold = 0.5,
    limit = 10
  } = options || {}

  // Generate Embedding for Query Text
  const response = await openai.embeddings.create({
    model: GPTEmbeddingModel.TEsmall,
    input: queryText
  })

  const queryEmbedding = response.data[0].embedding

  // Build Dynamic WHERE Clause
  const conditions = []
  const params: (string | string[] | number | number[])[] = [
    queryEmbedding,
    threshold
  ]
  let paramIndex = 3

  conditions.push(`"teamid" = $${paramIndex++}`)
  params.push(namespace)

  if (documentRecordsIds && documentRecordsIds.length > 0) {
    conditions.push(`"docId" = ANY($${paramIndex++}::int[])`)
    params.push(documentRecordsIds)
  }

  developer.log([options])

  const whereClause =
    conditions.length > 0 ? 'AND ' + conditions.join(' AND ') : ''

  const query = `
    SELECT
      "id",
      "teamid",
      "docId",
      "metadata",
      "chunkText",
      1 - ("embedding" <#> $1::vector) AS similarity
    FROM "UserVectorStore"
    WHERE
      (1 - ("embedding" <#> $1::vector)) >= $2
      ${whereClause}
    ORDER BY similarity DESC
    LIMIT $${paramIndex}`

  // Log the final query string and parameters
  // developer.log(["Query: ", query])
  // developer.log(["Limit: ", limit])

  // Execute the query
  const results = (await postgres.$queryRawUnsafe(
    query,
    ...params,
    limit
  )) as any[]

  // developer.log(results);

  const documents = results?.map((result: any) => {
    return new Document({
      id: result.id,
      pageContent: result.chunkText,
      metadata: {
        namespace: result.teamId,
        documentRecordsId: result.docId,
        ...result.metadata
      }
    })
  })

  return (documents || []) as Document[]
}
