'use client'

import { useRouter } from 'next/navigation'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import { CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { Icons } from '@/components/elements/icons'
import { showcauseOrderPostSchema } from '@/lib/validations/showcause-order'
import { useState } from 'react'

interface ShowCauseOrderGeneratorFormProps
  extends React.HTMLAttributes<HTMLFormElement> {
  className?: string
}

type FormData = z.infer<typeof showcauseOrderPostSchema>

export function ShowCauseOrderGeneratorForm({
  className,
  ...props
}: ShowCauseOrderGeneratorFormProps) {
  const router = useRouter()
  const {
    handleSubmit,
    register,
    control,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(showcauseOrderPostSchema)
  })

  const [isSaving, setIsSaving] = useState<boolean>(false)

  async function onSubmit(data: FormData) {
    setIsSaving(true)

    const formData = new FormData()
    formData.append('title', data.title)
    formData.append('showCauseNotice', data.showCauseNotice[0])
    formData.append('showCauseReply', data.showCauseReply[0])

    const response = await fetch(`/api/show-cause/create`, {
      method: 'POST',
      body: formData
    })

    setIsSaving(false)

    if (!response?.ok) {
      return toast({
        title: 'Uh Oh - something went wrong please try again.',
        description:
          'Your show cause response was not generated. My circuits have failed me. Tell my motherboard I loved her..',
        variant: 'destructive'
      })
    }

    toast({
      description:
        'Drumroll, please... Your show cause response is getting generated.'
    })

    const create = await response.json()
    router.push(`/dashboard/show-cause-notice/${create?.id}`)
  }

  console.log(errors)

  return (
    <form
      className={cn(className)}
      onSubmit={handleSubmit(onSubmit)}
      {...props}
    >
      <CardContent>
        <div className="grid gap-1 py-3">
          <Label htmlFor="title">Title</Label>
          <Input
            id="title"
            className="w-auto xl:w-[400px]"
            size={32}
            {...register('title')}
          />
          {errors?.title && (
            <p className="px-1 text-xs text-red-600">{errors.title.message}</p>
          )}
        </div>

        <div className="grid gap-1 py-3">
          <Label htmlFor="description">Upload Show Cause Notice</Label>
          <Input
            id="showCauseNotice"
            type="file"
            {...register('showCauseNotice')}
          />
          {errors?.showCauseNotice && (
            <p className="px-1 text-xs text-red-600">
              {errors.showCauseNotice.message as string}
            </p>
          )}
        </div>

        <div className="grid gap-1 py-3">
          <Label htmlFor="description">Upload Show Cause Reply</Label>
          <Input
            id="showCauseReply"
            type="file"
            {...register('showCauseReply')}
          />
          {errors?.showCauseReply && (
            <p className="px-1 text-xs text-red-600">
              {errors.showCauseReply.message as string}
            </p>
          )}
        </div>

        <button
          type="submit"
          className={cn(buttonVariants(), className)}
          disabled={isSaving}
        >
          {isSaving && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
          <span>Generate</span>
        </button>
      </CardContent>
    </form>
  )
}
