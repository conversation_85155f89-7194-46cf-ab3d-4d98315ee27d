'use server'

import { Pinecone } from '@pinecone-database/pinecone'
import { PineconeStore } from '@langchain/pinecone'
import { OpenAIEmbeddings } from '@langchain/openai'
import { env } from '@/env.mjs'
import { extractHumanMessages, fetchFullDocuments } from '@/lib/retriever-utils'
import { GPTModel } from '@/types'
import { Region, ResearchType } from '@prisma/client'
import { assessQuestionQuality } from '../cerebrum/gpt-assisted-processes'
import type { Document } from 'langchain/document'
import type { StoredNamespace, VercelChatMessage } from '@/types'
import type { CaseData } from '@/types/document'

export async function vectorSearchConversation({
  messages,
  researchType,
  region,
  filter
}: {
  messages: VercelChatMessage[]
  region: Region
  researchType: ResearchType
  filter: {
    namespace: StoredNamespace[Region]
    court?: string[]
    year?: string[]
    documentRecordsId?: {
      $nin: number[]
    }
  }
}) {
  const searchContent = extractHumanMessages(messages)

  const refinedQuestion = await assessQuestionQuality({
    messages,
    researchType,
    question: searchContent,
    model: GPTModel.GPT4o,
    region
  })

  const pinecone = new Pinecone()
  const pineconeIndex = pinecone.Index(env.PINECONE_INDEX)

  let documents: Document<Record<string, any>>[] = []
  const docsToSearch = 5
  const docsToSkip = filter?.documentRecordsId?.$nin || []
  let currentOutput = docsToSearch

  while (documents.length < docsToSearch && currentOutput >= docsToSearch) {
    const vectorstore = await PineconeStore.fromExistingIndex(
      new OpenAIEmbeddings(),
      {
        pineconeIndex,
        namespace: filter.namespace,
        filter: {
          court: filter?.court ? { $in: filter.court } : undefined,
          year: filter?.year
            ? {
                $in: filter.year.map((year) => parseInt(year))
              }
            : undefined,
          documentRecordsId: { $nin: docsToSkip }
        }
      }
    )

    const results = await vectorstore.similaritySearch(
      refinedQuestion.rewritten_question || searchContent,
      docsToSearch
    )

    const uniqueDocuments = results.reduce(
      (acc: Document<Record<string, any>>[], result) => {
        const documentId = result.metadata.documentRecordsId
        const existingDocumentIndex = acc.findIndex(
          (doc) => doc.metadata.documentRecordsId === documentId
        )

        if (existingDocumentIndex !== -1) {
          // If the document is already in the accumulator, concatenate its pageContent
          acc[existingDocumentIndex].pageContent += ' ... ' + result.pageContent
        } else {
          // If the document is not in the accumulator, add it
          acc.push(result)
        }

        return acc
      },
      []
    )

    currentOutput = results.length
    documents = documents.concat(uniqueDocuments)
    const documentIds = documents.map((doc) => doc.metadata.documentRecordsId)
    docsToSkip.push(...documentIds)
  }

  const documentCollection = await fetchFullDocuments(
    documents.map((doc) => doc.metadata.documentRecordsId)
  )

  const minifiedDocuments = documentCollection.map((doc) => {
    const vectorResult = documents.find(
      (document) => document.metadata.documentRecordsId === doc.id
    )
    const metadata = JSON.parse(doc.meta) as CaseData
    return {
      ref: 'llr-' + doc.ref,
      case: metadata.case,
      court: metadata.court,
      judges: metadata.judges,
      parties: metadata.parties,
      citation: metadata.citation,
      date: metadata.date,
      year: metadata.year || 1000,
      headnotes: metadata.headnotes,
      title: metadata.title || doc.title,
      pageContent: vectorResult?.pageContent
        ?.replace(/(\r\n|\n|\r)/gm, ' ')
        ?.trim(),
      summary: metadata.summary || doc.content.slice(0, 200)
    }
  })

  minifiedDocuments.sort((a, b) => {
    if (a.year > b.year) {
      return -1
    } else if (a.year < b.year) {
      return 1
    } else {
      return 0
    }
  })

  const extendedMessage: VercelChatMessage = {
    id: (messages.length + 1).toString(),
    content: JSON.stringify(minifiedDocuments),
    role: 'system'
  }

  return {
    messages: [...messages, extendedMessage],
    documents: docsToSkip
  }
}

export type VectorSearchConversationType = typeof vectorSearchConversation
