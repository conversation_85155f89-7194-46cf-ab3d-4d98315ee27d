import { redirect } from 'next/navigation'
import { getCurrentUser, getFeatureUsageStats } from '@/lib/session'
import { db } from '@/lib/db'
import { BinderList } from '@/components/elements/binder/binder-list'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { NewBinderModal } from '@/components/elements/binder/new-binder-modal'
import { CreditType } from '@prisma/client'

export default async function BinderPage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect('/login')
  }

  const usageStats = await getFeatureUsageStats({
    feature: CreditType.case
  })

  const allowCreation = usageStats.used < usageStats.available

  const binders = await db.binder.findMany({
    where: {
      teamId: user.teamId
    },
    include: {
      _count: {
        select: {
          documents: true,
          researches: true
        }
      },
      creator: {
        select: {
          name: true
        }
      },
      Dataset: {
        include: {
          _count: {
            select: {
              DocumentRecordDatasetMap: true
            }
          }
        }
      }
    },
    orderBy: {
      updatedAt: 'desc'
    }
  })

  const refinedList = binders.map((binder) => {
    const datasetDocCount = binder.Dataset.reduce(
      (sum, dataset) => sum + dataset._count.DocumentRecordDatasetMap,
      0
    )

    return {
      id: binder.id,
      title: binder.name,
      numDocuments: binder._count.documents + datasetDocCount,
      numResearches: binder._count.researches,
      createdBy: binder.creator.name || '',
      createdAt: binder.createdAt
    }
  })

  return (
    <DashboardShell>
      <DashboardHeader heading="Cases" text="Manage your cases">
        <NewBinderModal allow={allowCreation} />
      </DashboardHeader>
      <BinderList binders={refinedList} />
    </DashboardShell>
  )
}
