import { Icons } from '@/components/elements/icons'
import { UserWelcomeForm } from '@/components/elements/forms/user-welcome-form'
import { db } from '@/lib/db'
import { findTeamCreditStats } from '@/lib/recordstore-team'
import { getCurrentUser } from '@/lib/session'
import { redirect } from 'next/navigation'

export const metadata = {
  title: 'Welcome to SmartCounsel AI',
  description:
    'Welcome to SmartCounsel AI. Please help us with some information first.'
}

export default async function WelcomePage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect('/login')
  }
  const usage = await findTeamCreditStats({
    teamId: user.teamId
  })

  const teamName = await db.team.findUnique({
    where: { id: user.teamId },
    select: { name: true }
  })

  return (
    <div className="container grid h-screen w-screen flex-col items-center justify-center lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="h-full bg-muted flex flex-col space-y-2 text-center items-center justify-center">
        <h1 className="text-5xl font-bold tracking-wide font-heading">
          Welcome to
        </h1>
        <Icons.logo className="mx-auto px-16" />
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Let&apos;s get you started
            </h1>
            <p className="text-sm text-muted-foreground">
              Please help us with some information first
            </p>
          </div>
          <UserWelcomeForm
            user={user}
            teamName={teamName?.name}
            creditUsageStats={usage}
          />
        </div>
      </div>
    </div>
  )
}
