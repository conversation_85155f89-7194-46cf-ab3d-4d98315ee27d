'use server'

import { CaseFileType } from '@prisma/client'
import { db } from '../../db'
import { UnauthorizedError } from '../../exceptions'
import { getCurrentUser } from '../../session'
import { generateCaseEvaluationLite } from '@/lib/processes/case-eval-lite-generator'
import { DocumentContent } from '@/types/case'

export async function handleCaseEvaluationLiteGeneration({
  binderId,
  selectedDocumentsByType
}: {
  binderId: string
  selectedDocumentsByType: Record<string, string[]>
}) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    // Extract all document IDs and convert to numbers
    const allDocumentIds = Object.values(selectedDocumentsByType)
      .flat()
      .map(Number)

    // Fetch all documents in a single query
    const documents = await db.documentRecords.findMany({
      where: {
        id: {
          in: allDocumentIds
        }
      },
      select: {
        id: true,
        title: true,
        indexed: true,
        content: true
      }
    })

    // Map content by document type
    const documentContentByType = Object.fromEntries(
      Object.entries(selectedDocumentsByType).map(([docType, docIds]) => [
        docType,
        documents.filter((doc) => docIds.includes(doc.id.toString()))
      ])
    )

    // Generate the case evaluation lite
    const caseEvaluationLite = await generateCaseEvaluationLite(
      binderId,
      documentContentByType,
      user
    )

    // Store the case evaluation lite in the database
    const caseFile = await db.caseFile.upsert({
      where: {
        binderId_fileType: {
          binderId,
          fileType: CaseFileType.CASE_EVALUATION
        }
      },
      update: {
        content: caseEvaluationLite,
        selectedDocumentsByType,
        updatedAt: new Date()
      },
      create: {
        binderId,
        creatorId: user.id,
        fileType: CaseFileType.CASE_EVALUATION,
        content: caseEvaluationLite,
        selectedDocumentsByType
      }
    })

    // Track credit usage
    await db.teamCreditUsed.create({
      data: {
        teamId: user.teamId,
        type: 'case',
        refId: caseFile.id,
        eventId: new Date().getTime().toString()
      }
    })

    return {
      success: true,
      caseEvaluation: caseFile
    }
  } catch (error) {
    console.error('Failed to generate case evaluation lite:', error)
    return {
      success: false,
      error: 'Failed to generate case evaluation lite'
    }
  }
}
