import { BNSSInfoCard } from '@/components/elements/bnss-infocard'
import { ChatWindowExampler } from '@/components/elements/chat/chat-window-exampler'
import { buttonVariants } from '@/components/ui/button'
import { cn } from '@/lib/utils'

import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'New Criminal Law: BNS, BNSS, and BSA',
  description:
    "Explore Bharatiya Nyaya Sanhita (BNS), Bharatiya Nagarik <PERSON>aksha <PERSON> (BNSS), and Bharatiya Sakshya Adhiniyam (BSA) and its implications with SmartCounsel AI. Get AI-driven insights and guidance on India's new criminal laws."
}

const templatedQuestions = [
  {
    question:
      'What is the purpose of the Bharatiya Nagarik Suraksha Sanhita (BNSS)?',
    answer:
      'The Bharatiya Nagarik Suraksha Sanhita (BNSS) aims to consolidate and amend the laws relating to the procedure of the criminal justice system in India. Its purpose is to make the criminal justice system more efficient, ensure faster delivery of justice, and incorporate modern methods of fact-finding and evidence collection.'
  },
  {
    question: 'How does BNSS change the process of FIR registration?',
    answer:
      'Under the BNSS, there is a specific emphasis on speeding up the initial phases of criminal cases. FIRs (First Information Reports) must now be recorded within three days for complaints submitted through electronic communication. This change is intended to expedite the legal process right from its inception.'
  },
  {
    question:
      'What are the new provisions for trial and investigation timelines in the BNSS?',
    answer:
      'The BNSS introduces specific timelines for different stages of the criminal process to ensure speedy justice. For example, it requires competent magistrates to frame charges within 60 days from the first hearing on the charge. Additionally, criminal courts are required to pronounce judgments within 45 days after the trial concludes.'
  },
  {
    question: 'How does BNSS handle the recording of searches and seizures?',
    answer:
      'To bring transparency and accountability to the investigation process, BNSS mandates the audio-video recording of all search and seizure operations. This provision aims to minimize allegations of tampering and misconduct during these critical investigative procedures.'
  },
  {
    question:
      'What new rights or protections does BNSS offer to victims or informants?',
    answer:
      'The BNSS places a strong emphasis on keeping victims and informants in the loop regarding the status of investigations. It mandates regular updates to victims or informants about the status of the investigation within 90 days, enhancing transparency and ensuring that those affected by crime are kept informed of the progress.'
  }
]

export default function BharatiyaSakshyaAdhiniyamPage() {
  return (
    <main className="space-y-6 pb-8 pt-6 md:pb-12 md:pt-10 lg:py-32">
      <section className="container flex max-w-[64rem] flex-col items-center gap-4 text-center py-12">
        <div className="text-center space-y-4">
          <div className="rounded-2xl bg-muted px-4 py-1.5 text-sm font-medium w-max m-auto">
            SmartCounsel AI: New Criminal Laws Edition
          </div>
          <h1 className="font-heading text-3xl sm:text-5xl md:text-6xl lg:text-7xl">
            Understand and Navigate India&apos;s New Criminal Laws with AI
          </h1>
          <p className="mx-auto pt-4 leading-normal text-muted-foreground sm:text-xl sm:leading-8">
            With the introduction of Bharatiya Nyaya Sanhita (BNS), Bharatiya
            Nagarik Suraksha Sanhita (BNSS), and Bharatiya Sakshya Adhiniyam
            (BSA), the legal landscape in India is evolving. SmartCounsel AI
            helps you stay informed and prepared with AI-driven insights and
            guidance.
          </p>
        </div>
      </section>

      <section className="container space-y-6 bg-slate-50 py-8 dark:bg-transparent md:py-12 lg:py-24">
        <div className="space-y-6">
          <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
            New Legal Framework: <br /> A Closer Look
          </h2>
          <p className="max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8">
            The BNS, BNSS, and BSA have replaced the IPC, CrPC, and Indian
            Evidence Act respectively, marking significant shifts in procedural
            and substantive legal norms. These changes include enhanced police
            powers, alterations in legal proceedings, and implications for both
            ongoing and future litigation.
          </p>
          <p className="max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8">
            Explore the details of these new laws, their objectives, and the
            controversies surrounding their implementation. Our AI-powered tools
            offer comprehensive analysis and practical advice tailored to these
            new statutes.
          </p>
        </div>

        <h3 className="font-heading text-2xl leading-[1.1] sm:text-2xl md:text-4xl">
          Try it yourself:
        </h3>
        <ChatWindowExampler
          emptyStateComponent={<BNSSInfoCard />}
          sampleQuestions={templatedQuestions}
        />
      </section>

      <section className="container flex flex-col space-y-6 bg-slate-50 py-8 dark:bg-transparent md:py-12 lg:py-24">
        <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
          Key Features & Benefits
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="relative overflow-hidden rounded-lg border bg-background p-2">
            <div className="flex flex-col justify-between rounded-md p-6">
              <div className="space-y-2">
                <h3 className="font-bold">Instant Legal Queries</h3>
                <p className="text-sm text-muted-foreground">
                  Ask any question about the new Bharatiya Nyaya Sanhita,
                  Bharatiya Nagarik Suraksha Sanhita, and Bharatiya Sakshya
                  Adhiniyam and get accurate, AI-powered responses instantly.
                </p>
              </div>
            </div>
          </div>

          <div className="relative overflow-hidden rounded-lg border bg-background p-2">
            <div className="flex h-[180px] flex-col justify-between rounded-md p-6">
              <div className="space-y-2">
                <h3 className="font-bold">Case Analysis</h3>
                <p className="text-sm text-muted-foreground">
                  Upload case details and receive an analysis based on the new
                  laws, helping you understand potential outcomes and
                  preparations needed.
                </p>
              </div>
            </div>
          </div>

          <div className="relative overflow-hidden rounded-lg border bg-background p-2">
            <div className="flex h-[180px] flex-col justify-between rounded-md p-6">
              <div className="space-y-2">
                <h3 className="font-bold">Continuous Updates</h3>
                <p className="text-sm text-muted-foreground">
                  Stay updated as the legal landscape evolves. Our platform
                  continuously integrates the latest legal changes and
                  interpretations.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="container space-y-6 bg-slate-50 py-8 dark:bg-transparent md:py-12 lg:py-24">
        <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
          Get Started with SmartCounsel AI
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 items-center">
          <p className="max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8 col-span-3">
            Join thousands of users leveraging SmartCounsel AI to navigate
            India&apos;s new criminal laws. Whether you&apos;re a legal
            professional, a student, or just a citizen curious about your
            rights, SmartCounsel AI offers tailored guidance and insights.
          </p>
          <div className="flex justify-center">
            <a href="/register" className={cn([buttonVariants({})])}>
              Sign Up Now
            </a>
          </div>
        </div>
      </section>
    </main>
  )
}
