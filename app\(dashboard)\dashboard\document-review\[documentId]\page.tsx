import { notFound, redirect } from 'next/navigation'
import { TeamDocument } from '@prisma/client'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { getCurrentUser } from '@/lib/session'
import { Card, CardHeader, CardTitle } from '@/components/ui/card'
import { DocumentReviewResponse } from '@/app/api/gpt/review/route'
import { ReviewDisplayCardCollection } from '@/components/elements/document/document-review-display'
import { ReviewDisplayCardCollectionLoader } from '@/components/elements/document/document-review-loader'

async function getTeamDocumentChunkReviews(documentId: TeamDocument['id']) {
  const data = await db.teamDocumentChunk.findMany({
    where: {
      teamDocumentId: documentId
    }
  })

  if (!data || data.length === 0) {
    return null
  }

  const chunksReview = data.reduce((acc: DocumentReviewResponse[], chunk) => {
    if (chunk.indexed && chunk.review) {
      const review = JSON.parse(chunk.review) as DocumentReviewResponse[]
      acc.push(...review)
    }
    return acc
  }, [])

  const pendingChunks = data.filter((chunk) => !chunk.indexed)

  return {
    chunksReview,
    pendingChunks
  }
}

interface EditorPageProps {
  params: { documentId: string }
}

export default async function EditorPage({ params }: EditorPageProps) {
  const user = await getCurrentUser()

  if (!user) {
    redirect(authOptions?.pages?.signIn || '/login')
  }

  const data = await getTeamDocumentChunkReviews(params.documentId)

  if (!data) {
    notFound()
  }

  const progress =
    (data.chunksReview.length /
      (data.chunksReview.length + data.pendingChunks.length)) *
    100

  return (
    <main className="grid grid-cols-1 gap-5">
      <Card className="grid gap-5 p-5">
        <CardHeader>
          <CardTitle>Review Results</CardTitle>
        </CardHeader>
        {data.pendingChunks.length > 0 && progress < 100 && (
          <ReviewDisplayCardCollectionLoader
            documentId={params.documentId}
            progress={progress}
          />
        )}
      </Card>
      <ReviewDisplayCardCollection segments={data.chunksReview} />
    </main>
  )
}
