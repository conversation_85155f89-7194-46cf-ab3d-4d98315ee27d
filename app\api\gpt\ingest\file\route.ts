import { NextRequest, NextResponse } from 'next/server'
import { UnauthorizedError } from '@/lib/exceptions'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { developer } from '@/lib/utils'
import { mapDocumentToCase } from '@/lib/recordstore-case'
import { injestDocument } from '@/lib/actions/injest'

export const maxDuration = 800

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!(session && session.user)) {
    throw new UnauthorizedError()
  }

  try {
    const user = session.user
    const formData = await req.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    const binderId = formData.get('binderId') as string
    developer.log(['File:', file.name, file.type, binderId])

    const create = await injestDocument({ file, user })
    if (create?.id) {
      if (binderId && binderId !== '') {
        await mapDocumentToCase({
          documentId: create.id,
          binderId: binderId,
          userId: user.id
        })
      }

      return NextResponse.json({ ok: true }, { status: 200 })
    } else {
      return NextResponse.json(
        { error: 'Failed to create document' },
        { status: 500 }
      )
    }
  } catch (e: any) {
    console.error('Document ingestion error:', e)
    return NextResponse.json(
      { error: e.message || 'Unknown error' },
      { status: 500 }
    )
  }
}
