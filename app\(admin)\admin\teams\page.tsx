import { db } from '@/lib/db'
import { EmptyPlaceholder } from '@/components/elements/custom-components/empty-placeholder'
import { DashboardHeader } from '@/components/elements/layout/header'
import { TeamItem } from '@/components/elements/team-item'
import { DashboardShell } from '@/components/elements/layout/shell'
import { DataTablePagination } from '@/components/elements/data-table-pagination'
import DownloadExcelButton from '@/components/elements/buttons/download-excel-button'
import { getTeamsReportList } from '@/lib/actions/admin'

export const metadata = {
  title: 'Teams List'
}

export default async function TeamsListPage({
  searchParams
}: {
  searchParams: { [key: string]: string | string[] }
}) {
  let pageLimiter = searchParams.limit ? Number(searchParams.limit) : 20
  const page = searchParams.page ? Number(searchParams.page) : 1
  const teamId = searchParams.teamId ? String(searchParams.teamId) : undefined

  const teams = await db.team.findMany({
    where: {
      id: teamId ? { equals: teamId } : undefined
    },
    include: {
      User: true,
      TeamDocument: true,
      TeamPeriodicCredit: true,
      TeamCreditUsed: true
    },
    orderBy: {
      createdAt: 'desc'
    },
    skip: (page - 1) * pageLimiter,
    take: pageLimiter
  })

  const totalTeams = await db.team.count({
    where: {
      id: teamId ? { equals: teamId } : undefined
    }
  })
  const totalPage = Math.ceil(totalTeams / pageLimiter)

  const startIndex = totalTeams - (page - 1) * pageLimiter

  const teamsWithIndex = teams?.map((team, index) => ({
    index: startIndex - index,
    ...team
  }))

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Teams"
        text="List of active teams on SmartCounsel"
      >
        <DownloadExcelButton
          customName="Team_List_Report"
          dataFunction={getTeamsReportList}
        />
      </DashboardHeader>
      <div>
        {teamsWithIndex?.length ? (
          <div className="divide-y divide-border rounded-md border bg-white dark:bg-slate-950">
            {teamsWithIndex.map((team) => (
              <TeamItem key={team.id} team={team} />
            ))}
            <DataTablePagination
              currentPage={page}
              totalPage={totalPage}
              searchParams={searchParams}
            />
          </div>
        ) : (
          <EmptyPlaceholder>
            <EmptyPlaceholder.Icon name="post" />
            <EmptyPlaceholder.Title>
              No teams joined yet.
            </EmptyPlaceholder.Title>
            <EmptyPlaceholder.Description>
              Create a new team and invite members to join.
            </EmptyPlaceholder.Description>
          </EmptyPlaceholder>
        )}
      </div>
    </DashboardShell>
  )
}
