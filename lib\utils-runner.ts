import axios from 'axios'

export async function validatePdfUrlsInBatches(
  urls: string[]
): Promise<{ goodUrls: string[]; badUrls: string[] }> {
  const BATCH_SIZE = 10
  const goodUrls: string[] = []
  const badUrls: string[] = []

  for (let i = 0; i < urls.length; i += BATCH_SIZE) {
    const batch = urls.slice(i, i + BATCH_SIZE)

    console.log(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1}:`, batch)

    const results = await Promise.all(
      batch.map(async (url) => {
        try {
          const response = await axios.head(url, { timeout: 5000 }) // Timeout to prevent hanging requests
          const contentType = response.headers['content-type']

          if (contentType === 'application/pdf') {
            return { url, isValid: true }
          } else {
            return { url, isValid: false }
          }
        } catch (error) {
          console.error(`Error validating URL: ${url}`, error)
          return { url, isValid: false }
        }
      })
    )

    // Separate good and bad URLs
    results.forEach(({ url, isValid }) => {
      if (isValid) {
        goodUrls.push(url)
      } else {
        badUrls.push(url)
      }
    })
  }

  return { goodUrls, badUrls }
}
