import { Node, mergeAttributes, InputRule } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import { CitationListModal } from '../custom-components/citation-list-modal'

export const CitationModalExtension = Node.create({
  name: 'modalLink',
  priority: 1000,
  group: 'inline',
  inline: true,
  atom: true,
  marks: '',
  content: '', // leaf node

  addAttributes() {
    return {
      docId: {
        default: null
      },
      page: {
        default: null
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-modal-link]',
        getAttrs: (dom: HTMLElement) => {
          const docId = dom.getAttribute('data-doc-id')
          const page = dom.getAttribute('data-page')
          return { docId, page }
        }
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const { docId, page } = HTMLAttributes

    return [
      'span',
      mergeAttributes(this.options.HTMLAttributes, {
        'data-modal-link': 'true',
        'data-doc-id': docId,
        'data-page': page
      }),
      `[${docId}:${page}]`
    ]
  },

  addNodeView() {
    return ReactNodeViewRenderer(CitationListModal)
  },

  // Add input rule to match [docId:page] format
  addInputRules() {
    // Matches [3243:3] or [3243:3-6] format for citations (docId:page or docId:page-range)
    const regex = /\[(\d+):(\d+(?:-\d+)?)\]$/
    const type = this.type

    return [
      new InputRule({
        find: regex,
        handler: ({ state, range, match }) => {
          const { tr } = state
          const { from, to } = range
          const [, docId, page] = match

          if (match[0]) {
            tr.replaceWith(from, to, type.create({ docId, page }))
          }
        }
      })
    ]
  }
})
