'use client'

import { useState } from 'react'
import FileDropZone from './file-dropzone'
import { cn } from '@/lib/utils'
import { buttonVariants } from '../../ui/button'
import { Icons } from '../icons'
import { toast } from '../../ui/use-toast'
import { Textarea } from '../../ui/textarea'
import { useRouter } from 'next/navigation'
import { Card } from '../../ui/card'

export function DocumentReview({ endpoint }: { endpoint: string }) {
  const router = useRouter()
  const [file, setFile] = useState<File | null>(null)
  const [input, setInput] = useState<string>('')

  const [isLoading, setIsLoading] = useState<boolean>(false)

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()
    setIsLoading(true)

    const formData = new FormData()
    if (file) {
      formData.set('file', file)
    } else {
      formData.set('text', input)
    }

    const response = await fetch(endpoint, {
      method: 'POST',
      body: formData
    })

    setIsLoading(false)

    if (!response?.ok) {
      return toast({
        title: 'Something went wrong.',
        description: 'Your name was not updated. Please try again.',
        variant: 'destructive'
      })
    }

    const data = await response.json()
    router.refresh()

    router.push(`/dashboard/document-review/${data.id}`)
  }

  return (
    <main className="grid grid-cols-1 gap-5">
      <Card>
        <div className="flex flex-col items-center p-4 md:p-8 rounded grow overflow-hidden border">
          <FileDropZone
            file={file}
            setFile={setFile}
            allowedFileTypes={['application/pdf']}
            maxFileSize={10}
            className="w-full mt-1"
          />

          <form onSubmit={handleSubmit} className="flex w-full flex-col">
            <div className="w-full my-4">
              <Textarea
                className="grow mr-8 p-4 rounded"
                value={input}
                placeholder={'Paste your document here'}
                onChange={
                  ((e) =>
                    setInput(
                      e.target.value
                    )) as React.ChangeEventHandler<HTMLTextAreaElement>
                }
              />
            </div>

            <button type="submit" className={cn(buttonVariants())}>
              {isLoading && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              <span>Review Document</span>
            </button>
          </form>
        </div>
      </Card>
    </main>
  )
}
