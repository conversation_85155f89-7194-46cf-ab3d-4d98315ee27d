'use client'

import { useState, useCallback, useMemo } from 'react'
import { CaseDocumentSelector } from './doc-upload-case-selector'
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { But<PERSON> } from '@/components/ui/button'
import ReviewAndFinalizeUploads from './review-finalize-uploads'
import DocumentGeneration from './doc-generation'
import { ArrowRightCircle, Loader2, Save } from 'lucide-react'
import {
  handleCaseEvaluationGeneration,
  saveCaseEvaluationContent
} from '@/lib/actions/case/case-eval'
import { handleCaseEvaluationLiteGeneration } from '@/lib/actions/case/case-eval-lite'
import { toast } from '@/components/ui/use-toast'
import { Binder, CaseFile } from '@prisma/client'
import { CopyButton, DownloadButton } from '../document/document-actions'
import { useRouter } from 'next/navigation'
import { TipTapEditor } from '@/components/external/tiptap/tiptap-editor'
import {
  AttorneyInsightsForm,
  AttorneyInsightsFormData
} from './attorney-strategy-form'
import { DocumentTitle } from '@/types/case'

type DocType =
  | 'Police/Incident Reports'
  | 'EMS Reports'
  | 'Hospital Records'
  | 'Treatment Records'
  | 'Imaging Reports'
  | 'Medical Bills'
  | 'Insurance Correspondence'
  | 'Witness Statements'

export type SelectedDocuments = Record<DocType | string, string[]>

// Constants to avoid repetition
const DOCUMENT_TYPES: DocType[] = [
  'Police/Incident Reports',
  'EMS Reports',
  'Hospital Records',
  'Treatment Records',
  'Imaging Reports',
  'Medical Bills',
  'Insurance Correspondence',
  'Witness Statements'
]

const STEPS = {
  SELECT: 'step1',
  ADDITIONAL: 'step2',
  STRATEGY: 'step3',
  REVIEW: 'step4',
  GENERATE: 'step5'
} as const

export default function CaseEvaluationSteps({
  binder,
  allDocuments,
  caseEvaluation,
  selectedDocumentsByTypeFromStore,
  attorneyStrategyPlan,
  medicalChronologyExists = false,
  usageStats = {
    available: 0,
    used: 0
  }
}: {
  binder: Binder
  allDocuments: DocumentTitle[]
  caseEvaluation: CaseFile | null
  selectedDocumentsByTypeFromStore: SelectedDocuments
  attorneyStrategyPlan: AttorneyInsightsFormData | null
  medicalChronologyExists?: boolean
  usageStats?: {
    available: number
    used: number
  }
}) {
  const router = useRouter()

  // Initialize state with properly typed structure
  const [selectedDocumentsByType, setSelectedDocumentsByType] =
    useState<SelectedDocuments>(() => {
      // Initialize with default empty arrays for each document type
      const initial = DOCUMENT_TYPES.reduce((acc, type) => {
        acc[type] = selectedDocumentsByTypeFromStore[type] || []
        return acc
      }, {} as SelectedDocuments)

      // Add additional documents category
      initial['Additional Documents'] =
        selectedDocumentsByTypeFromStore['Additional Documents'] || []
      return initial
    })

  // Track current step and generation state
  const [currentTab, setCurrentTab] = useState<
    (typeof STEPS)[keyof typeof STEPS]
  >(caseEvaluation?.content ? STEPS.GENERATE : STEPS.SELECT)
  const [step4Allowed, setStep4Allowed] = useState(!!caseEvaluation?.content)
  const [generatedDocument, setGeneratedDocument] = useState<string | null>(
    caseEvaluation?.content || null
  )
  const [isGenerating, setIsGenerating] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [editedDocument, setEditedDocument] = useState<string | null>(null)
  const [attorneyInsights, setAttorneyInsights] =
    useState<AttorneyInsightsFormData | null>(attorneyStrategyPlan)
  const [isGeneratingLite, setIsGeneratingLite] = useState(false)

  // Memoized calculation for available documents in step 2
  const availableAdditionalDocuments = useMemo(() => {
    // Get all documents already selected in previous steps
    const selectedIdsToExclude = Object.entries(selectedDocumentsByType)
      .filter(([category]) => category !== 'Additional Documents')
      .flatMap(([_, ids]) => ids)

    // Return only documents not already selected
    return allDocuments.filter(
      (doc) => !selectedIdsToExclude.includes(String(doc.id))
    )
  }, [allDocuments, selectedDocumentsByType])

  // Navigation functions
  const goToNextTab = useCallback(() => {
    if (currentTab === STEPS.SELECT) setCurrentTab(STEPS.ADDITIONAL)
    else if (currentTab === STEPS.ADDITIONAL) setCurrentTab(STEPS.STRATEGY)
    else if (currentTab === STEPS.STRATEGY) setCurrentTab(STEPS.REVIEW)
    else if (currentTab === STEPS.REVIEW) setCurrentTab(STEPS.GENERATE)
  }, [currentTab])

  const goToPrevTab = useCallback(() => {
    if (currentTab === STEPS.GENERATE) setCurrentTab(STEPS.REVIEW)
    else if (currentTab === STEPS.REVIEW) setCurrentTab(STEPS.STRATEGY)
    else if (currentTab === STEPS.STRATEGY) setCurrentTab(STEPS.ADDITIONAL)
    else if (currentTab === STEPS.ADDITIONAL) setCurrentTab(STEPS.SELECT)
  }, [currentTab])

  // Tab change handler with validation
  const handleTabChange = useCallback(
    (value: string) => {
      // Prevent navigation during generation or to restricted tabs
      if (isGenerating || isGeneratingLite) return
      if (value === STEPS.GENERATE && !step4Allowed) return
      setCurrentTab(value as (typeof STEPS)[keyof typeof STEPS])
    },
    [isGenerating, isGeneratingLite, step4Allowed]
  )

  // Validation check - ensure at least one required doc type is selected
  const areDocsProvided = useMemo(() => {
    return Object.values(selectedDocumentsByType).some(
      (docs) => docs.length > 0
    )
  }, [selectedDocumentsByType])

  // Handler for document generation
  const handleFinalizeUploads = useCallback(async () => {
    // Prevent multiple submissions
    if (isGenerating) return

    // Update UI state for generation process
    setGeneratedDocument(null)
    setIsGenerating(true)
    setStep4Allowed(true)
    setCurrentTab(STEPS.GENERATE)

    try {
      const result = await handleCaseEvaluationGeneration({
        caseId: caseEvaluation?.id || '',
        binderId: binder.id,
        selectedDocumentsByType,
        attorneyInsights: attorneyInsights
      })

      if (result.success && result.caseEvaluation?.revisedMarkdownReport) {
        router.refresh()
        setGeneratedDocument(result.caseEvaluation.revisedMarkdownReport)
        toast({
          title: 'Case Evaluation generated successfully',
          description: 'The Case Evaluation has been generated'
        })
      } else {
        throw new Error('Failed to generate Case Evaluation')
      }
    } catch (error) {
      // Reset UI state on error
      setStep4Allowed(false)
      setCurrentTab(STEPS.REVIEW)
      setGeneratedDocument(null)

      console.error('Error generating Case Evaluation:', error)
      toast({
        title: 'Failed to generate Case Evaluation',
        description: 'An error occurred while generating the Case Evaluation',
        variant: 'destructive'
      })
    } finally {
      setIsGenerating(false)
    }
  }, [binder.id, isGenerating, selectedDocumentsByType])

  // Handler for case evaluation lite generation
  const handleGenerateLite = useCallback(async () => {
    // Prevent multiple submissions
    if (isGeneratingLite || isGenerating) return

    // Update UI state for generation process
    setGeneratedDocument(null)
    setIsGeneratingLite(true)
    setStep4Allowed(true)
    setCurrentTab(STEPS.GENERATE)

    try {
      const result = await handleCaseEvaluationLiteGeneration({
        binderId: binder.id,
        selectedDocumentsByType
      })

      if (result.success && result.caseEvaluation?.content) {
        router.refresh()
        setGeneratedDocument(result.caseEvaluation.content)
        toast({
          title: 'Case Evaluation Lite generated successfully',
          description: 'The rapid case assessment has been completed'
        })
      } else {
        throw new Error('Failed to generate Case Evaluation Lite')
      }
    } catch (error) {
      // Reset UI state on error
      setStep4Allowed(false)
      setCurrentTab(STEPS.REVIEW)
      setGeneratedDocument(null)

      console.error('Error generating Case Evaluation Lite:', error)
      toast({
        title: 'Failed to generate Case Evaluation Lite',
        description: 'An error occurred while generating the rapid assessment',
        variant: 'destructive'
      })
    } finally {
      setIsGeneratingLite(false)
    }
  }, [
    binder.id,
    isGeneratingLite,
    isGenerating,
    selectedDocumentsByType,
    router
  ])

  // Handle saving changes to the generated document
  const handleSaveChanges = useCallback(async () => {
    if (!caseEvaluation?.id || !editedDocument) return

    setIsSaving(true)
    try {
      const result = await saveCaseEvaluationContent(
        caseEvaluation.id,
        editedDocument
      )

      if (result.success) {
        setGeneratedDocument(editedDocument)
        setHasUnsavedChanges(false)
        router.refresh()
        toast({
          title: 'Changes saved successfully',
          description: 'Your Case Evaluation has been updated'
        })
      } else {
        throw new Error('Failed to save changes')
      }
    } catch (error) {
      console.error('Error saving Case Evaluation:', error)
      toast({
        title: 'Failed to save changes',
        description: 'An error occurred while saving your changes',
        variant: 'destructive'
      })
    } finally {
      setIsSaving(false)
    }
  }, [caseEvaluation?.id, editedDocument, router])

  // Handle content changes from the editor
  const handleContentChange = useCallback(
    (newContent: string) => {
      setEditedDocument(newContent)
      setHasUnsavedChanges(newContent !== generatedDocument)
    },
    [generatedDocument]
  )

  const allowCaseEvaluationGeneration = usageStats.used < usageStats.available

  return (
    <div className="space-y-4">
      <Tabs
        value={currentTab}
        onValueChange={handleTabChange}
        className="mt-6 w-full"
      >
        <TabsList
          className="w-full justify-center gap-2"
          aria-label="Case Evaluation generation steps"
        >
          <TabsTrigger
            value={STEPS.SELECT}
            disabled={isGenerating || isGeneratingLite}
            aria-label="Step 1: Select Documents"
          >
            Step 1: Select Documents
          </TabsTrigger>
          <ArrowRightCircle className="w-5 h-5" aria-hidden="true" />
          <TabsTrigger
            value={STEPS.ADDITIONAL}
            disabled={isGenerating || isGeneratingLite}
            aria-label="Step 2: Additional Documents"
          >
            Step 2: Additional Docs
          </TabsTrigger>
          <ArrowRightCircle className="w-5 h-5" aria-hidden="true" />
          <TabsTrigger
            value={STEPS.STRATEGY}
            disabled={isGenerating || isGeneratingLite}
            aria-label="Step 3: Strategise"
          >
            Step 3: Strategise
          </TabsTrigger>
          <ArrowRightCircle className="w-5 h-5" aria-hidden="true" />
          <TabsTrigger
            value={STEPS.REVIEW}
            disabled={isGenerating || isGeneratingLite}
            aria-label="Step 4: Review and Finalize"
          >
            Step 4: Review &amp; Finalize
          </TabsTrigger>
          <ArrowRightCircle className="w-5 h-5" aria-hidden="true" />
          <TabsTrigger
            value={STEPS.GENERATE}
            disabled={!step4Allowed || isGenerating || isGeneratingLite}
            aria-label="Step 5: Generate"
          >
            Step 5: Generate{' '}
            {(isGenerating || isGeneratingLite) && (
              <Loader2
                className="ml-2 h-4 w-4 animate-spin"
                aria-hidden="true"
              />
            )}
          </TabsTrigger>
        </TabsList>

        {/* Step 1: Document Selection */}
        <TabsContent value={STEPS.SELECT} className="mt-4 space-y-4 w-full">
          <CaseDocumentSelector
            title="Select Documents"
            docTypes={DOCUMENT_TYPES}
            allDocuments={allDocuments}
            selectedDocumentsByType={selectedDocumentsByType}
            setSelectedDocumentsByType={setSelectedDocumentsByType}
          />
          <div className="flex justify-end mt-4">
            <Button onClick={goToNextTab}>Next Step</Button>
          </div>
        </TabsContent>

        {/* Step 2: Additional Documents */}
        <TabsContent value={STEPS.ADDITIONAL} className="mt-4 space-y-4">
          <CaseDocumentSelector
            title="Select Additional Docs"
            docTypes={['Additional Documents']}
            allDocuments={availableAdditionalDocuments}
            selectedDocumentsByType={selectedDocumentsByType}
            setSelectedDocumentsByType={setSelectedDocumentsByType}
          />
          <div className="flex justify-between mt-4">
            <Button variant="outline" onClick={goToPrevTab}>
              Previous Step
            </Button>
            <Button onClick={goToNextTab}>Next Step</Button>
          </div>
        </TabsContent>

        <TabsContent value={STEPS.STRATEGY} className="mt-4 space-y-4">
          <AttorneyInsightsForm
            caseId={binder.id}
            setValues={setAttorneyInsights}
            defaultValues={attorneyStrategyPlan}
          />
          <div className="flex justify-between mt-4">
            <Button variant="outline" onClick={goToPrevTab}>
              Previous Step
            </Button>
            <Button onClick={goToNextTab}>Next Step</Button>
          </div>
        </TabsContent>

        {/* Step 3: Review & Finalize */}
        <TabsContent value={STEPS.REVIEW} className="mt-4 space-y-4">
          <ReviewAndFinalizeUploads
            baseFiles={{
              'Medical Chronology': [
                `/dashboard/case/${binder.id}/medical-chronology`
              ]
            }}
            selectedDocumentsByType={selectedDocumentsByType}
            allDocuments={allDocuments}
            setSelectedDocumentsByType={setSelectedDocumentsByType}
          />

          {/* Case Evaluation Options */}
          <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg border border-slate-200 dark:border-slate-700">
            <h3 className="font-semibold text-sm mb-3 text-slate-900 dark:text-slate-100">
              Case Evaluation Options
            </h3>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded border border-blue-200 dark:border-blue-800">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                  Quick Evaluation
                  <span className="ml-2 text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-0.5 rounded">
                    Available
                  </span>
                </h4>
                <p className="text-blue-700 dark:text-blue-300 text-xs">
                  Quick evaluation without medical chronology requirement.
                  Provides Accept/Investigate/Decline recommendation in ~2
                  minutes.
                </p>
              </div>
              <div
                className={`p-3 rounded border ${
                  medicalChronologyExists
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                    : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                }`}
              >
                <h4
                  className={`font-medium mb-1 ${
                    medicalChronologyExists
                      ? 'text-green-800 dark:text-green-200'
                      : 'text-gray-600 dark:text-gray-400'
                  }`}
                >
                  Full Evaluation
                  <span
                    className={`ml-2 text-xs px-2 py-0.5 rounded ${
                      medicalChronologyExists
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200'
                        : 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200'
                    }`}
                  >
                    {medicalChronologyExists
                      ? 'Available'
                      : 'Requires Medical Chronology'}
                  </span>
                </h4>
                <p
                  className={`text-xs ${
                    medicalChronologyExists
                      ? 'text-green-700 dark:text-green-300'
                      : 'text-gray-600 dark:text-gray-400'
                  }`}
                >
                  Comprehensive analysis including damages calculation, risk
                  assessment, and litigation strategy.
                  {!medicalChronologyExists && (
                    <span className="font-medium">
                      Generate medical chronology first.
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-between mt-4">
            <Button variant="outline" onClick={goToPrevTab}>
              Previous Step
            </Button>
            <div className="flex gap-3">
              <Button
                variant="secondary"
                onClick={handleGenerateLite}
                disabled={
                  !areDocsProvided ||
                  isGenerating ||
                  isGeneratingLite ||
                  !allowCaseEvaluationGeneration
                }
                aria-busy={isGeneratingLite}
              >
                {!areDocsProvided
                  ? 'Documents Required'
                  : isGeneratingLite
                    ? 'Generating Analysis...'
                    : !allowCaseEvaluationGeneration
                      ? 'Out of Credits'
                      : 'Quick Case Evaluation'}
                {isGeneratingLite && (
                  <Loader2
                    className="ml-2 h-4 w-4 animate-spin"
                    aria-hidden="true"
                  />
                )}
              </Button>
              <Button
                onClick={handleFinalizeUploads}
                disabled={
                  !areDocsProvided ||
                  isGenerating ||
                  isGeneratingLite ||
                  !allowCaseEvaluationGeneration ||
                  !medicalChronologyExists
                }
                aria-busy={isGenerating}
                title={
                  !medicalChronologyExists
                    ? 'Medical chronology required for full evaluation'
                    : undefined
                }
              >
                {!areDocsProvided
                  ? 'Atleast 1 Document Required'
                  : isGenerating
                    ? 'Generating...'
                    : !allowCaseEvaluationGeneration
                      ? 'Out of Credits'
                      : !medicalChronologyExists
                        ? 'Medical Chronology Required'
                        : generatedDocument
                          ? 'Regenerate Case Evaluation'
                          : 'Generate Full Evaluation'}
                {isGenerating && (
                  <Loader2
                    className="ml-2 h-4 w-4 animate-spin"
                    aria-hidden="true"
                  />
                )}
              </Button>
            </div>
          </div>
        </TabsContent>

        {/* Step 4: Generation Results */}
        <TabsContent value={STEPS.GENERATE} className="mt-4 space-y-4">
          {generatedDocument && (
            <div className="flex justify-end gap-4 mt-4">
              <Button
                onClick={handleSaveChanges}
                disabled={isSaving || !hasUnsavedChanges}
                variant="outline"
              >
                {isSaving ? 'Saving...' : 'Save Changes'}
                {isSaving ? (
                  <Loader2
                    className="ml-2 h-4 w-4 animate-spin"
                    aria-hidden="true"
                  />
                ) : (
                  <Save className="ml-2 h-4 w-4" aria-hidden="true" />
                )}
              </Button>
              <CopyButton content={editedDocument || generatedDocument} />
              <DownloadButton
                content={editedDocument || generatedDocument}
                filename={`${binder.name} - Case Evaluation`}
                documents={allDocuments}
              />
            </div>
          )}

          {generatedDocument ? (
            <TipTapEditor
              content={generatedDocument}
              setContent={handleContentChange}
              refDocuments={allDocuments}
              isEditable={true}
            />
          ) : (
            <DocumentGeneration
              document={generatedDocument}
              feature="caseEvaluation"
            />
          )}
          {generatedDocument && (
            <div className="flex justify-center gap-4 mt-4">
              <Button
                variant="outline"
                onClick={() => setCurrentTab(STEPS.REVIEW)}
                disabled={isGenerating || isGeneratingLite}
              >
                Return to Review
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
