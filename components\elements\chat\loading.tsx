import { DashboardHeader } from '@/components/elements/layout/header'
import { ShellItem } from '@/components/elements/layout/shell-item'
import { DashboardShell } from '@/components/elements/layout/shell'

export default function DashboardLoading() {
  return (
    <DashboardShell>
      <DashboardHeader heading="Smart Counsel" text="Loading..." />
      <div className="divide-border-200 divide-y rounded-md border">
        <ShellItem.Skeleton />
        <ShellItem.Skeleton />
        <ShellItem.Skeleton />
        <ShellItem.Skeleton />
        <ShellItem.Skeleton />
      </div>
    </DashboardShell>
  )
}
