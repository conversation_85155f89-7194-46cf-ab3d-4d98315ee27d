import pdf from 'pdf-parse'
import mammoth from 'mammoth'
import { <PERSON><PERSON><PERSON> } from 'buffer'
import { developer, textToHTML } from './utils'
import { Readable } from 'stream'

export async function extractFileContentAndHtmlWithMammoth({
  file
}: {
  file: {
    buffer: Buffer
    type: string
    name: string
  }
}) {
  try {
    let htmlContent = ''
    let textContent = ''

    if (file.type === 'application/pdf') {
      console.time('Extracting text from PDF')
      const pdfData = await pdf(file.buffer)
      textContent = pdfData.text
      htmlContent = textToHTML(pdfData.text)
      console.timeEnd('Extracting text from PDF')
    } else if (
      file.type ===
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
      file.type === 'application/msword'
    ) {
      console.time('Extracting text from Word document')
      const docxHtml = await mammoth.convertToHtml({ buffer: file.buffer })
      htmlContent = docxHtml.value
      developer.log(docxHtml.messages)

      const docxText = await mammoth.extractRawText({ buffer: file.buffer })
      textContent = docxText.value
      developer.log(docxText.messages)
      console.timeEnd('Extracting text from Word document')
    }

    return { htmlContent, textContent }
  } catch (e: any) {
    console.log('Error in extractFileContentAndHtml', e.message)
    throw new Error('Failed to extract content from file')
  }
}

export async function streamToBuffer(stream: Readable): Promise<Buffer> {
  return new Promise<Buffer>((resolve, reject) => {
    const chunks: Uint8Array[] = []
    stream.on('data', (chunk: Uint8Array) => chunks.push(chunk))
    stream.on('end', () => resolve(Buffer.concat(chunks)))
    stream.on('error', reject)
  })
}

export async function fileToBuffer(file: File): Promise<{
  buffer: Buffer
  dataBuffer: ArrayBuffer
  type: string
  name: string
}> {
  console.time('Loading file into buffer')
  const dataBuffer = await file.arrayBuffer()
  const buffer = Buffer.from(dataBuffer)
  console.timeEnd('Loading file into buffer')
  return {
    dataBuffer,
    buffer,
    type: file.type,
    name: file.name
  }
}

export async function fileToUint8Array(
  file: File
): Promise<{ dataBuffer: Uint8Array; type: string; name: string }> {
  console.time('Loading file into buffer')

  const dataBuffer = await file.arrayBuffer()
  const uint8Array = new Uint8Array(dataBuffer)

  console.timeEnd('Loading file into buffer')

  return {
    dataBuffer: uint8Array,
    type: file.type,
    name: file.name
  }
}
