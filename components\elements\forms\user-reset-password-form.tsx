'use client'

import * as React from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { cn } from '@/lib/utils'
import { userResetPasswordSchema } from '@/lib/validations/auth'
import { buttonVariants } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { Icons } from '@/components/elements/icons'
import { updatePassword } from '@/lib/actions/user'
import { signIn } from 'next-auth/react'

interface UserResetPasswordFormProps
  extends React.HTMLAttributes<HTMLDivElement> {
  userId: string
}

type FormData = z.infer<typeof userResetPasswordSchema>

export function UserResetPasswordForm({
  className,
  ...props
}: UserResetPasswordFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zod<PERSON><PERSON><PERSON>ver(userResetPasswordSchema)
  })
  const [isLoading, setIsLoading] = React.useState<boolean>(false)

  async function onSubmit(data: FormData) {
    setIsLoading(true)
    try {
      const reset = await updatePassword({
        userId: props.userId,
        password: data.password
      })

      if (reset) {
        toast({
          title: 'Password Updated',
          description: 'Your password has been updated'
        })

        const signInResult = await signIn('credentials', {
          email: reset,
          password: data.password,
          redirect: false,
          callbackUrl: '/dashboard'
        })

        if (!signInResult?.ok) {
          throw new Error('Sign in failed.')
        }
      } else throw new Error('An error occurred while setting the password')
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An error occurred while setting the password',
        variant: 'destructive'
      })
    }
    setIsLoading(false)
  }

  return (
    <div className={cn('grid gap-6', className)} {...props}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid gap-2">
          <div className="grid gap-1">
            <Label htmlFor="password">Enter new password</Label>
            <Input
              id="password"
              type="password"
              autoCapitalize="none"
              autoCorrect="off"
              disabled={isLoading}
              {...register('password')}
            />
            {errors?.password && (
              <p className="px-1 text-xs text-red-600">
                {errors.password.message}
              </p>
            )}
          </div>

          <div className="grid gap-1">
            <Label htmlFor="confirmPassword">Confirm new password</Label>
            <Input
              id="confirmPassword"
              type="confirmPassword"
              autoCapitalize="none"
              autoCorrect="off"
              disabled={isLoading}
              {...register('confirmPassword')}
            />
            {errors?.confirmPassword && (
              <p className="px-1 text-xs text-red-600">
                {errors.confirmPassword.message}
              </p>
            )}
          </div>

          <button className={cn(buttonVariants())} disabled={isLoading}>
            {isLoading && (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            )}
            Update Password
          </button>
        </div>
      </form>
    </div>
  )
}
