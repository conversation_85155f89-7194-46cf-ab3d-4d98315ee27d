import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { caseBinderFeatures } from '@/config/dashboard'
import { db } from '@/lib/db'
import { PrivateUploadButton } from '@/components/elements/buttons/private-upload-button'
import MedicalChronologyDocSelector from '@/components/elements/doc-selector/medical-chronology-docs'
import { MedicalChronologyUploadInfoCard } from '@/components/elements/custom-components/feature-info-card'
import { Card } from '@/components/ui/card'
import { CaseFileType, CreditType } from '@prisma/client'
import {
  getFeatureUsageStats,
  getMasqueradeUserNonNullable
} from '@/lib/session'
import { fetchDocuments } from '@/lib/actions/case/fetch-documents'

export const metadata = caseBinderFeatures['medicalChronology']

interface MedicalChronologyMasqueradePageProps {
  params: {
    userId: string
    binderId: string
  }
}

export default async function MedicalChronologyMasqueradePage({
  params
}: MedicalChronologyMasqueradePageProps) {
  const user = await getMasqueradeUserNonNullable(params.userId)

  const usageStats = await getFeatureUsageStats({
    feature: CreditType.case,
    user
  })

  const binderPromise = db.binder.findUnique({
    where: {
      id: params.binderId,
      teamId: user.teamId
    }
  })

  const medicalChronologyPromise = db.caseFile.findUnique({
    where: {
      binderId_fileType: {
        binderId: params.binderId,
        fileType: CaseFileType.MEDICAL_CHRONOLOGY
      }
    }
  })

  const allDocumentsPromise = fetchDocuments(params.binderId)

  const [binder, medicalChronology, allDocuments] = await Promise.all([
    binderPromise,
    medicalChronologyPromise,
    allDocumentsPromise
  ])

  if (!binder) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Binder not found"
          text="This binder does not exist or you don't have access to it."
        />
      </DashboardShell>
    )
  }

  return (
    <DashboardShell>
      <DashboardHeader
        heading={metadata.title + ': Masquerade ' + user.name}
        text={metadata.description}
      >
        <PrivateUploadButton className="flex" binderId={params.binderId} />
      </DashboardHeader>
      <div className="grid gap-10">
        {allDocuments.length > 0 ? (
          <MedicalChronologyDocSelector
            usageStats={usageStats}
            binder={binder}
            allDocuments={allDocuments}
            medicalChronology={medicalChronology}
          />
        ) : (
          <Card>
            <MedicalChronologyUploadInfoCard />
          </Card>
        )}
      </div>
    </DashboardShell>
  )
}
