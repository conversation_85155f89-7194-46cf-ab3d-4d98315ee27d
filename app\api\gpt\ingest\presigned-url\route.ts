// app/api/gpt/ingest/presigned-url/route.ts

import { NextRequest, NextResponse } from 'next/server'
import { UnauthorizedError } from '@/lib/exceptions'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { s3UrlPut } from '@/lib/services/s3-service'
import { AWS_BUCKET } from '@/lib/services/s3-service'

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!(session && session.user)) {
    throw new UnauthorizedError()
  }

  try {
    const { fileName, fileType, fileSize } = await req.json()

    if (!fileName || !fileType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate file size
    const MAX_FILE_SIZE = 5242880 * 10 // 50 MB in bytes
    if (fileSize > MAX_FILE_SIZE) {
      return NextResponse.json(
        {
          error: 'File size exceeds the maximum allowed (50MB)'
        },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]

    if (!allowedTypes.includes(fileType)) {
      return NextResponse.json(
        {
          error:
            'File type not supported. Please upload PDF, DOC, or DOCX files only.'
        },
        { status: 400 }
      )
    }

    // Create a unique file key
    const teamId = session.user.teamId
    const ref =
      fileName
        .toLowerCase()
        .replace(/[\s\.!@#$%^&*()+\-=\[\]{};':"\\|,<>\/?]+/g, '_') +
      '_' +
      Date.now()
    const s3Key = `${teamId}/${ref}`

    // Generate presigned URL for the SECURE bucket
    const presignedUrl = await s3UrlPut(s3Key, fileType, AWS_BUCKET.secure)

    return NextResponse.json(
      {
        presignedUrl,
        s3Key,
        ref
      },
      { status: 200 }
    )
  } catch (e: any) {
    console.error('Error generating presigned URL:', e)
    return NextResponse.json(
      { error: e.message || 'Unknown error' },
      { status: 500 }
    )
  }
}
