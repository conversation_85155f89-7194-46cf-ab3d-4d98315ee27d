import { notFound } from 'next/navigation'
import { db } from '@/lib/db'
import { EditorNewsletter } from '@/components/elements/newsletter/editor-newsletter'
interface EditorPageProps {
  params: { newsletterId: string }
}

export default async function EditorPage({ params }: EditorPageProps) {
  const newsletter = await db.newsletter.findUnique({
    where: {
      id: params.newsletterId
    }
  })

  if (!newsletter) {
    notFound()
  }

  return (
    <EditorNewsletter
      newsletter={{
        id: newsletter.id,
        title: newsletter.title,
        content: newsletter.content,
        slug: newsletter.slug,
        published: newsletter.published
      }}
    />
  )
}
