import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { db } from '@/lib/db'
import { getMasqueradeUserNonNullable } from '@/lib/session'
import Link from 'next/link'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { formatDate } from '@/lib/utils'
import { Button } from '@/components/ui/button'

interface CaseMasqueradePageProps {
  params: {
    userId: string
  }
}

export default async function CaseMasqueradePage({
  params
}: CaseMasqueradePageProps) {
  const user = await getMasqueradeUserNonNullable(params.userId)

  const binders = await db.binder.findMany({
    where: {
      teamId: user.teamId
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 50
  })

  return (
    <DashboardShell>
      <DashboardHeader
        heading={'Case Binders: Masquerade ' + user.name}
        text="Select a binder to access case features"
      />
      <div className="grid gap-4">
        {binders.length > 0 ? (
          binders.map((binder) => (
            <Card key={binder.id}>
              <CardHeader>
                <CardTitle>{binder.name}</CardTitle>
                <CardDescription>
                  Created on {formatDate(binder.createdAt.toDateString())}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex gap-2">
                  <Link
                    href={`/admin/users/${params.userId}/masquerade/case/${binder.id}/medical-chronology`}
                  >
                    <Button variant="outline" size="sm">
                      Medical Chronology
                    </Button>
                  </Link>
                  <Link
                    href={`/admin/users/${params.userId}/masquerade/case/${binder.id}/case-evaluation`}
                  >
                    <Button variant="outline" size="sm">
                      Case Evaluation
                    </Button>
                  </Link>
                  <Link
                    href={`/admin/users/${params.userId}/masquerade/case/${binder.id}/demand-letter`}
                  >
                    <Button variant="outline" size="sm">
                      Demand Letter
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>No Binders Found</CardTitle>
              <CardDescription>
                This user has no case binders in their team.
              </CardDescription>
            </CardHeader>
          </Card>
        )}
      </div>
    </DashboardShell>
  )
}
