import type { NextApiRequest, NextApiResponse } from 'next'
import { db } from '@/lib/db'

export interface FreeLawCourtListenerOpinions {
  count: number
  next: string | null
  previous: string | null
  results: ResultsEntity[]
}
export interface ResultsEntity {
  resource_uri: string
  id: number
  absolute_url: string
  cluster_id: number
  cluster: string
  author_id: string | null
  author: string | null
  joined_by: string[]
  date_created: string
  date_modified: string
  author_str: string
  per_curiam: boolean
  joined_by_str: string
  type: string
  sha1: string
  page_count: number
  download_url: string
  local_path: string
  plain_text: string
  html: string
  html_lawbox: string
  html_columbia: string
  html_anon_2020: string
  xml_harvard: string
  html_with_citations: string
  extracted_by_ocr: boolean
  opinions_cited: string[]
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    let startDate = new Date('2022-09-01')
    let endDate = new Date('2024-07-31')
    let url: string | null = ''

    for (let i = startDate; i <= endDate; i.setMonth(i.getMonth() + 1)) {
      const startOfMonth = new Date(i.getFullYear(), i.getMonth(), 1)
        .toISOString()
        .slice(0, 10)

      const endOfMonth = new Date(i.getFullYear(), i.getMonth() + 1, 31)
        .toISOString()
        .slice(0, 10)

      url = `https://www.courtlistener.com/api/rest/v3/opinions/?date_created__gte=${startOfMonth}&date_created__lte=${endOfMonth}&cluster__docket__court=scotus`

      while (url) {
        console.log('\n\n\n\n >>>>> Fetching: ', url)

        const data = await fetch(url, {
          headers: {
            Authorization: `Token ${process.env.COURTLISTENER_API_KEY}`
          }
        })
        const jsonResponse: FreeLawCourtListenerOpinions = await data.json()

        url = jsonResponse.next

        if (!jsonResponse.results) {
          console.log('No results found', jsonResponse)
          break
        }

        for (const opinion of jsonResponse.results) {
          const validate = await db.documentRecords.findUnique({
            where: {
              source_ref: {
                source: 'courtlistener',
                ref: opinion.id.toString()
              }
            }
          })

          const data = {
            title: opinion.absolute_url,
            date: new Date(opinion.date_created),
            content: opinion.plain_text,
            meta: JSON.stringify({
              cluster: opinion.cluster,
              dateCreated: opinion.date_created,
              downloadUrl: opinion.download_url,
              absoluteUrl: opinion.absolute_url
            }),
            html: opinion.html,
            source: 'courtlistener',
            ref: opinion.id.toString(),
            url: opinion.resource_uri
          }
          let store = null
          if (validate) {
            store = await db.documentRecords.update({
              where: {
                id: validate.id
              },
              data
            })
            console.log('Data updated for: ', store.id, store.title, store.ref)
          } else {
            store = await db.documentRecords.create({ data })
            console.log('Data stored for: ', store.id, store.title, store.ref)
          }
        }
      }
    }
  } catch (error) {
    console.log(error)
    res.status(500).json({ error: 'Failed to fetch and parse the website.' })
  }
}
