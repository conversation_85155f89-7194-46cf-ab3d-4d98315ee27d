import { NextRequest, NextResponse } from 'next/server'
import { MemoryVectorStore } from 'langchain/vectorstores/memory'
import { ChatOpenAI, OpenAIEmbeddings } from '@langchain/openai'
import { StringOutputParser } from '@langchain/core/output_parsers'
import {
  ChatPromptTemplate,
  HumanMessagePromptTemplate,
  SystemMessagePromptTemplate
} from '@langchain/core/prompts'
import {
  RunnablePassthrough,
  RunnableSequence
} from '@langchain/core/runnables'
import type { Document } from 'langchain/document'
import { isJsonString } from '@/lib/utils'
import { db } from '@/lib/db'
import { z } from 'zod'
import { getCurrentUserResponse } from '@/lib/session'
import { TeamDocumentChunk } from '@prisma/client'
import { type VectorStoreRetriever } from '@langchain/core/vectorstores'
import { GPTModel } from '@/types'

const routeContextSchema = z.object({
  params: z.object({
    documentId: z.string()
  })
})

export const maxDuration = 800

export async function GET(
  req: NextRequest,
  context: z.infer<typeof routeContextSchema>
) {
  try {
    const { params } = routeContextSchema.parse(context)
    await getCurrentUserResponse()
    const data = await db.teamDocumentChunk.findMany({
      where: {
        teamDocumentId: params.documentId
      }
    })

    const ChunksData = data.map(
      (chunk) => chunk.indexed && chunk.review && JSON.parse(chunk.review)
    )

    const pendingChunks = data.filter((chunk) => !chunk.indexed)

    return new Response(
      JSON.stringify({ data: ChunksData, pending: pendingChunks })
    )
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}

export async function POST(
  req: NextRequest,
  context: z.infer<typeof routeContextSchema>
) {
  try {
    const start = Date.now()

    const { params } = routeContextSchema.parse(context)

    const data = await db.teamDocumentChunk.findMany({
      where: {
        teamDocumentId: params.documentId,
        indexed: false
      }
    })

    const model = new ChatOpenAI({
      modelName: GPTModel.GPT4Turbo,
      temperature: 0.2
    })

    const vectorStore = await MemoryVectorStore.fromTexts(
      data.map((chunk) => chunk.content),
      data.map((text, index) => ({ id: index })),
      new OpenAIEmbeddings()
    )

    const vectorStoreRetriever = vectorStore.asRetriever()

    const SYSTEM_TEMPLATE = `Title: Legal Document Review
    Use the following pieces of context to provide an array of pointers to enhance its consistency, integrity, and address any missing loopholes.
    ----------------
    {context}`

    const completion = await runParallelWithLimit(
      data,
      10,
      start,
      vectorStoreRetriever,
      SYSTEM_TEMPLATE,
      model
    )

    return new Response(JSON.stringify({ message: 'success', completion }))
  } catch (e: any) {
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}

export interface DocumentReviewResponse {
  topic: string
  review: string
  priority: string
  reason: string
}

function processChunk(
  chunk: TeamDocumentChunk,
  vectorStoreRetriever: VectorStoreRetriever<MemoryVectorStore>,
  systemTemplate: string,
  model: ChatOpenAI
): Promise<void> {
  return new Promise(async (resolve, reject) => {
    try {
      const messages = [
        SystemMessagePromptTemplate.fromTemplate(systemTemplate),
        HumanMessagePromptTemplate.fromTemplate('{question}')
      ]
      const prompt = ChatPromptTemplate.fromMessages(messages)

      const serializedDocs = (docs: Array<Document>) =>
        docs.map((doc) => doc.pageContent).join('\n\n')

      const chain = RunnableSequence.from([
        {
          context: vectorStoreRetriever.pipe(serializedDocs),
          question: new RunnablePassthrough()
        },
        prompt,
        model,
        new StringOutputParser()
      ])

      const answer = await chain.invoke(
        `
${chunk.content}
Your response should follow this format:
[{
  "topic": "area of review",
  "review": "review text as good | average | bad",
  "priority": "priority of review as high | medium | low",
  "reason": "reason for review"
  }]
Example:
[{
  "topic": "Clarity on judical authority",
  "review": "bad",
  "priority": "high",
  "reason": "The document does not clearly state the judicial authority."
  }]
There can be multiple entries in the array. If there's nothing prominent, return an empty array.
If you don't know the answer or if the document is no where related to legal context, return an empty array. The response has to be JSON parsable.`
      )
      const answerJson = isJsonString(answer) ? JSON.parse(answer) : []

      await db.teamDocumentChunk.update({
        where: { id: chunk.id },
        data: {
          review: JSON.stringify(answerJson),
          indexed: isJsonString(answer)
        }
      })
      resolve()
    } catch (error) {
      reject(error)
    }
  })
}

async function runParallelWithLimit(
  data: TeamDocumentChunk[],
  limit: number,
  startTime: number,
  vectorStoreRetriever: VectorStoreRetriever<MemoryVectorStore>,
  systemTemplate: string,
  model: ChatOpenAI
): Promise<boolean> {
  let completed = true
  const chunks = []
  for (let i = 0; i < data.length; i += limit) {
    chunks.push(data.slice(i, i + limit))
  }

  for (const chunkGroup of chunks) {
    await Promise.all(
      chunkGroup.map((chunk) =>
        processChunk(chunk, vectorStoreRetriever, systemTemplate, model)
      )
    )

    const now = Date.now()
    const duration = now - startTime
    if (duration > 30 * 1000) {
      completed = false
      break
    }
  }
  return completed
}
