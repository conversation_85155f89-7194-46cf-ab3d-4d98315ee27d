import { Region } from '@prisma/client'
import * as z from 'zod'

export const userAuthSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8).max(100)
})

export const userForgotPasswordSchema = z.object({
  email: z.string().email()
})

const passwordRegex = /^(?=.*[0-9])(?=.*[!@#$%^&*])/

export const userResetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, {
        message: 'Password must be at least 8 characters'
      })
      .max(30, {
        message: 'Password must be at most 30 characters'
      })
      .regex(passwordRegex, {
        message:
          'Password must contain at least one number and one special character'
      }),
    confirmPassword: z.string()
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Confirm password doesn't match with password",
    path: ['confirmPassword']
  })

export const userRegisterSchema = z.object({
  name: z.string().min(3).max(100),
  email: z.string().email(),
  password: z.string().min(8).max(100)
})

export const userWelcomeSchema = z.object({
  name: z.string().min(3).max(100),
  teamName: z.string().min(3).max(100),
  region: z.enum([Region.US, Region.IN])
})
