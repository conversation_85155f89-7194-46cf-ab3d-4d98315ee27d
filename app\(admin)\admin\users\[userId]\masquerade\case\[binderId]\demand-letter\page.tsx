import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { caseBinderFeatures } from '@/config/dashboard'
import { db } from '@/lib/db'
import { CaseFileType, CreditType } from '@prisma/client'
import { redirect } from 'next/navigation'
import DemandLetterGenerator from '@/components/elements/doc-selector/demand-letter-generator'
import {
  getFeatureUsageStats,
  getMasqueradeUserNonNullable
} from '@/lib/session'

export const metadata = caseBinderFeatures['demandLetterGeneration']

interface DemandLetterMasqueradePageProps {
  params: {
    userId: string
    binderId: string
  }
}

export default async function DemandLetterMasqueradePage({
  params
}: DemandLetterMasqueradePageProps) {
  const user = await getMasqueradeUserNonNullable(params.userId)

  const usageStats = await getFeatureUsageStats({
    feature: CreditType.case,
    user
  })

  const binderPromise = db.binder.findUnique({
    where: {
      id: params.binderId,
      teamId: user.teamId
    }
  })

  const caseEval = await db.caseFile.findUnique({
    where: {
      binderId_fileType: {
        binderId: params.binderId,
        fileType: CaseFileType.CASE_EVALUATION
      }
    }
  })

  const dataSourcePromise = db.dataset.findMany({
    where: {
      OR: [
        {
          createdBy: params.binderId
        },
        {
          binderId: params.binderId
        }
      ]
    },
    select: {
      DocumentRecordDatasetMap: {
        select: {
          DocumentRecords: {
            select: {
              id: true,
              title: true,
              indexed: true
            }
          }
        }
      }
    }
  })

  if (!caseEval) {
    redirect(
      `/admin/users/${params.userId}/masquerade/case/${params.binderId}/case-evaluation`
    )
  }

  const demandLetterPromise = db.caseFile.findUnique({
    where: {
      binderId_fileType: {
        binderId: params.binderId,
        fileType: CaseFileType.DEMAND_LETTER
      }
    }
  })

  const [binder, demandLetter, dataSource] = await Promise.all([
    binderPromise,
    demandLetterPromise,
    dataSourcePromise
  ])

  if (!binder) {
    return (
      <DashboardShell>
        <DashboardHeader
          heading="Binder not found"
          text="This binder does not exist or you don't have access to it."
        />
      </DashboardShell>
    )
  }

  const documents = dataSource
    .map((dataSet) =>
      dataSet.DocumentRecordDatasetMap.map((doc) => {
        return {
          id: doc.DocumentRecords.id,
          title: doc.DocumentRecords.title,
          indexed: doc.DocumentRecords.indexed
        }
      })
    )
    .flat()

  return (
    <DashboardShell>
      <DashboardHeader
        heading={metadata.title + ': Masquerade ' + user.name}
        text={metadata.description}
      />
      <div className="grid gap-10">
        <DemandLetterGenerator
          usageStats={usageStats}
          binder={binder}
          demandLetter={demandLetter}
          refDocuments={documents}
        />
      </div>
    </DashboardShell>
  )
}
