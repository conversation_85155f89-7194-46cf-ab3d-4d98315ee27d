(function() {
    // Retrieve or initialize scraped data
    let pageCount = parseInt(localStorage.getItem('scrapedPageCount') || '0', 10);
    let allMembers = JSON.parse(localStorage.getItem('allMembers') || '[]');

    // Function to extract member data from the current page
    function extractMembers() {
        const members = [];
        const entries = document.querySelectorAll('.ShortListing3');

        entries.forEach(entry => {
            const nameElement = entry.querySelector('a b');
            const name = nameElement ? nameElement.innerText.trim() : "N/A";

            const textContent = entry.innerText;
            const phoneMatch = textContent.match(/Phone:\s*([\d()\- .]+)/i);
            const phone = phoneMatch ? phoneMatch[1].trim() : "N/A";

            const emailMatch = textContent.match(/E-mail:\s*([\w\-.]+@[\w\-.]+\.\w+)/i);
            const email = emailMatch ? emailMatch[1].trim() : "N/A";

            // Extract Address: Assume it's between the name and "Phone:"
            const lines = entry.innerText.split('\n').map(l => l.trim()).filter(l => l !== "");
            const addressLines = lines.filter(line => line !== name && !line.startsWith("Phone:") && !line.startsWith("E-mail:"));
            const address = addressLines.join(", ");

            members.push({ name, phone, email, address });
        });

        return members;
    }

    // Extract and save data from the current page
    const pageMembers = extractMembers();
    allMembers = allMembers.concat(pageMembers);
    pageCount++;

    console.log(`Scraped page ${pageCount}. Total members so far: ${allMembers.length}`);

    // Store updated data in localStorage
    localStorage.setItem('scrapedPageCount', pageCount);
    localStorage.setItem('allMembers', JSON.stringify(allMembers));

    // Stop after 15 pages
    if (pageCount >= 15) {
        console.log("✅ Reached 15-page limit. Scraping complete!");
        console.table(allMembers);
        localStorage.removeItem('scrapedPageCount');
        localStorage.removeItem('allMembers');
        return;
    }

    // Find and click the "Next Page" button
    const nextPageLinkImg = document.querySelector("img[alt='Next Page']");
    if (nextPageLinkImg) {
        const nextPageAnchor = nextPageLinkImg.closest('a');
        if (nextPageAnchor) {
            console.log(`🔄 Navigating to next page: ${nextPageAnchor.href}`);
            setTimeout(() => {
                nextPageAnchor.click(); // Click the button
            }, 2000);
        } else {
            console.log("⚠️ Next Page link not found.");
            console.table(allMembers);
        }
    } else {
        console.log("⛔ No 'Next Page' button found. Ending scraping.");
        console.table(allMembers);
    }
})();
