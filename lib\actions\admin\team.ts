'use server'

import { db } from '@/lib/db'

export async function deleteTeamWithAllRecords(teamId: string): Promise<{
  success: boolean
  message: string
  counts?: string
  summary?: any
}> {
  try {
    // First, collect counts of all records that will be deleted
    const counts = {
      teamCreditUsed: 0,
      teamPeriodicCredit: 0,
      teamDocuments: 0,
      teamDocumentChunks: 0,
      binders: 0,
      caseFiles: 0,
      researchStores: 0,
      documentEvents: 0,
      processedChronologies: 0,
      datasets: 0,
      showCauseNotices: 0,
      users: 0,
      userAccounts: 0,
      userSessions: 0,
      userSettings: 0
    }

    // Count team credit usage records
    counts.teamCreditUsed = await db.teamCreditUsed.count({
      where: { teamId }
    })

    // Count team periodic credits
    counts.teamPeriodicCredit = await db.teamPeriodicCredit.count({
      where: { teamId }
    })

    // Count team documents
    counts.teamDocuments = await db.teamDocument.count({
      where: { teamId }
    })

    // Count team document chunks
    const teamDocuments = await db.teamDocument.findMany({
      where: { teamId }
    })
    for (const doc of teamDocuments) {
      const chunkCount = await db.teamDocumentChunk.count({
        where: { teamDocumentId: doc.id }
      })
      counts.teamDocumentChunks += chunkCount
    }

    // Count binders and their related data
    const binders = await db.binder.findMany({
      where: { teamId }
    })
    counts.binders = binders.length

    for (const binder of binders) {
      counts.caseFiles += await db.caseFile.count({
        where: { binderId: binder.id }
      })
      counts.researchStores += await db.researchStore.count({
        where: { binderId: binder.id }
      })
      counts.documentEvents += await db.documentEvent.count({
        where: { binderId: binder.id }
      })
      counts.processedChronologies += await db.processedChronology.count({
        where: { binderId: binder.id }
      })
      counts.datasets += await db.dataset.count({
        where: { binderId: binder.id }
      })
    }

    // Count show cause notices
    counts.showCauseNotices = await db.showCauseNotice.count({
      where: { teamId }
    })

    // Count team members and their data
    const teamMembers = await db.user.findMany({
      where: { teamId }
    })
    counts.users = teamMembers.length

    for (const user of teamMembers) {
      counts.userAccounts += await db.account.count({
        where: { userId: user.id }
      })
      counts.userSessions += await db.session.count({
        where: { userId: user.id }
      })
      counts.userSettings += await db.userSettings.count({
        where: { userId: user.id }
      })
    }

    // Start a transaction to ensure all deletions happen together
    await db.$transaction(async (tx) => {
      // Delete all team credit usage records
      await tx.teamCreditUsed.deleteMany({
        where: {
          teamId
        }
      })

      // Delete all team periodic credits
      await tx.teamPeriodicCredit.deleteMany({
        where: {
          teamId
        }
      })

      // Delete all team documents and their chunks
      const teamDocuments = await tx.teamDocument.findMany({
        where: { teamId }
      })

      // Delete all team document chunks first
      for (const doc of teamDocuments) {
        await tx.teamDocumentChunk.deleteMany({
          where: {
            teamDocumentId: doc.id
          }
        })
      }

      // Delete all team documents
      await tx.teamDocument.deleteMany({
        where: {
          teamId
        }
      })

      // Delete all binders and their related data
      const binders = await tx.binder.findMany({
        where: { teamId }
      })

      for (const binder of binders) {
        // Delete case files associated with binders
        await tx.caseFile.deleteMany({
          where: {
            binderId: binder.id
          }
        })

        // Delete research stores associated with binders
        await tx.researchStore.deleteMany({
          where: {
            binderId: binder.id
          }
        })

        // Delete document events associated with binders
        await tx.documentEvent.deleteMany({
          where: {
            binderId: binder.id
          }
        })

        // Delete processed chronologies associated with binders
        await tx.processedChronology.deleteMany({
          where: {
            binderId: binder.id
          }
        })

        // Delete datasets associated with binders
        await tx.dataset.deleteMany({
          where: {
            binderId: binder.id
          }
        })
      }

      // Delete all binders associated with the team
      await tx.binder.deleteMany({
        where: {
          teamId
        }
      })

      // Delete all show cause notices associated with the team
      await tx.showCauseNotice.deleteMany({
        where: {
          teamId
        }
      })

      // Delete all team members and their associated data
      const teamMembers = await tx.user.findMany({
        where: {
          teamId
        }
      })

      for (const user of teamMembers) {
        // Delete user's accounts
        await tx.account.deleteMany({
          where: {
            userId: user.id
          }
        })

        // Delete user's sessions
        await tx.session.deleteMany({
          where: {
            userId: user.id
          }
        })

        // Delete user's settings
        await tx.userSettings.deleteMany({
          where: {
            userId: user.id
          }
        })
      }

      // Delete all team members
      await tx.user.deleteMany({
        where: {
          teamId
        }
      })

      // Finally, delete the team itself
      await tx.team.delete({
        where: {
          id: teamId
        }
      })
    })

    const totalRecords = Object.values(counts).reduce(
      (sum, count) => sum + count,
      0
    )

    return {
      success: true,
      message: `Team deleted successfully! Removed ${totalRecords} records total.`,
      counts: JSON.stringify(counts),
      summary: {
        total: totalRecords,
        users: counts.users,
        binders: counts.binders,
        documents: counts.teamDocuments,
        caseFiles: counts.caseFiles,
        researchStores: counts.researchStores
      }
    }
  } catch (error: any) {
    console.error('Error deleting team:', error)
    return {
      success: false,
      message: error.message || 'An error occurred while deleting the team'
    }
  }
}
