import { redirect } from 'next/navigation'

import { authOptions } from '@/lib/auth'
import { getCurrentUser } from '@/lib/session'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { DocumentReview } from '@/components/elements/document/document-review'
import { db } from '@/lib/db'
import { EmptyPlaceholder } from '@/components/elements/custom-components/empty-placeholder'
import { DocumentItem } from '@/components/elements/document/document-item'
import { features } from '@/config/dashboard'

export const metadata = features['documentReview']

export default async function DocumentReviewRoot() {
  const user = await getCurrentUser()

  if (!user) {
    redirect(authOptions?.pages?.signIn || '/login')
  }

  const documents = await db.teamDocument.findMany({
    where: {
      teamId: user.teamId
    }
  })

  return (
    <DashboardShell>
      <DashboardHeader heading={metadata.title} text={metadata.description} />
      <div className="grid gap-10">
        <DocumentReview endpoint="/api/gpt/review" />
      </div>

      <div>
        {documents?.length ? (
          <div className="divide-y divide-border rounded-md border bg-white dark:bg-slate-950">
            {documents.map((document) => (
              <DocumentItem key={document.id} document={document} />
            ))}
          </div>
        ) : (
          <EmptyPlaceholder>
            <EmptyPlaceholder.Icon name="post" />
            <EmptyPlaceholder.Title>
              No Documents Reviewed
            </EmptyPlaceholder.Title>
            <EmptyPlaceholder.Description>
              You don&apos;t have any documents yet. Start by reviewing a
              document.
            </EmptyPlaceholder.Description>
          </EmptyPlaceholder>
        )}
      </div>
    </DashboardShell>
  )
}
