import type {
  ExtractedCaseFinancialData,
  ExtractedFinancialData,
  ExtractedMedicalExpense,
  ExtractedWageLoss,
  NonEconomicDamagesMetadata,
  PunitiveDamagesMetadata
} from './financial-extraction-types'
import {
  EconomicDamages,
  NonEconomicDamages,
  PunitiveDamagesData,
  DamagesCalculation
} from '@/types/case'

// Updated constants for damage calculations to match Excel sheet
const NON_ECONOMIC_MULTIPLIER_RANGES = {
  Mild: { min: 1.5, default: 1.75, max: 2.0 }, // Minor injury in Excel
  Moderate: { min: 2.0, default: 2.5, max: 3.0 },
  Severe: { min: 3.0, default: 3.5, max: 4.0 },
  Catastrophic: { min: 4.0, default: 4.5, max: 5.0 } // 5+ in Excel
}

// Keep duration multiplier adjustment for compatibility
const DURATION_MULTIPLIER_ADJUSTMENT = {
  Temporary: 0.7,
  'Long-term': 1.0,
  Permanent: 1.3
}

// Default multipliers for other non-economic damages
const EMOTIONAL_DISTRESS_MULTIPLIERS = {
  Mild: 1.75, // 1.5-2 in Excel
  Moderate: 2.5, // 2-3 in Excel
  Severe: 3.5, // 3-4 in Excel
  Extreme: 4.5 // 4-5+ in Excel
}

const LOSS_OF_ENJOYMENT_MULTIPLIERS = {
  Mild: 1.75, // 1.5-2 in Excel
  Moderate: 2.5, // 2-3 in Excel
  Severe: 3.5, // 3-4 in Excel
  Total: 4.5 // 4-5+ in Excel
}

const LOSS_OF_CONSORTIUM_MULTIPLIERS = {
  Temporary: 1.25, // 1-1.5 in Excel
  Significant: 2.0, // 1.5-2.5 in Excel
  Complete: 2.75 // 2.5-3+ in Excel
}

const DISFIGUREMENT_MULTIPLIERS = {
  Mild: 2.5, // 2-3 in Excel
  Moderate: 4.0, // 3-5 in Excel
  Severe: 6.0, // 5-7 in Excel
  Catastrophic: 8.0 // 7+ in Excel
}

// Updated punitive damages multiplier to match Excel
const PUNITIVE_MULTIPLIER_RANGES = {
  Negligent: 1.5, // 1-2 in Excel
  Reckless: 2.5, // 2-3 in Excel
  Intentional: 4.0, // 3-5 in Excel
  Malicious: 6.0 // 5+ in Excel
}

const PUNITIVE_DEFAULT_MULTIPLIER = 2.0 // Updated from 0.5 to align with Excel

/**
 * Calculate economic damages based on extracted financial data
 */
export function calculateEconomicDamages(
  financialData: ExtractedFinancialData
): EconomicDamages {
  // Calculate past medical expenses
  const pastMedical = financialData.medicalExpenses
    .filter((expense) => !expense.isFutureExpense)
    .reduce((sum, expense) => sum + expense.amount, 0)

  // Calculate future medical expenses
  const futureMedical = financialData.medicalExpenses
    .filter((expense) => expense.isFutureExpense)
    .reduce((sum, expense) => sum + expense.amount, 0)

  // Calculate past lost wages
  const pastWages = financialData.wageLosses
    .filter((wage) => !wage.isFutureWageLoss)
    .reduce((sum, wage) => sum + wage.totalAmount, 0)

  // Calculate future lost wages
  const futureWages = financialData.wageLosses
    .filter((wage) => wage.isFutureWageLoss)
    .reduce((sum, wage) => sum + wage.totalAmount, 0)

  // Calculate property damage
  const propertyDamage = financialData.propertyDamages.reduce(
    (sum, damage) => sum + damage.totalAmount,
    0
  )

  const householdServiceLoss = (
    financialData.householdServiceLosses ?? []
  ).reduce((sum, loss) => sum + loss.netLoss, 0)

  // Calculate other economic losses
  const otherLosses: { [key: string]: number } = {}

  financialData.otherExpenses.forEach((expense) => {
    if (!otherLosses[expense.category]) {
      otherLosses[expense.category] = 0
    }
    otherLosses[expense.category] += expense.amount
  })

  // Generate medical details
  const medicalDetails = generateMedicalDetails(financialData.medicalExpenses)

  // Generate wage loss details
  const wageDetails = generateWageLossDetails(financialData.wageLosses)

  // Calculate total
  const total =
    pastMedical +
    futureMedical +
    pastWages +
    futureWages +
    propertyDamage +
    householdServiceLoss +
    Object.values(otherLosses).reduce((sum, amount) => sum + amount, 0)

  return {
    medicalExpenses: {
      past: pastMedical,
      future: futureMedical,
      details: medicalDetails
    },
    lostWages: {
      past: pastWages,
      future: futureWages,
      details: wageDetails
    },
    propertyDamage,
    householdServiceLoss,
    otherEconomicLosses: otherLosses,
    total
  }
}

/**
 * Calculate non-economic damages based on economic damages and metadata
 * Updated to match Excel sheet's multiplier approach
 */
export function calculateNonEconomicDamages(
  economicDamages: EconomicDamages,
  metadata: NonEconomicDamagesMetadata,
  multiplierOverride?: number
): NonEconomicDamages {
  // Determine base multiplier based on pain severity
  const severityMultiplier =
    NON_ECONOMIC_MULTIPLIER_RANGES[metadata.painAndSuffering.severity].default

  // Apply duration adjustment if needed
  const durationAdjustment =
    DURATION_MULTIPLIER_ADJUSTMENT[metadata.painAndSuffering.duration]

  // Calculate adjusted multiplier (can be overridden)
  const multiplier =
    multiplierOverride ?? severityMultiplier * durationAdjustment

  // Calculate pain and suffering using multiplier method (as in Excel)
  const painAndSufferingAmount = economicDamages.total * multiplier

  // Initialize other non-economic damages
  let emotionalDistressAmount = 0
  let lossOfEnjoymentAmount = 0
  let disfigurementAmount = 0
  let lossOfConsortiumAmount = 0

  // Calculate emotional distress if applicable - using multiplier approach from Excel
  if (metadata.emotionalDistress) {
    // Determine severity or use a default multiplier based on diagnosis
    let emotionalMultiplier = 2.0 // Default for moderate distress

    // Determine multiplier based on diagnosis and treatment
    if (
      metadata.emotionalDistress.professionallyDiagnosed &&
      metadata.emotionalDistress.treatmentRequired
    ) {
      emotionalMultiplier = 3.0 // Severe
    } else if (metadata.emotionalDistress.professionallyDiagnosed) {
      emotionalMultiplier = 2.0 // Moderate
    } else {
      emotionalMultiplier = 1.5 // Mild
    }

    emotionalDistressAmount = economicDamages.total * emotionalMultiplier
  }

  // Calculate loss of enjoyment if applicable - using multiplier approach
  if (
    metadata.lossOfEnjoyment &&
    metadata.lossOfEnjoyment.affectedActivities.length > 0
  ) {
    // Determine appropriate multiplier based on number of affected activities
    const activitiesCount = metadata.lossOfEnjoyment.affectedActivities.length
    let enjoymentMultiplier = 1.5 // Default (mild)

    if (activitiesCount > 10) {
      enjoymentMultiplier = 4.5 // Total loss
    } else if (activitiesCount > 5) {
      enjoymentMultiplier = 3.5 // Severe
    } else if (activitiesCount > 2) {
      enjoymentMultiplier = 2.5 // Moderate
    }

    lossOfEnjoymentAmount = economicDamages.total * enjoymentMultiplier
  }

  // Calculate disfigurement if applicable - using multiplier approach
  if (metadata.disfigurement) {
    // Determine appropriate multiplier based on visibility and permanence
    let disfigurementMultiplier = 2.5 // Default (mild)

    if (
      metadata.disfigurement.visibility === 'Visible' &&
      metadata.disfigurement.permanence === 'Permanent'
    ) {
      disfigurementMultiplier = 6.0 // Severe
    } else if (metadata.disfigurement.visibility === 'Visible') {
      disfigurementMultiplier = 4.0 // Moderate
    } else if (metadata.disfigurement.permanence === 'Permanent') {
      disfigurementMultiplier = 4.0 // Moderate
    }

    disfigurementAmount = economicDamages.total * disfigurementMultiplier
  }

  // Calculate total non-economic damages
  const total =
    painAndSufferingAmount +
    emotionalDistressAmount +
    lossOfEnjoymentAmount +
    disfigurementAmount +
    lossOfConsortiumAmount

  // Compose result
  const result: NonEconomicDamages = {
    painAndSuffering: {
      multiplier,
      amount: painAndSufferingAmount,
      justification: `Based on ${metadata.painAndSuffering.severity} severity and ${metadata.painAndSuffering.duration?.toLowerCase()} duration of pain and suffering.`
    },
    emotionalDistress: {
      amount: emotionalDistressAmount,
      justification: `Based on emotional distress description ${metadata.emotionalDistress?.description}.`
    },
    lossOfEnjoyment: {
      amount: lossOfEnjoymentAmount,
      justification: `Based on loss of enjoyment due to affected activities ${metadata.lossOfEnjoyment?.affectedActivities}`
    },
    disfigurement: {
      amount: disfigurementAmount,
      justification: `Based on ${metadata.disfigurement?.visibility} visibility and ${metadata.disfigurement?.permanence} duration of disfigurement.`
    },
    total
  }

  // Add optional fields if applicable
  if (emotionalDistressAmount > 0) {
    result.emotionalDistress = {
      amount: emotionalDistressAmount,
      justification: `Based on ${metadata.emotionalDistress!.professionallyDiagnosed ? 'professionally diagnosed' : 'reported'} emotional distress requiring ${metadata.emotionalDistress!.treatmentRequired ? 'professional treatment' : 'no professional treatment'}.`
    }
  }

  if (lossOfEnjoymentAmount > 0) {
    result.lossOfEnjoyment = {
      amount: lossOfEnjoymentAmount,
      justification: `Based on inability to participate in ${metadata.lossOfEnjoyment!.affectedActivities.length} previously enjoyed activities.`
    }
  }

  if (disfigurementAmount > 0) {
    result.disfigurement = {
      amount: disfigurementAmount,
      justification: `Based on ${metadata.disfigurement!.permanence?.toLowerCase()} disfigurement that is ${metadata.disfigurement!.visibility === 'Visible' ? 'visible in normal attire' : 'not visible in normal attire'}.`
    }
  }

  return result
}

/**
 * Calculate punitive damages if applicable
 * Updated to match Excel sheet's higher multiplier ranges
 */
export function calculatePunitiveDamages(
  economicTotal: number,
  nonEconomicTotal: number,
  metadata?: PunitiveDamagesMetadata,
  multiplierOverride?: number
): PunitiveDamagesData {
  // Determine eligibility based on metadata
  const isEligible = metadata && metadata.eligibilityFactors.length > 0

  if (!isEligible) {
    return {
      eligibility: false,
      justification: 'No evidence of conduct warranting punitive damages.'
    }
  }

  // Use override or default multiplier
  const multiplier = multiplierOverride ?? PUNITIVE_DEFAULT_MULTIPLIER

  // Calculate amount based on total compensatory damages (economic + non-economic)
  const baseAmount = economicTotal + nonEconomicTotal
  const punitiveDamagesAmount = baseAmount * multiplier

  return {
    eligibility: true,
    justification: `Eligible based on: ${metadata!.eligibilityFactors.join(', ')}`,
    recommendedMultiplier: multiplier,
    estimatedAmount: punitiveDamagesAmount
  }
}

/**
 * Calculate final damages with adjustment for comparative fault
 */
export function calculateTotalDamages(
  economicDamages: EconomicDamages,
  nonEconomicDamages: NonEconomicDamages,
  punitiveDamagesData: PunitiveDamagesData,
  liabilityDistribution?: {
    plaintiffPercentage: number
    defendantPercentage: number
    otherPercentage?: number
  }
): DamagesCalculation {
  const economicTotal = economicDamages.total
  const nonEconomicTotal = nonEconomicDamages.total
  const punitiveTotal = punitiveDamagesData.eligibility
    ? punitiveDamagesData.estimatedAmount || 0
    : 0

  // Calculate total damages before fault adjustment
  const totalBeforeFault = economicTotal + nonEconomicTotal + punitiveTotal

  // Calculate reduction based on plaintiff's fault percentage
  let reductionForComparativeFault = 0
  if (liabilityDistribution && liabilityDistribution.plaintiffPercentage > 0) {
    reductionForComparativeFault =
      totalBeforeFault * (liabilityDistribution.plaintiffPercentage / 100)
  }

  // Calculate final damages
  const totalAfterFault = totalBeforeFault - reductionForComparativeFault

  // Create a reasonable range for settlement/verdict estimates
  const lowEstimate = Math.round(totalAfterFault * 0.7)
  const highEstimate = Math.round(totalAfterFault * 1.3)

  return {
    economic: economicTotal,
    nonEconomic: nonEconomicTotal,
    punitive: punitiveTotal > 0 ? punitiveTotal : undefined,
    total: totalBeforeFault,
    reductionForComparativeFault:
      reductionForComparativeFault > 0
        ? reductionForComparativeFault
        : undefined,
    finalEstimate: {
      low: lowEstimate,
      expected: totalAfterFault,
      high: highEstimate
    }
  }
}

/**
 * Generate a detailed description of medical expenses
 */
function generateMedicalDetails(
  medicalExpenses: ExtractedMedicalExpense[]
): string {
  const pastCategories: { [key: string]: number } = {}
  const futureCategories: { [key: string]: number } = {}

  // Group expenses by category
  medicalExpenses.forEach((expense) => {
    const targetCategories = expense.isFutureExpense
      ? futureCategories
      : pastCategories
    if (!targetCategories[expense.category]) {
      targetCategories[expense.category] = 0
    }
    targetCategories[expense.category] += expense.amount
  })

  let details = 'Past medical expenses include '
  details += Object.entries(pastCategories)
    .map(([category, amount]) => `${category} ($${amount.toLocaleString()})`)
    .join(', ')

  if (Object.keys(futureCategories).length > 0) {
    details += '. Future medical expenses include '
    details += Object.entries(futureCategories)
      .map(([category, amount]) => `${category} ($${amount.toLocaleString()})`)
      .join(', ')
  }

  details += '.'
  return details
}

/**
 * Generate a detailed description of wage losses
 */
function generateWageLossDetails(wageLosses: ExtractedWageLoss[]): string {
  const pastLosses = wageLosses.filter((wage) => !wage.isFutureWageLoss)
  const futureLosses = wageLosses.filter((wage) => wage.isFutureWageLoss)

  let details = ''

  if (pastLosses.length > 0) {
    const totalDays = pastLosses.reduce((days, loss) => {
      if (loss.startDate && loss.endDate) {
        const start = new Date(loss.startDate)
        const end = new Date(loss.endDate)
        const dayDiff = Math.ceil(
          (end.getTime() - start.getTime()) / (1000 * 3600 * 24)
        )
        return days + dayDiff
      }
      return days
    }, 0)

    details += `Client missed approximately ${totalDays} days of work with a total past wage loss of $${pastLosses.reduce((sum, loss) => sum + loss.totalAmount, 0).toLocaleString()}.`
  }

  if (futureLosses.length > 0) {
    if (details) details += ' '
    details += `Future lost wages of $${futureLosses.reduce((sum, loss) => sum + loss.totalAmount, 0).toLocaleString()} account for projected earning capacity reduction.`
  }

  return details
}

/**
 * Main function to orchestrate the extraction and calculation process
 */
export function processAndCalculateDamages(
  extractedData: ExtractedCaseFinancialData,
  nonEconomicMultiplierOverride?: number,
  punitiveMultiplierOverride?: number
) {
  // Step 1: Calculate economic damages
  const economicDamages = calculateEconomicDamages(extractedData.financialData)

  // Step 2: Calculate non-economic damages
  const nonEconomicDamages = calculateNonEconomicDamages(
    economicDamages,
    extractedData.nonEconomicMetadata,
    nonEconomicMultiplierOverride
  )

  // Step 3: Calculate punitive damages if applicable
  const punitiveDamagesData = calculatePunitiveDamages(
    economicDamages.total,
    nonEconomicDamages.total,
    extractedData.punitiveDamagesMetadata,
    punitiveMultiplierOverride
  )

  // Step 4: Calculate total damages with adjustments
  const damagesCalculation = calculateTotalDamages(
    economicDamages,
    nonEconomicDamages,
    punitiveDamagesData,
    extractedData.liabilityDistribution
  )

  return {
    economicDamages,
    nonEconomicDamages,
    punitiveDamagesData,
    damagesCalculation
  }
}
