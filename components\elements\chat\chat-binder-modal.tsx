'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { toast } from '@/components/ui/use-toast'
import {
  fetchLastBinder,
  listAllBinders,
  updateResearchBinder
} from '@/lib/actions/binder'
import { Binder } from '@prisma/client'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { NewBinderForm } from '../binder/new-binder-form'

export function ChatBinderAssignModal({ researchId }: { researchId: string }) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="rounded-full px-4 w-fit" size="xs">
          Assign to Case
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <ChatBinderAssignForm researchId={researchId} />
      </DialogContent>
    </Dialog>
  )
}

function ChatBinderAssignForm({ researchId }: { researchId: string }) {
  const [binders, setBinders] = useState<Binder[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedBinder, setSelectedBinder] = useState<string>('')
  const router = useRouter()

  useEffect(() => {
    async function fetchBinders() {
      const fetchBinders = await listAllBinders()
      if (fetchBinders && fetchBinders.length > 0) {
        setBinders(fetchBinders)
        setSelectedBinder(fetchBinders[0].id)
      } else {
        router.push('/dashboard/binder')
      }
    }
    fetchBinders()
  }, [])

  async function handleSubmit() {
    try {
      await updateResearchBinder({
        researchId,
        binderId: selectedBinder
      })

      toast({
        title: 'Case assigned'
      })
      router.refresh()
    } catch (error: any) {
      toast({
        title: 'Error assigning case',
        description: error.message || 'An error occurred',
        variant: 'destructive'
      })
    }
  }

  async function handleNewBinder() {
    setIsLoading(true)
    try {
      const binder = await fetchLastBinder()
      if (binder) {
        setBinders([...binders, binder])
        setSelectedBinder(binder.id)
        handleSubmit()
      }
    } catch (error) {
      console.error('Error creating case:', error)
      toast({
        title: 'Failed to create case',
        description: 'An error occurred while creating the case',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <DialogHeader>
        <DialogTitle>Select Case</DialogTitle>
        <DialogDescription>
          Assign this conversation to a case
        </DialogDescription>
      </DialogHeader>
      <div className="flex justify-between items-center">
        <Select
          value={selectedBinder}
          onValueChange={(value) => setSelectedBinder(value)}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select a case" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Cases</SelectLabel>
              {binders.map((binder) => (
                <SelectItem key={binder.id} value={binder.id}>
                  {binder.name}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
        <Button onClick={handleSubmit} disabled={!selectedBinder}>
          Save changes
        </Button>
      </div>
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs">
          <span className="bg-white dark:bg-slate-950 px-2 text-muted-foreground">
            Or create new case
          </span>
        </div>
      </div>
      <NewBinderForm onSuccess={handleNewBinder} />
    </>
  )
}
