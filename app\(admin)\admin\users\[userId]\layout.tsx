import { notFound } from 'next/navigation'
import { db } from '@/lib/db'

export interface AdminMasqueradeProps {
  params: { userId: string }
}

interface AdminCheckUserLayoutProps {
  params: { userId: string }
  children?: React.ReactNode
}

export default async function DashboardLayout({
  params,
  children
}: AdminCheckUserLayoutProps) {
  const userData = await db.user.findUnique({
    where: {
      id: params.userId
    }
  })

  if (!(userData && userData.teamId)) {
    notFound()
  }
  return <>{children}</>
}
