'use client'

import * as React from 'react'
import { useRouter } from 'next/navigation'
import { zodResolver } from '@hookform/resolvers/zod'
import TextareaAutosize from 'react-textarea-autosize'
import { useForm } from 'react-hook-form'
import * as z from 'zod'

import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { Icons } from '@/components/elements/icons'
import { caseDataMetadataSchema } from '@/lib/validations/query'
import { CaseData } from '@/types/document'
import { updateDocumentRecordMeta } from '@/lib/actions/admin'

interface MetadataFormProps {
  documentId: number
  metadata: CaseData
}

type FormData = z.infer<typeof caseDataMetadataSchema>

export function MetadataForm({ documentId, metadata }: MetadataFormProps) {
  const router = useRouter()
  const {
    handleSubmit,
    register,
    formState: { errors },
    reset
  } = useForm<FormData>({
    resolver: zodResolver(caseDataMetadataSchema),
    defaultValues: {
      title: metadata.title,
      case: metadata.case,
      court: metadata.court,
      judges: metadata.judges.join(', '),
      parties: metadata.parties.join(', '),
      citation: metadata.citation,
      date: metadata.date?.toString(),
      year: metadata.year,
      headnotes: metadata.headnotes,
      summary: metadata.summary
    }
  })
  const [isSaving, setIsSaving] = React.useState<boolean>(false)

  React.useEffect(() => {
    reset({
      title: metadata.title,
      case: metadata.case,
      court: metadata.court,
      judges: metadata.judges.join(', '),
      parties: metadata.parties.join(', '),
      citation: metadata.citation,
      date: metadata.date?.toString(),
      year: metadata.year,
      headnotes: metadata.headnotes,
      summary: metadata.summary
    })
  }, [metadata, reset])

  async function onSubmit(data: FormData) {
    setIsSaving(true)
    const proppedData = {
      ...data,
      judges: data.judges?.split(',').map((judge) => judge.trim()),
      parties: data.parties?.split(',').map((party) => party.trim())
    }
    const store = await updateDocumentRecordMeta(
      documentId,
      JSON.stringify(proppedData)
    )
    setIsSaving(false)

    if (typeof store === 'string') {
      return toast({
        title: 'Something went wrong.',
        description: store || 'Metadata was not updated. Please try again.',
        variant: 'destructive'
      })
    }

    toast({
      description: 'Metadata has been updated.'
    })

    router.refresh()
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card>
        <CardHeader>
          <CardTitle>Edit Metadata</CardTitle>
          <CardDescription>
            Update the metadata for this document.
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-2">
          <div className="grid grid-cols-8 gap-1 items-center">
            <Label className="col-span-2" htmlFor="title">
              Title:
            </Label>
            <div className="col-span-6">
              <Input
                id="title"
                className="w-full"
                size={32}
                {...register('title')}
              />
              {errors?.title && (
                <p className="px-1 text-xs text-red-600">
                  {errors.title.message}
                </p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-8 gap-1 items-center">
            <Label className="col-span-2" htmlFor="case">
              Case:
            </Label>
            <div className="col-span-6">
              <Input
                id="case"
                className="w-full"
                size={32}
                {...register('case')}
              />
              {errors?.case && (
                <p className="px-1 text-xs text-red-600">
                  {errors.case.message}
                </p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-8 gap-1 items-center">
            <Label className="col-span-2" htmlFor="court">
              Court:
            </Label>
            <div className="col-span-6">
              <Input
                id="court"
                className="w-full"
                size={32}
                {...register('court')}
              />
              {errors?.court && (
                <p className="px-1 text-xs text-red-600">
                  {errors.court.message}
                </p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-8 gap-1 items-center">
            <Label className="col-span-2" htmlFor="judges">
              Judges:
            </Label>
            <div className="col-span-6">
              <Input
                id="judges"
                className="w-full"
                size={32}
                {...register('judges')}
              />
              {errors?.judges && (
                <p className="px-1 text-xs text-red-600">
                  {errors.judges.message}
                </p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-8 gap-1 items-center">
            <Label className="col-span-2" htmlFor="parties">
              Parties:
            </Label>
            <div className="col-span-6">
              <Input
                id="parties"
                className="w-full"
                size={32}
                {...register('parties')}
              />
              {errors?.parties && (
                <p className="px-1 text-xs text-red-600">
                  {errors.parties.message}
                </p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-8 gap-1 items-center">
            <Label className="col-span-2" htmlFor="citation">
              Citation:
            </Label>
            <div className="col-span-6">
              <Input
                id="citation"
                className="w-full"
                size={32}
                {...register('citation')}
              />
              {errors?.citation && (
                <p className="px-1 text-xs text-red-600">
                  {errors.citation.message}
                </p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-8 gap-1 items-center">
            <Label className="col-span-2" htmlFor="date">
              Date:
            </Label>
            <div className="col-span-6">
              <Input
                id="date"
                className="w-full"
                size={32}
                {...register('date')}
              />
              {errors?.date && (
                <p className="px-1 text-xs text-red-600">
                  {errors.date.message}
                </p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-8 gap-1 items-center">
            <Label className="col-span-2" htmlFor="year">
              Year:
            </Label>
            <div className="col-span-6">
              <Input
                id="year"
                className="w-full"
                size={32}
                {...register('year')}
              />
              {errors?.year && (
                <p className="px-1 text-xs text-red-600">
                  {errors.year.message}
                </p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-8 gap-1 items-center">
            <Label className="col-span-2" htmlFor="headnotes">
              Headnotes:
            </Label>
            <div className="col-span-6">
              <Input
                id="headnotes"
                className="w-full"
                size={32}
                {...register('headnotes')}
              />
              {errors?.headnotes && (
                <p className="px-1 text-xs text-red-600">
                  {errors.headnotes.message}
                </p>
              )}
            </div>
          </div>
          <div>
            <Label htmlFor="summary">Summary:</Label>
            <div className="mt-2">
              <TextareaAutosize
                autoFocus
                id="summary"
                placeholder="Summary"
                className="w-full border-2 rounded-lg p-2"
                {...register('summary')}
              />
              {errors?.summary && (
                <p className="px-1 text-xs text-red-600">
                  {errors.summary.message}
                </p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <button
            type="submit"
            disabled={isSaving}
            className={cn(buttonVariants({ variant: 'default' }), 'w-full')}
          >
            {isSaving && (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            )}
            <span>Update</span>
          </button>
        </CardFooter>
      </Card>
    </form>
  )
}
