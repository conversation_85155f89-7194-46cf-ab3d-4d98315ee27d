import { env } from '@/env.mjs'

/**
 * Send notification to any public slack channel in the organisation using custom app with auth token.
 * @param {string} channel - Name or ID of channel to which message is to be sent.
 * @param {string} textMsg - Message that needs to be sent. Refer doc for formatting: https://api.slack.com/reference/surfaces/formatting
 * @returns {null}
 */
export const sendMsgOnSlack = async ({
  channel = 'smartcounsel-dev',
  textMsg
}: {
  channel?: string
  textMsg: string
}) => {
  try {
    const url = 'https://slack.com/api/chat.postMessage'
    const payload = {
      channel: channel,
      text: textMsg
    }
    await fetch(url, {
      method: 'POST',
      body: JSON.stringify(payload),
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        authorization: `Bearer ${env.SLACK_TOKEN}`
      }
    })
  } catch (error) {
    console.error(error)
  }
}
