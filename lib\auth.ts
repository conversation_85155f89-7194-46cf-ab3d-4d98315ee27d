import { PrismaAdapter } from '@next-auth/prisma-adapter'
import { NextAuthOptions, Session } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'

import { env } from '@/env.mjs'
import { db } from '@/lib/db'
import {
  createNewTeamRecord,
  validateAndCreateTrialCreditOnFeature,
  findTeamPeriodicCreditOnFeature,
  findTeamPeriodicCreditHistoryOnFeature,
  findTeamFromId
} from './recordstore-team'
import { CreditType, type User } from '@prisma/client'
import {
  createDefaultUserSettings,
  findUserById,
  validateUser
} from './recordstore-user'
import { sendMsgOnSlack } from './services/slack-service'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db),
  pages: {
    signIn: '/login',
    newUser: '/welcome'
  },
  session: {
    strategy: 'jwt'
  },
  debug: false,
  providers: [
    GoogleProvider({
      clientId: env.GOOGLE_ID,
      clientSecret: env.GOOGLE_SECRET
    }),
    CredentialsProvider({
      id: 'credentials',
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials, req) {
        if (!credentials || !credentials.email || !credentials.password) {
          return null
        }
        const user = await validateUser({
          email: credentials.email,
          password: credentials.password
        })

        if (user === 'User not found' || user === 'Invalid password') {
          throw new Error(user)
        }

        return user
      }
    })
  ],
  callbacks: {
    async session({ session, token, user }) {
      const userData = (user || token) as User
      if (!userData) {
        return session
      }
      const sessionData = await generateSessionData(userData, session)
      return sessionData
    },
    async jwt({ token, user }) {
      const dbUser = await db.user.findFirst({
        where: {
          email: token.email
        }
      })

      if (!dbUser) {
        if (user) {
          token.id = user?.id
        }
        return token
      }

      return {
        ...token,
        id: dbUser.id,
        name: dbUser.name,
        email: dbUser.email,
        picture: dbUser.image,
        teamId: dbUser.teamId
      }
    }
  },
  events: {
    async signIn({ user, isNewUser }) {
      if (isNewUser) {
        await sendMsgOnSlack({
          channel: `smartcounsel-signups`,
          textMsg: `New user signed up:
          Name: ${user.name}
          Email: ${user.email}`
        })
      }
    }
  }
}

async function generateSessionData(userAuth: User, session: Session) {
  const user = await findUserById(userAuth.id)
  if (!user) {
    return session
  }

  session.user.region = user.region

  if (!user.teamId) {
    const team = await createNewTeamRecord(
      user.name || user.email || '_unknown',
      user.id
    )
    session.user.teamId = team?.id || 'unknown'
    session.user.plan = team?.plan || 'trial'
  } else {
    const team = await findTeamFromId(user.teamId)
    session.user.teamId = user.teamId
    session.user.plan = team!.plan
  }

  if (user.userSettings) {
    session.user.settings = {
      questionAssessment: user.userSettings.questionAssessment
    }
  } else {
    const store = await createDefaultUserSettings({ userId: user.id })
    session.user.settings = {
      questionAssessment: store?.questionAssessment || true
    }
  }

  session.user.id = user.id
  session.user.userType = user.userType
  session.usage = {}

  await validateAndCreateTrialCreditOnFeature({
    teamId: session.user.teamId,
    type: CreditType.research
  })

  const researchCreditsAvailablePromise = findTeamPeriodicCreditOnFeature({
    teamId: session.user.teamId,
    type: CreditType.research
  })

  const caseCreditsAvailablePromise = findTeamPeriodicCreditOnFeature({
    teamId: session.user.teamId,
    type: CreditType.case
  })

  const [researchCreditsAvailable, caseCreditsAvailable] = await Promise.all([
    researchCreditsAvailablePromise,
    caseCreditsAvailablePromise
  ])

  const researchCreditsUsedPromise = findTeamPeriodicCreditHistoryOnFeature({
    teamId: session.user.teamId,
    type: CreditType.research,
    startDate: researchCreditsAvailable?.startDate
  })
  const caseCreditsUsedPromise = findTeamPeriodicCreditHistoryOnFeature({
    teamId: session.user.teamId,
    type: CreditType.case,
    startDate: researchCreditsAvailable?.startDate
  })

  const [researchCreditsUsed, caseCreditsUsed] = await Promise.all([
    researchCreditsUsedPromise,
    caseCreditsUsedPromise
  ])

  session.usage!.research = {
    available: researchCreditsAvailable?.credit || 0,
    used: researchCreditsUsed || 0
  }

  session.usage!.case = {
    available: caseCreditsAvailable?.credit || 0,
    used: caseCreditsUsed || 0
  }

  return session
}
