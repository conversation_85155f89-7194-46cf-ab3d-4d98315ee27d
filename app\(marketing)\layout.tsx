import Link from 'next/link'

import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import { MainNav } from '@/components/elements/layout/main-nav'
import { SiteFooter } from '@/components/elements/layout/site-footer'
import { marketingConfig } from '@/config/marketing'
import { getCurrentUser } from '@/lib/session'
import { UserAccountNav } from '@/components/elements/user/user-account-nav'
import { ZohoSalesIQ } from '@/components/external/zoho-salesiq'

interface MarketingLayoutProps {
  children: React.ReactNode
}

export default async function MarketingLayout({
  children
}: MarketingLayoutProps) {
  const user = await getCurrentUser()

  return (
    <div className="flex min-h-screen flex-col">
      <header className="container z-40 bg-background">
        <div className="flex h-20 items-center justify-between py-6">
          <MainNav items={marketingConfig.mainNav} />
          {user ? (
            <UserAccountNav user={user} />
          ) : (
            <nav>
              <Link
                href="/login"
                className={cn(
                  buttonVariants({ variant: 'secondary', size: 'sm' }),
                  'px-4'
                )}
              >
                Login
              </Link>
            </nav>
          )}
        </div>
      </header>
      <main className="flex-1">{children}</main>
      <SiteFooter items={marketingConfig.footerNav} />
      <ZohoSalesIQ />
    </div>
  )
}
