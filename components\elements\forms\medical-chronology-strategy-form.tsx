'use client'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { useEffect, useImperativeHandle, forwardRef } from 'react'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'

const medicalChronologyStrategyFormSchema = z.object({
  treatmentTimelineGuidelines: z.string().optional(),
  prospectiveCareGuidelines: z.string().optional()
})

export type MedicalChronologyStrategyFormData = z.infer<
  typeof medicalChronologyStrategyFormSchema
>

interface MedicalChronologyStrategyFormProps {
  caseId: string
  defaultValues?: Partial<MedicalChronologyStrategyFormData> | null
  setValues: (values: Partial<MedicalChronologyStrategyFormData>) => void
}

export interface MedicalChronologyStrategyFormRef {
  submitForm: () => Promise<boolean>
}

export const MedicalChronologyStrategyForm = forwardRef<
  MedicalChronologyStrategyFormRef,
  MedicalChronologyStrategyFormProps
>(({ caseId, defaultValues, setValues }, ref) => {
  const form = useForm<MedicalChronologyStrategyFormData>({
    resolver: zodResolver(medicalChronologyStrategyFormSchema),
    defaultValues: defaultValues || {
      treatmentTimelineGuidelines: '',
      prospectiveCareGuidelines: ''
    }
  })

  // Watch form values and auto-save as user types
  const watchedValues = form.watch()

  useEffect(() => {
    // Auto-save form values when they change
    const subscription = form.watch((value) => {
      setValues(value)
    })
    return () => subscription.unsubscribe()
  }, [form, setValues])

  function onSubmit(data: MedicalChronologyStrategyFormData) {
    setValues(data)
    return Promise.resolve(true)
  }

  // Expose submitForm function to parent component
  useImperativeHandle(ref, () => ({
    submitForm: async () => {
      const isValid = await form.trigger()
      if (isValid) {
        const values = form.getValues()
        setValues(values)
        return true
      }
      return false
    }
  }))

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Medical Chronology Strategy</CardTitle>
        <CardDescription>
          Provide specific guidelines for how the medical chronology should be
          structured and focused.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="treatmentTimelineGuidelines"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Part A: Treatment Timeline Guidelines</FormLabel>
                  <FormControl>
                    <Textarea
                      className="resize-none min-h-[120px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    E.g., Include all visits to [Provider Name], focus on
                    treatment for [Injury/Condition], or limit to care from
                    [Start Date] to [End Date].
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="prospectiveCareGuidelines"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Part B: Prospective Care & Follow-Up Guidelines
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      className="resize-none min-h-[120px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    E.g., Highlight follow-up care for [Injury/Condition],
                    future plans by [Specialist], or ongoing therapy and
                    recommended procedures.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  )
})

MedicalChronologyStrategyForm.displayName = 'MedicalChronologyStrategyForm'
