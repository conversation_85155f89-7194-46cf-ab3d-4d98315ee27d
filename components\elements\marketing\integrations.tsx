import Image from 'next/image'
import { cn } from '@/lib/utils'

export function Integrations() {
  const cloudStorageTools = [
    {
      name: 'Google Drive',
      logo: '/marketing/logos/brands/google-drive.png',
      invert: false
    },
    {
      name: 'SharePoint',
      logo: '/marketing/logos/brands/sharepoint.png',
      invert: false
    },
    {
      name: 'OneDrive',
      logo: '/marketing/logos/brands/onedrive.png',
      invert: false
    },
    {
      name: 'Dropbox',
      logo: '/marketing/logos/brands/dropbox.png',
      invert: false
    },
    { name: 'Box', logo: '/marketing/logos/brands/box.png', invert: false },
    {
      name: '<PERSON>oter<PERSON>',
      logo: '/marketing/logos/brands/zotero.png',
      invert: false
    }
  ]

  // Tweak angles to spread out the icons.
  // With 6 icons, angles from -80° to 80° can make a broader arc.
  const cloudStorageAngles = [330, 210, 175, 140, 10, 40]

  const emailTools = [
    { name: 'Gmail', logo: '/marketing/logos/brands/gmail.png', invert: false },
    {
      name: 'Outlook',
      logo: '/marketing/logos/brands/outlook.png',
      invert: false
    },
    { name: 'Slack', logo: '/marketing/logos/brands/slack.png', invert: false }
  ]

  // 3 angles for 3 icons
  const emailAngles = [-45, 0, 45]

  return (
    <div className="mx-auto max-w-[58rem] space-y-16">
      {/* Top Header */}
      <div className="flex flex-col items-center space-y-4 text-center">
        <h2 className="font-heading text-3xl leading-[1.2] sm:text-4xl md:text-5xl">
          Seamlessly integrate with your favorite tools
        </h2>
        <p className="max-w-[55ch] text-muted-foreground sm:text-lg">
          Connect SmartCounsel with the tools you already use and love, and work
          the way you prefer.
        </p>
      </div>

      {/* Cloud Storage: Increase the height and radius */}
      <div className="relative hidden md:flex items-center justify-center h-[500px]">
        {/* Heading in center */}
        <h3 className="z-10 font-heading text-2xl md:text-3xl">
          Cloud Storage
        </h3>

        {/* Icons in a semicircle */}
        {cloudStorageTools.map((tool, index) => {
          const angle = cloudStorageAngles[index] || 0
          const transform = `rotate(${angle}deg) translate(18rem) rotate(-${angle}deg)`

          return (
            <div
              key={tool.name}
              className="absolute flex flex-col items-center border rounded-md bg-slate-200 w-56 h-16"
              style={{
                transform: transform
              }}
            >
              <Image
                src={tool.logo}
                alt={tool.name}
                width={200}
                height={50}
                className={cn(
                  'aspect-auto h-16 w-auto',
                  tool.invert && 'dark:invert'
                )}
              />
            </div>
          )
        })}
      </div>

      {/* Email & Communication: Same approach */}
      <div className="space-y-4">
        <h3 className="font-heading text-center text-2xl md:text-3xl py-10">
          Email &amp; Communication
        </h3>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          {emailTools.map((tool, i) => (
            <div
              key={i}
              className="flex flex-col items-center justify-center p-4 border rounded-md bg-slate-100"
            >
              <Image
                src={tool.logo}
                alt={tool.name}
                width={200}
                height={200}
                className={cn(
                  'aspect-auto h-auto w-auto',
                  tool.invert && 'dark:invert'
                )}
              />
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="font-heading text-center text-2xl md:text-3xl py-10">
          Top Security Certifications
        </h3>
        <div className="grid gap-2 grid-cols-2">
          <Image
            src="/marketing/hipaa_blue-3.webp"
            alt="hipaa"
            width={180}
            height={500}
            className="m-auto mr-0 dark:invert"
          />
          <Image
            src="/marketing/soc-2.webp"
            alt="soc"
            width={100}
            height={300}
            className="m-auto ml-16"
          />
        </div>
      </div>
    </div>
  )
}
