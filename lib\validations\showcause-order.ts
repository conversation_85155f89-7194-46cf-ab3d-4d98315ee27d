import * as z from 'zod'

export const showcauseOrderPatchSchema = z.object({
  title: z.string().min(3).max(128),
  // string or null
  finalJudgementSummary: z.string().nullable()
})

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
const ACCEPTED_FILE_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/msword'
] as const

const fileSchema = z
  .instanceof(FileList)
  .refine((files) => files.length === 1, 'File is required')
  .refine((files) => files[0].size <= MAX_FILE_SIZE, `Max file size is 10MB.`)
  .refine(
    (files) => ACCEPTED_FILE_TYPES.includes(files[0].type as any),
    'Invalid file type. Only PDF, DOCX, DOC files are accepted.'
  )

export const showcauseOrderPostSchema = z.object({
  title: z.string().min(3).max(128),
  showCauseNotice: fileSchema,
  showCauseReply: fileSchema
})
