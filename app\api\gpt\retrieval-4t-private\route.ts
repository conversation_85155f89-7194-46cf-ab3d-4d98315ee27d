import { NextRequest, NextResponse } from 'next/server'
import { StreamingTextResponse } from 'ai'
import { ChatOpenAI } from '@langchain/openai'
import { PromptTemplate } from '@langchain/core/prompts'
import { <PERSON><PERSON>hain } from 'langchain/chains'
import { formatVercelMessages } from '@/lib/retriever-utils'
import { getCurrentUserResponse } from '@/lib/session'
import { GPTModel, YesNo, type ResearchStoreContent } from '@/types'
import { fetchResearchContext } from '@/lib/cerebrum/vector-processes'
import { assessQuestionQuality } from '@/lib/cerebrum/gpt-assisted-processes'

// export const runtime = 'edge'
export const dynamic = 'force-dynamic'
export const maxDuration = 120

const GPT_MODEL = GPTModel.GPT4Turbo

const ANSWER_TEMPLATE = `You are now operating under the persona of "<PERSON><PERSON>ou<PERSON><PERSON>," a highly specialized legal research bot. Your responses must adhere strictly to context and must be delivered professionally and informatively.

The following context is provided from documents uploaded by the user for your reference. Please use this context to answer the question.

Answer the question based only on the following context and chat history. Do not explicitly say "based on context", instead cite your reference. Do not respond to queries that are not provided in context, even if you know. Your response will automatically include links to relevant legal documents. Based on the context if you think additional context is required, ask for it before answering the question.
<context>
  {context}
</context>

Given the following conversation and a follow up question, use the discussion for additional context. Skip messages that are completely unrelated to the follow up question.
<chat_history>
  {chat_history}
</chat_history>

Question: {question}
`

export async function POST(req: NextRequest) {
  try {
    const url = req.nextUrl
    const queryParams = url.searchParams
    let namespace = queryParams.get('ns') ?? 'default'
    const body = await req.json()
    const user = await getCurrentUserResponse()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const messages = body.messages ?? []
    const previousMessages = messages.slice(0, -1)
    const userQuestion = messages[messages.length - 1].content
    const currentQuestion = userQuestion
    const filter: Pick<ResearchStoreContent, 'court' | 'year' | 'sources'> =
      body.metadata
    const chatHistory = formatVercelMessages(previousMessages)

    if (namespace === 'default' || namespace === '') {
      namespace = user.teamId
    }

    const model = new ChatOpenAI({
      modelName: GPT_MODEL,
      temperature: 0.2,
      streaming: true
    })

    const assessment = await assessQuestionQuality({
      messages: previousMessages,
      question: userQuestion,
      region: user.region,
      researchType: 'private',
      namespace: namespace,
      model: GPT_MODEL,
      dummy: false
    })

    const { serializedSources, context } = await fetchResearchContext({
      summarise: false,
      question: currentQuestion,
      relatedQuestions: assessment.related_searches || [],
      region: user.region,
      isPrivate: true,
      researchType: 'private',
      assessment: {
        relevance: YesNo.YES,
        sector: namespace,
        independent: YesNo.NO,
        specific_case: YesNo.NO,
        rewritten_question: currentQuestion
      },
      namespace,
      filter
    })

    const prompt = PromptTemplate.fromTemplate(ANSWER_TEMPLATE)
    const chain = new LLMChain({ llm: model, prompt })
    const textEncoder = new TextEncoder()

    const stream = new ReadableStream({
      async start(controller) {
        controller.enqueue(textEncoder.encode(''))

        await chain.call(
          {
            context,
            chat_history: chatHistory,
            question: userQuestion
          },
          [
            {
              handleLLMNewToken(token: string) {
                controller.enqueue(textEncoder.encode(token))
              }
            }
          ]
        )
        controller.close()
      }
    })

    return new StreamingTextResponse(stream, {
      headers: {
        'x-message-index': (previousMessages.length + 1).toString(),
        'x-sources': serializedSources,
        'x-namespace': namespace
      }
    })
  } catch (e: any) {
    console.log(e)
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}
