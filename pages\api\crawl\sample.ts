import type { NextApiRequest, NextApiResponse } from 'next'
import { db } from '@/lib/db'
import { env } from '@/env.mjs'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (env.NODE_ENV !== 'development') {
      throw new Error('Unauthorized')
    }

    const reseaches = await db.researchStore.findMany({
      where: {
        question: null
      }
    })

    for (let i = 0; i < reseaches.length; i++) {
      const research = reseaches[i]
      const question = (research.content as unknown as any).messages?.[0]
        ?.content
      const questionPreview =
        question && question.length > 900
          ? question.slice(0, 900) + '...'
          : question || 'Unused research'
      await db.researchStore.update({
        where: {
          id: research.id
        },
        data: {
          question: questionPreview
        }
      })
    }

    res.status(200).json({ message: 'Data saved successfully!' })
  } catch (error) {
    res.status(500).json({ error: 'Failed to index.', errorcontent: error })
  }
}
