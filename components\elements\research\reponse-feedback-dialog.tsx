import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle
} from '@/components/ui/dialog'
import { Button, buttonVariants } from '../../ui/button'
import { useState } from 'react'
import { toast } from '../../ui/use-toast'
import { useRouter } from 'next/navigation'
import { storeResponseFeedback } from '@/lib/actions/research'
import { Textarea } from '../../ui/textarea'
import { FeedbackType } from '@prisma/client'

const CreateResponseFeedbackDialog = ({
  type,
  researchId,
  messageId,
  open,
  setOpen
}: {
  type: FeedbackType
  researchId: string
  messageId: string
  open: boolean
  setOpen: (open: boolean) => void
}) => {
  const router = useRouter()

  const [feedback, setFeedback] = useState('')

  const handleGroupDocumentsSubmit = async () => {
    try {
      const responseFeedback = feedback.trim()

      if (responseFeedback === '') {
        toast({
          title: 'Feedback cannot be empty',
          description: 'Please provide a description',
          variant: 'destructive'
        })
        return
      }

      const response = await storeResponseFeedback({
        type: type,
        feedbackContent: feedback,
        researchId: researchId,
        messageId: messageId
      })
      if (response.ok) {
        toast({
          title: 'Feedback submitted successfully',
          description: 'Your response will be reviewed by the team'
        })
      } else {
        throw new Error('Failed to submit feedback')
      }
    } catch (error) {
      console.error('Error:', error)
      toast({
        title: 'Failed to submit feedback',
        description: 'An error occurred while submitting your response',
        variant: 'destructive'
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Please elaborate the issue</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-3">
          <h2>This will help in improving the quality of responses</h2>
          <Textarea
            rows={12}
            cols={15}
            placeholder="Description..."
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
            className=""
          ></Textarea>
        </div>
        <DialogFooter className="sm:justify-start">
          <DialogClose asChild>
            <Button onClick={handleGroupDocumentsSubmit} size="sm">
              Submit
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default CreateResponseFeedbackDialog
