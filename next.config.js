const { PrismaPlugin } = require('@prisma/nextjs-monorepo-workaround-plugin')

/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['pdf-parse', '@aws-sdk']
  },
  webpack: (config, { isServer }) => {
    if (isServer) {
      config.plugins = [...config.plugins, new PrismaPlugin()]
    }
    return config
  },
  images: {
    domains: [
      'edoflip-public.s3.ap-south-1.amazonaws.com',
      'landingfoliocom.imgix.net',
      'cdn.rareblocks.xyz',
      'lh3.googleusercontent.com',
      'img.icons8.com',
      'source.unsplash.com',
      'filemanager.gupshup.io'
    ]
  }
}

module.exports = nextConfig
