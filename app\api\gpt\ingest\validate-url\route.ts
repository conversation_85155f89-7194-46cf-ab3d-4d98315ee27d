import { NextRequest, NextResponse } from 'next/server'
import { UnauthorizedError } from '@/lib/exceptions'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!(session && session.user)) {
      throw new UnauthorizedError()
    }

    const body = await req.json()

    const url = body.url
    // Check if URL ends with .pdf
    if (!url.toLowerCase().endsWith('.pdf')) {
      throw new Error(`URL must end with .pdf: ${url}`)
    }

    try {
      const response = await fetch(url, { method: 'HEAD' })
      if (!response.ok) {
        throw new Error(`Unable to fetch URL: ${url}`)
      }

      const contentType = response.headers.get('Content-Type')
      if (contentType && !contentType.includes('application/pdf')) {
        throw new Error(`Content-Type is not application/pdf: ${url}`)
      }

      const contentLength = response.headers.get('Content-Length')
      const sizeInBytes = contentLength ? parseInt(contentLength) : 0
      const maxSizeInBytes = body.max_size

      if (sizeInBytes > maxSizeInBytes) {
        throw new Error(`File size exceeds 5 MB for URL: ${url}`)
      }

      // Try to get the filename from Content-Disposition header
      let fileName = 'unknown.pdf'
      const contentDisposition = response.headers.get('Content-Disposition')
      if (contentDisposition && contentDisposition.includes('filename=')) {
        const matches = contentDisposition.match(/filename="?([^"]+)"?/)
        if (matches && matches[1]) {
          fileName = matches[1]
        }
      } else {
        // Extract file name from URL if Content-Disposition is not available
        const urlObj = new URL(url)
        fileName = urlObj.pathname.split('/').pop() || 'unknown.pdf'
      }

      const validUrl = {
        url,
        name: fileName,
        size: sizeInBytes
      }

      return NextResponse.json({ ok: true, validUrl }, { status: 200 })
    } catch (error) {
      console.error(`Error validating URL: ${url}`, error)
      throw new Error(`Error validating URL: ${url}`)
    }
  } catch (e: any) {
    console.log(e)
    return NextResponse.json({ error: e.message }, { status: 500 })
  }
}
