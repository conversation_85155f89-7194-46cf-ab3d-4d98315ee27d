'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Home } from 'lucide-react'

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb'

interface BreadcrumbNavProps {
  currentPageTitle?: string
}

export function BreadcrumbNav({ currentPageTitle }: BreadcrumbNavProps) {
  const pathname = usePathname()

  // Skip rendering breadcrumbs on the home page
  if (pathname === '/') {
    return null
  }

  // Split the pathname into segments
  const segments = (pathname || '').split('/').filter(Boolean)

  // Format segment names for display (capitalize, replace hyphens)
  const formatSegmentName = (
    segment: string,
    index: number,
    isLastSegment: boolean
  ): string => {
    // Check if it's a dynamic route segment (includes Next.js route params, CUIDs, UUIDs or integers)
    const isDynamicSegment =
      /^\[.*\]$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$|^[a-z0-9]{25,}$|^[0-9]+$/.test(
        segment
      )

    // If it's the last segment and we have a title, use that
    if (isLastSegment && currentPageTitle) {
      return currentPageTitle
    }

    // If it's a dynamic segment
    if (isDynamicSegment) {
      // Get the previous segment name to create "Selected X" format
      const previousSegment =
        index > 0
          ? formatSegmentName(segments[index - 1], index - 1, false)
          : 'Item'

      return `Selected ${previousSegment}`
    }

    // Regular segment formatting
    return segment
      .replace(/-/g, ' ')
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  return (
    <Breadcrumb className="px-2 py-1">
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link href="/">
              <Home className="h-4 w-4" />
              <span className="sr-only">Home</span>
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />

        {segments.map((segment, index) => {
          // Create the URL up to this segment
          const href = `/${segments.slice(0, index + 1).join('/')}`
          const isLastSegment = index === segments.length - 1
          const displayName = formatSegmentName(segment, index, isLastSegment)

          return (
            <React.Fragment key={segment}>
              <BreadcrumbItem>
                {isLastSegment ? (
                  <BreadcrumbPage>{displayName}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link href={href}>{displayName}</Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {!isLastSegment && <BreadcrumbSeparator />}
            </React.Fragment>
          )
        })}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
