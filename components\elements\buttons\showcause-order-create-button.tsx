'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { ShowCauseOrderGeneratorForm } from '../showcause-order-generator-form'

export function ShowCauseOrderCreateButton() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">Create New</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Generate Show Cause Order</DialogTitle>
          <DialogDescription>
            Fill in the details below to generate a Show Cause Order.
          </DialogDescription>
        </DialogHeader>
        <ShowCauseOrderGeneratorForm />
      </DialogContent>
    </Dialog>
  )
}
